
import { client } from './client';
const { hostname, pathname } = window.location;
const baseHost = hostname.includes('localhost') ? 'http://localhost:5000' : ``;
let baseApiPath = '/sidekick';
const apiPathPrefix = pathname.split(baseApiPath)[0];
baseApiPath = `${apiPathPrefix}${baseApiPath}/api/users`;
const baseRoute = `${baseHost}${baseApiPath}`;
export interface GetUserResponse {
  user: string | null;
  roles: string[];
}
export const getCurrentUser = async () => {
  const response = await client.get<GetUserResponse>(`${baseRoute}/me`);
  return response.data;
};
