import React from 'react';
// import { IoMicOutline } from 'react-icons/io5';
import { FaArrowRight } from 'react-icons/fa';
import { useThemeStyles } from '@/hooks/useThemeStyles';
import { InputLowerTrayProps } from '@features/workbook/workbookTypes';

const InputLowerTray: React.FC<InputLowerTrayProps> = ({ onSubmit }) => {
  const { classes } = useThemeStyles();

  const styles = {
    container: "flex w-full items-center justify-end absolute bottom-3 right-6",
    buttonGroup: "flex items-center gap-[12px]",
    iconButton: classes.circleIconButton,
    icon: "h-5 w-5"
  };

  return (
    <div className={styles.container}>
      <div className={styles.buttonGroup}>
        {/* <button className={styles.iconButton} aria-label="Speak" tabIndex={0}>
          <IoMicOutline className={styles.icon} />
        </button> */}

        <button
          className={`${styles.iconButton} font-roboto`}
          onClick={onSubmit}
          aria-label="Send message"
          tabIndex={0}
        >
          <FaArrowRight className={styles.icon} />
        </button>
      </div>
    </div>
  );
};

export default InputLowerTray;
