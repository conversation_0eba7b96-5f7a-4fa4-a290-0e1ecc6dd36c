import { useState } from 'react';


interface CopyActionState {
  copied: boolean;
}

interface UseCopyActionResult {
  state: CopyActionState; 
  handleCopy: (e: React.MouseEvent) => void;
}

export const useCopyAction = (messageText: string): UseCopyActionResult => {
  const [copied, setCopied] = useState(false);


  const handleCopy = (e: React.MouseEvent) => {
    e.stopPropagation();
    navigator.clipboard.writeText(messageText);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000); 
  };

  return {
    state: {
      copied,
    },
    handleCopy,
  };
};
