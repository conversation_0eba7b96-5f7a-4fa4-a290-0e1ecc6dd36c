"""Keeping this helpers in views_workflow as this will just be creating HTML elements for frontend"""

from flask_wtf import FlaskForm
from wtforms import RadioField, SelectField, StringField, TextAreaField
from wtforms.validators import DataRequired

HM_LOCATIONS = [
    ("All Highmark", "All Highmark"),
    ("Highmark Health Plans", "Highmark Health Plans"),
    ("enGen", "enGen"),
    ("HM Insurance Group", "HM Insurance Group"),
    ("HM Home & Community Services", "HM Home & Community Services"),
    ("Highmark Wholecare", "Highmark Wholecare"),
    ("Thryve Digital", "Thryve Digital"),
    ("United Concordia Dental", "United Concordia Dental"),
]

AHN_LOCATIONS = [
    ("All AHN", "All AHN"),
    ("Allegheny Health Network", "Allegheny Health Network"),
    ("Allegheny Clinic", "Allegheny Clinic"),
    ("Allegheny General Hospital", "Allegheny General Hospital"),
    ("Allegheny Valley Hospital", "Allegheny Valley Hospital"),
    ("Canonsburg Hospital", "Canonsburg Hospital"),
    ("Ambulatory Surgery Centers", "Ambulatory Surgery Centers"),
    ("Forbes Hospital", "Forbes Hospital"),
    ("Grove City Hospital", "Grove City Hospital"),
    ("Jefferson Hospital", "Jefferson Hospital"),
    ("Rural Health Clinic", "Rural Health Clinic"),
    ("Saint Vincent Hospital", "Saint Vincent Hospital"),
    ("Saint Vincent Endoscopy Center", "Saint Vincent Endoscopy Center"),
    ("Saint Vincent Surgery Center", "Saint Vincent Surgery Center"),
    ("Westfield Memorial Hospital", "Westfield Memorial Hospital"),
    ("West Penn Hospital", "West Penn Hospital"),
    ("Wexford Hospital", "Wexford Hospital"),
]


class ChatForm(FlaskForm):

    # text_area = TextAreaField('Your Text', validators=[DataRequired()])

    select_area = SelectField(
        "select_org",
        choices={
            "Select an Organization": [("All", "Select an Organization (All)")],
            "Highmark Health": HM_LOCATIONS,
            "Allegheny Health Network": AHN_LOCATIONS,
        },
    )

    temp = RadioField(
        "Temperature",
        choices=[
            ("0", "Precise"),
            ("1", "Balanced"),
            ("2", "Creative"),
        ],
        validators=[DataRequired()],
        default="1",
    )
