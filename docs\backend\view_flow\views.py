import os, traceback, html, json

from flask import Blueprint, make_response, render_template, request, session

from lib.firestore_client import FirestoreClient
from services.SessionService import SessionService
from website.authorization import authorization_required
from website.logger_framework import make_logger
from website.views_workflow.utils import helpers

workflow = "views_workflow"

logger_info, logger_error = make_logger(workflow, __file__)

views_print = Blueprint("views_process", __name__)

@views_print.route("/", methods=["GET"])
@views_print.route("/chat", methods=["GET"])
def multimodal_main():
    """Similar to the ones found in views_workflow/views.py"""
    try:
        return make_response(
            render_template("views/multimodal.html", user_email=session["email"])
        )

    except Exception as e:
        logger_error.error(
            f"{workflow} : multimodalbot : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, frontend render failure"}

@views_print.route("/chat/<session_id>/resume", methods=["GET"])
def multimodal_resume(session_id):
    try:
        user_email:str = session["email"]
        session_client = FirestoreClient().InstanceClient(user_email)
        session_service = SessionService(persistence_client=session_client)
        session_document = session_service.get_user_session_by_id(
            session_id, user_email, include_messages=True
        )
        if not session_document or session_document.user_email.lower() != user_email.lower():
            return make_response(
                render_template("views/multimodal.html", user_email=session["email"])
            )
    
        for message in session_document.conversation_history:
            message.message = html.escape(message.message)
        document_json = json.dumps(session_document.to_dict(date_format_iso=True, include_document_id=True))

        return make_response(
            render_template("views/multimodal.html", user_email=session["email"], session_document=document_json)
        )
    except Exception as e:
        logger_error.error(
            f"{workflow} : multimodalbot : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, frontend render failure"}


@views_print.route("/code", methods=["GET", "POST"])
# @authorization_required
def code_main():
    try:
        return code_chat_frontend_response(
            request,
            {
                "session_name": "code_session",
                "template_name_or_list": "views/code.html",
            },
        )

    except Exception as e:
        logger_error.error(
            f"{workflow} : code_main : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, frontend render failure"}

@views_print.route("/code/<session_id>/resume", methods=["GET"])
def code_resume(session_id):
    try:
        user_email = session["email"]
        code_session_data = {
            "session_name": "code_session",
            "template_name_or_list": "views/code.html",
        }

        session_client = FirestoreClient().InstanceClient(user_email)
        session_service = SessionService(persistence_client=session_client)
        session_document = session_service.get_user_session_by_id(
            session_id, user_email, include_messages=True
        )        
        if not session_document or session_document.user_email.lower() != user_email.lower():
            return code_chat_frontend_response(request, code_session_data)
    
        for message in session_document.conversation_history:
            message.message = html.escape(message.message)
        document_json = json.dumps(session_document.to_dict(date_format_iso=True, include_document_id=True))

        code_session_data["session_document"] = document_json
        return code_chat_frontend_response(request, code_session_data)
    except Exception as e:
        logger_error.error(
            f"{workflow} : code_main : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, frontend render failure"}

@views_print.route("/med", methods=["GET", "POST"])
# @authorization_required
def medlm_main():
    try:
        return code_chat_frontend_response(
            request,
            {
                "session_name": "mdlm_session",
                "template_name_or_list": "views/medlm.html",
            },
        )
    except Exception as e:
        logger_error.error(
            f"{workflow} : medlm_main : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, frontend render failure"}

@views_print.route("/med/<session_id>/resume", methods=["GET", "POST"])
# @authorization_required
def medlm_resume(session_id):
    try:
        user_email = session["email"]
        medlm_session_data = {
            "session_name": "mdlm_session",
            "template_name_or_list": "views/medlm.html",
        }

        session_client = FirestoreClient().InstanceClient(user_email)
        session_service = SessionService(persistence_client=session_client)
        session_document = session_service.get_user_session_by_id(
            session_id, user_email, include_messages=True
        )        
        if not session_document or session_document.user_email.lower() != user_email.lower():
            return code_chat_frontend_response(request, medlm_session_data)
    
        for message in session_document.conversation_history:
            message.message = html.escape(message.message)
        document_json = json.dumps(session_document.to_dict(date_format_iso=True, include_document_id=True))

        medlm_session_data["session_document"] = document_json
        return code_chat_frontend_response(request, medlm_session_data)
    except Exception as e:
        logger_error.error(
            f"{workflow} : medlm_main : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, frontend render failure"}

@views_print.route("/translate")
def translate():
    try:
        return render_template("views/translate.html", user_email=session["email"])

    except Exception as e:
        logger_error.error(
            f"{workflow} : resources : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, frontend render failure"}

@views_print.route("/texttospeech")
def textToSpeech():
    try:
        return render_template("views/text_to_speech.html", user_email=session["email"])

    except Exception as e:
        logger_error.error(
            f"{workflow} : resources : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, check logs for more info"}
    

@views_print.route("/policyweb", methods=["GET", "POST"])
# @authorization_required
def policyweb():
    try:
        return code_chat_frontend_response(
            request,
            {
                "session_name": "policy_session",
                "template_name_or_list": "views/policyweb.html",
                "form": helpers.ChatForm(),
            },
        )

    except Exception as e:
        logger_error.error(
            f"{workflow} : code_main : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, frontend render failure"}


@views_print.route("/resources")
# @authorization_required
def resources():
    try:
        return render_template("views/resources.html")

    except Exception as e:
        logger_error.error(
            f"{workflow} : resources : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, frontend render failure"}


@views_print.route("/history", methods=["GET", "POST"])
# @authorization_required
def serve_menu_history():
    try:
        return history_frontend_response(
            {"template_name_or_list": "views/menu_history.html"}
        )

    except Exception as e:
        logger_error.error(
            f"{workflow} : serve_menu_history : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, frontend render failure"}


@views_print.route("/history/<id>", methods=["GET", "POST"])
# @authorization_required
def serve_chat_history(id):
    try:
        # session_list = show_single_session(id)
        # json_list = jsonify(session_list)
        return history_frontend_response(
            {"id": id, "template_name_or_list": "views/history.html"}
        )

    except Exception as e:
        logger_error.error(
            f"{workflow} : serve_chat_history : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, frontend render failure"}


def history_frontend_response(data):
    """Common function for history response"""
    try:
        response = make_response(
            render_template(
                **data,
                deployment=os.environ.get("DEPLOYMENT", ""),
                user_email=session["email"],
            )
        )

        return response

    except Exception as e:
        logger_error.error(
            f"{workflow} : history_frontend_response : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, frontend render failure"}


def code_chat_frontend_response(request, data: dict):
    """Common function to chat and code chat response"""
    try:
        response = make_response(
            render_template(
                data["template_name_or_list"],
                deployment=os.environ.get("DEPLOYMENT", ""),
                user_email=session["email"],
                form=data.get("form", None),
                session_document=data.get("session_document", None)
            )
        )

        if request.method == "GET":
            session.pop(data["session_name"], None)

        return response

    except Exception as e:
        logger_error.error(
            f"{workflow} : code_chat_frontend_response : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, frontend render failure"}
