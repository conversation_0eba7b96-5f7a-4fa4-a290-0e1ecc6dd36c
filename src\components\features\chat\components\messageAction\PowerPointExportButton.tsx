import React, { useState, useEffect, useRef } from 'react';
import ReactDOM from 'react-dom';
import { TbFileTypePpt } from 'react-icons/tb';
import { AiOutlineLoading } from 'react-icons/ai';
import { IoMdArrowDropdown } from 'react-icons/io';
import clsx from 'clsx';
import { usePowerPointExport } from './hooks/usePowerPointExport';
import {
  PowerPointOption,
  DropdownPositionState,
  POWERPOINT_OPTIONS,
  EXPORT_BUTTON_STYLES,
  POWERPOINT_DROPDOWN_STYLES,
  calculateDropdownPosition,
  getExportButtonText,
  getExportIconStyle,
} from './utils/messageActionUtils';

interface PowerPointExportButtonProps {
  messageText: string;
}

const PowerPointExportButton: React.FC<PowerPointExportButtonProps> = ({ messageText }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState<PowerPointOption>(POWERPOINT_OPTIONS[0]);
  const [dropdownPosition, setDropdownPosition] = useState<DropdownPositionState>({
    top: 0,
    left: 0,
    width: 0,
    orientation: 'down',
  });
  const buttonRef = useRef<HTMLButtonElement>(null);
  const dropdownContentRef = useRef<HTMLDivElement>(null);

  const { isLoading, error, handlePowerPointExport } = usePowerPointExport();

  const iconStyle = getExportIconStyle(isLoading, 'PowerPoint');

  const buttonDisplayIcon = isLoading ? (
    <AiOutlineLoading className={clsx(iconStyle)} />
  ) : (
    <TbFileTypePpt className={iconStyle} />
  );
  const buttonDisplayText = getExportButtonText(isLoading, 'PowerPoint');

  const toggleDropdown = () => {
    if (isLoading) return;
    if (!isOpen && buttonRef.current) {
      setDropdownPosition(calculateDropdownPosition(buttonRef.current));
    }
    setIsOpen(!isOpen);
  };

  const handleOptionClick = (option: PowerPointOption) => {
    if (isLoading) return;
    setSelectedOption(option);
    setIsOpen(false);
    handlePowerPointExport(messageText, option.value);
  };

  useEffect(() => {
    if (error) {
      console.error('PowerPoint Export Error:', error);
    }
  }, [error]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node) &&
        dropdownContentRef.current &&
        !dropdownContentRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen && !isLoading) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, isLoading]);

  const currentButtonStyle = clsx(EXPORT_BUTTON_STYLES.base, {
    [EXPORT_BUTTON_STYLES.active]: !isLoading,
    [EXPORT_BUTTON_STYLES.disabled]: isLoading,
  });

  const dropdownStyle = {
    position: 'fixed' as const,
    top: `${dropdownPosition.top}px`,
    left: `${dropdownPosition.left}px`,
    width: `${dropdownPosition.width}px`,
    minWidth: `${dropdownPosition.width}px`,
    zIndex: 50,
  };

  const dropdownClasses = clsx(
    'ppt-export__dropdown',
    dropdownPosition.orientation === 'up'
      ? POWERPOINT_DROPDOWN_STYLES.dropdownUp
      : POWERPOINT_DROPDOWN_STYLES.dropdown
  );

  const DropdownContent = (
    <div
      ref={dropdownContentRef}
      className={dropdownClasses}
      style={dropdownStyle}
      role="listbox"
      aria-labelledby="options-menu"
      data-testid="ppt-export__dropdown"
    >
      <ul className="ppt-export__options-list py-1">
        {POWERPOINT_OPTIONS.map((option: PowerPointOption) => (
          <li
            key={option.value}
            className={clsx(
              POWERPOINT_DROPDOWN_STYLES.listItem,
              option.value === selectedOption.value && POWERPOINT_DROPDOWN_STYLES.listItemSelected
            )}
            onClick={() => handleOptionClick(option)}
            role="option"
            aria-selected={option.value === selectedOption.value}
            data-testid={`ppt-export__option--${option.value}`}
          >
            {option.text}
          </li>
        ))}
      </ul>
    </div>
  );

  return (
    <div className="ppt-export">
      <button
        type="button"
        ref={buttonRef}
        className={`${currentButtonStyle} font-roboto`}
        onClick={toggleDropdown}
        disabled={isLoading}
        aria-haspopup="listbox"
        aria-expanded={isOpen}
        data-testid="ppt-export__button"
      >
        {buttonDisplayIcon}
        <span className="ppt-export__button-text">{buttonDisplayText}</span>
        <IoMdArrowDropdown
          className={clsx(
            POWERPOINT_DROPDOWN_STYLES.arrow,
            isOpen &&
              !isLoading &&
              (dropdownPosition.orientation === 'up' ? 'rotate-0' : 'rotate-180'),
            isLoading && 'hidden'
          )}
        />
      </button>

      {isOpen &&
        !isLoading &&
        typeof document !== 'undefined' &&
        ReactDOM.createPortal(DropdownContent, document.body)}
    </div>
  );
};

export default PowerPointExportButton;
