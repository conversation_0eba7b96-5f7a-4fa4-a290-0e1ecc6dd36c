function setCookie(cname, cvalue, exdays = 180) {
  const d = new Date();
  d.setTime(d.getTime() + exdays * 24 * 60 * 60 * 1000);
  let expires = "expires=" + d.toUTCString();
  document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
}

function getCookie(cname) {
  let name = cname + "=";
  let decodedCookie = decodeURIComponent(document.cookie);
  let ca = decodedCookie.split(";");
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i];
    while (c.charAt(0) == " ") {
      c = c.substring(1);
    }
    if (c.indexOf(name) == 0) {
      return c.substring(name.length, c.length);
    }
  }
  return false;
}

// TODO: Clean up similar functions.

function setGenericCookie(cookieName, cookieValue, expires) {
  // Set the cookie with the room name, 12-hour expiry, and secure flag
  document.cookie = `${cookieName}=${cookieValue}; expires=${getCookieExpiryDate(
    expires
  ).toUTCString()}; path=/; secure`;
}

function getCookieExpiryDate(hours) {
  const now = new Date();
  now.setTime(now.getTime() + hours * 60 * 60 * 1000);
  return now;
}

function getGenericCookie(cookieName) {
  cookieName = `${cookieName}=`;
  const cookies = document.cookie.split(";");
  for (let i = 0; i < cookies.length; i++) {
    let cookie = cookies[i].trim();
    if (cookie.indexOf(cookieName) === 0) {
      return cookie.substring(cookieName.length);
    }
  }
  return null; // Cookie not found
}
