---
description: 
globs: 
alwaysApply: false
---
# Sidekick Frontend Project Structure and Key Concepts

## 1. Overview
This project is a React application built with Vite and TypeScript. It utilizes React Router for navigation, Redux Toolkit for complex state management (especially for workbooks), and custom contexts for theming and sidebar state. Styling is done using Tailwind CSS and SCSS modules.

## 2. Core Application Flow
- **Entry Point:** [main.tsx](mdc:sidekick-frontend/src/main.tsx) initializes the Redux store, dispatches initial data fetching actions (`fetchMyWorkbooks`, `fetchGlobalWorkbooks`), and renders the main `App` component.
- **Root Component:** [App.tsx](mdc:sidekick-frontend/src/App.tsx) sets up global context providers:
    - `ThemeProvider` from [@contexts/ThemeContext.tsx](mdc:sidekick-frontend/src/contexts/ThemeContext.tsx) for light/dark mode.
    - `SidebarContext` from [@contexts/SidebarContext.tsx](mdc:sidekick-frontend/src/contexts/SidebarContext.tsx) for sidebar visibility.
    - `WorkbookProvider` from [@features/workbook/WorkbookContext.tsx](mdc:sidekick-frontend/src/components/features/workbook/WorkbookContext.tsx) (currently uses mock data).
    - It then renders the `RouterProvider` with routes defined in [routes.tsx](mdc:sidekick-frontend/src/routes.tsx).
- **Global Styles:** Applied via [index.css](mdc:sidekick-frontend/src/index.css), which imports Tailwind and defines custom scrollbar styles.

## 3. Routing
- Defined in [routes.tsx](mdc:sidekick-frontend/src/routes.tsx) using `createBrowserRouter`.
- Uses a dynamic `basePath`.
- **Main Layout:** [layouts/MainLayout.tsx](mdc:sidekick-frontend/src/layouts/MainLayout.tsx) is the primary layout, containing the [Sidebar](mdc:sidekick-frontend/src/components/layout/sidebar/index.tsx) and a content area with a [TopBar](mdc:sidekick-frontend/src/components/layout/main/TopBar.tsx) and [FeedbackSection](mdc:sidekick-frontend/src/components/layout/main/FeedbackSection.tsx).
- **Nested Routes:** Handles nested views, particularly for the `/workbooks` section, which uses [WorkbookLayout](mdc:sidekick-frontend/src/components/features/workbook/layouts/WorkbookLayout.tsx) and specific views like [WorkbookListView](mdc:sidekick-frontend/src/components/features/workbook/views/WorkbookListView.tsx) and [WorkbookDetailsView](mdc:sidekick-frontend/src/components/features/workbook/views/WorkbookDetailsView/WorkbookDetailsView.tsx).
- **Not Found:** A generic [NotFoundPage](mdc:sidekick-frontend/src/components/common/NotFoundPage.tsx) handles invalid routes.

## 4. State Management (Redux Toolkit)
- **Store:** Configured in [store/store.ts](mdc:sidekick-frontend/src/store/store.ts).
- **Slices:** Located in [store/slices/](mdc:sidekick-frontend/src/store/slices).
    - **[authSlice.ts](mdc:sidekick-frontend/src/store/slices/authSlice.ts):** Defines the structure for authentication state (currently minimal).
    - **[workbookSlice.ts](mdc:sidekick-frontend/src/store/slices/workbookSlice.ts):** Manages all state related to user and global workbooks, including files, sessions, messages, and pending states (fetching, updating, deleting, querying, indexing). It defines numerous async thunks for interacting with the `workbookApi`.
- **Typed Hooks:** [store/hooks.ts](mdc:sidekick-frontend/src/store/hooks.ts) provides pre-typed `useAppDispatch` and `useAppSelector`.
- **Typed Helpers:** [store/withTypes.ts](mdc:sidekick-frontend/src/store/withTypes.ts) provides helpers like `createSidekickAsyncThunk` for creating thunks with correct types.

## 5. API Communication
- **Client Wrapper:** [api/client.ts](mdc:sidekick-frontend/src/api/client.ts) provides a custom `fetch` wrapper (`client`) with methods like `get`, `post`, etc. It includes logic to handle authentication checks and token refresh flows via functions in [api/auth.ts](mdc:sidekick-frontend/src/api/auth.ts).
- **Authentication API:** [api/auth.ts](mdc:sidekick-frontend/src/api/auth.ts) handles the OAuth token refresh process (`refreshAuthFlow`, `refreshAuth`) and checking the current authentication status (`checkAuth`) using cookies.
- **Workbook API:** [api/workbookApi.ts](mdc:sidekick-frontend/src/api/workbookApi.ts) defines functions for all workbook-related operations (CRUD for workbooks, files, sessions, querying, feedback) using the `client` wrapper. It constructs API routes based on the current hostname and a base path. It also includes the direct S3 `PUT` request logic (`putFileDirectly`).

## 6. Components (`components/`)
- **Common Components:** [components/common/](mdc:sidekick-frontend/src/components/common) contains globally reusable components like:
    - [Modal.tsx](mdc:sidekick-frontend/src/components/common/Modal.tsx): A generic modal component.
    - [NotFoundPage.tsx](mdc:sidekick-frontend/src/components/common/NotFoundPage.tsx): Displays a 404 message.
    - [SimpleAccordion.tsx](mdc:sidekick-frontend/src/components/common/SimpleAccordion.tsx): A basic accordion component.
- **Layout Components:** [components/layout/](mdc:sidekick-frontend/src/components/layout) contains components used within the main application layout:
    - [sidebar/](mdc:sidekick-frontend/src/components/layout/sidebar): The main sidebar navigation.
    - [SidebarHeader/](mdc:sidekick-frontend/src/components/layout/SidebarHeader): Header for the sidebar with logo and collapse button.
    - [SidebarLink/](mdc:sidekick-frontend/src/components/layout/SidebarLink): Individual navigation links within the sidebar, supporting sub-links.
    - [main/](mdc:sidekick-frontend/src/components/layout/main): Components for the main content area's chrome:
        - [TopBar.tsx](mdc:sidekick-frontend/src/components/layout/main/TopBar.tsx): Top navigation bar, includes AI model selection for the chat page.
        - [FeedbackSection.tsx](mdc:sidekick-frontend/src/components/layout/main/FeedbackSection.tsx): Bottom feedback prompt.
        - [ExpandButton.tsx](mdc:sidekick-frontend/src/components/layout/main/ExpandButton.tsx): Button to re-open the collapsed sidebar.
- **Feature Components:** [components/features/](mdc:sidekick-frontend/src/components/features) contains components specific to application features:
    - **Chat:** [components/features/chat/](mdc:sidekick-frontend/src/components/features/chat): Components for the main chat interface ([ChatView.tsx](mdc:sidekick-frontend/src/components/features/chat/ChatView.tsx)), including input ([PromptInput.tsx](mdc:sidekick-frontend/src/components/features/chat/components/PromptInput.tsx)), suggestions ([PromptSuggestions.tsx](mdc:sidekick-frontend/src/components/features/chat/components/PromptSuggestions.tsx)), etc.
    - **Theme:** [components/features/theme/](mdc:sidekick-frontend/src/components/features/theme): Contains the [ThemeToggle.tsx](mdc:sidekick-frontend/src/components/features/theme/ThemeToggle.tsx) button.
    - **Workbook:** [components/features/workbook/](mdc:sidekick-frontend/src/components/features/workbook): A large feature area with many sub-components for workbook management and interaction.
        - **Layouts:** [layouts/](mdc:sidekick-frontend/src/components/features/workbook/layouts): Layouts specific to workbook views ([WorkbookLayout.tsx](mdc:sidekick-frontend/src/components/features/workbook/layouts/WorkbookLayout.tsx), [WorkbookDashboardLayout.tsx](mdc:sidekick-frontend/src/components/features/workbook/layouts/WorkbookDashboardLayout.tsx)).
        - **Views:** [views/](mdc:sidekick-frontend/src/components/features/workbook/views): Top-level views for workbooks:
            - [WorkbookListView.tsx](mdc:sidekick-frontend/src/components/features/workbook/views/WorkbookListView.tsx): Displays cards for workbooks. Includes the "Add Workbook" flow triggering [CreateWorkbookModalContent.tsx](mdc:sidekick-frontend/src/components/features/workbook/views/CreateWorkbookModalContent.tsx).
            - [WorkbookDetailsView/](mdc:sidekick-frontend/src/components/features/workbook/views/WorkbookDetailsView): Container view for a single workbook, combining the chat view and utility bar. Handles fetching workbook data and file uploads initiated from the list view.
            - [WorkbookChatView/](mdc:sidekick-frontend/src/components/features/workbook/views/WorkbookChatView): Displays the chat interface within a workbook, including [ChatMessageList.tsx](mdc:sidekick-frontend/src/components/features/workbook/components/chat/ChatMessageList.tsx) and the chat input.
        - **Components:** [components/](mdc:sidekick-frontend/src/components/features/workbook/components): Various UI elements for workbooks:
            - [cards/](mdc:sidekick-frontend/src/components/features/workbook/components/cards): Components for displaying workbook cards ([WorkbookCard.tsx](mdc:sidekick-frontend/src/components/features/workbook/components/cards/WorkbookCard/WorkbookCard.tsx), [AddWorkbookCard.tsx](mdc:sidekick-frontend/src/components/features/workbook/components/cards/AddWorkbookCard.tsx)).
            - [chat/](mdc:sidekick-frontend/src/components/features/workbook/components/chat): Components specifically for the workbook chat experience:
                - [ChatMessage.tsx](mdc:sidekick-frontend/src/components/features/workbook/components/chat/ChatMessage.tsx): Renders a single message (user or model).
                - [ChatMessageList.tsx](mdc:sidekick-frontend/src/components/features/workbook/components/chat/ChatMessageList.tsx): Displays the list of messages with infinite scrolling.
                - [CitationRenderer.tsx](mdc:sidekick-frontend/src/components/features/workbook/components/chat/CitationRenderer.tsx): Renders message content with inline citations and an expandable list of sources.
                - [MarkdownRenderer.tsx](mdc:sidekick-frontend/src/components/features/workbook/components/chat/MarkdownRenderer.tsx): Renders markdown content, including syntax highlighting for code blocks.
                - [MessageActions.tsx](mdc:sidekick-frontend/src/components/features/workbook/components/chat/MessageActions.tsx): Buttons for copy/feedback on model messages.
            - [dashboard/](mdc:sidekick-frontend/src/components/features/workbook/components/dashboard): Components potentially used in a dashboard view (e.g., [RightPanel.tsx](mdc:sidekick-frontend/src/components/features/workbook/components/dashboard/RightPanel.tsx) for file listing/search).
            - [WorkbookUtilityBar/](mdc:sidekick-frontend/src/components/features/workbook/components/WorkbookUtilityBar): The right-hand sidebar shown in the `WorkbookDetailsView`, allowing workbook name editing, file uploads ([WorkbookUtilityFile.tsx](mdc:sidekick-frontend/src/components/features/workbook/components/WorkbookUtilityBar/WorkbookUtilityFile/index.tsx)), and creating new chat sessions.

## 7. Contexts (`contexts/`)
- **[ThemeContext.tsx](mdc:sidekick-frontend/src/contexts/ThemeContext.tsx):** Manages the application theme (light/dark) using `useState` and `localStorage`. Provides `isDarkMode`, `toggleTheme`, `setTheme`.
- **[SidebarContext.tsx](mdc:sidekick-frontend/src/contexts/SidebarContext.tsx):** Manages the expanded/collapsed state of the main sidebar. Provides `isSidebarExpanded`, `toggleSidebar`.
- **[WorkbookContext.tsx](mdc:sidekick-frontend/src/components/features/workbook/WorkbookContext.tsx):** (Currently seems mock/placeholder) Intended to manage workbook data, loading, and errors, providing `currentWorkbook`, `setCurrentWorkbookById`, etc. *Note: Most workbook logic is handled by the Redux `workbookSlice`.*

## 8. Hooks (`hooks/`)
- **[useThemeStyles.ts](mdc:sidekick-frontend/src/hooks/useThemeStyles.ts):** Consumes `ThemeContext` and provides convenient access to theme state (`isDarkMode`), theme functions (`toggleTheme`), theme colors ([config/theme.ts](mdc:sidekick-frontend/src/config/theme.ts)), and pre-computed Tailwind class strings for different UI elements based on the current theme.
- **[useWorkbookDashboard.ts](mdc:sidekick-frontend/src/components/features/workbook/hooks/useWorkbookDashboard.ts):** A hook potentially used for managing local state within workbook dashboard components (message input, file list state).
- **[useMessageActions.ts](mdc:sidekick-frontend/src/components/features/workbook/components/chat/hooks/useMessageActions.ts):** Manages the local state for the message action bar (copy, like, dislike status).

## 9. Configuration (`config/`)
- **[constants.ts](mdc:sidekick-frontend/src/config/constants.ts):** Defines application constants, currently including file size limits.
- **[theme.ts](mdc:sidekick-frontend/src/config/theme.ts):** Defines the color palette, spacing, typography, and other design tokens. Exports `getThemeColors` and `themeClasses` which are used by the `useThemeStyles` hook to generate dynamic styles.

## 10. Types (`types/`)
- Contains TypeScript interfaces and type definitions used throughout the application.
- **[index.ts](mdc:sidekick-frontend/src/types/index.ts):** Exports types from other files.
- **[workbookTypes.ts](mdc:sidekick-frontend/src/components/features/workbook/workbookTypes.ts):** A very important file containing numerous type definitions specifically for the workbook feature (API request/response shapes, component props, context types, Redux state related types).
- Other files define types for layout ([layout.ts](mdc:sidekick-frontend/src/types/layout.ts)), theming ([theme.ts](mdc:sidekick-frontend/src/types/theme.ts)), etc.

## 11. Utilities (`utils/`)
- **[themeUtils.ts](mdc:sidekick-frontend/src/utils/themeUtils.ts):** Contains helper functions to dynamically get theme-based class strings (likely less used now that `useThemeStyles` exists).



