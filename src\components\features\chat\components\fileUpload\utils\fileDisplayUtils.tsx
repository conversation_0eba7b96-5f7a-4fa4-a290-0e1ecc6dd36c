import React from 'react';
import { FaFileAlt, FaImage, FaVideo, FaFileAudio, FaFilePdf } from 'react-icons/fa';
import { BsFiletypeTxt } from 'react-icons/bs';

const IconWrapper = (IconComponent: React.ElementType, className: string): React.ReactElement => (
  <IconComponent className={className} />
);

// Maps a category key to its icon component
interface IconStyle {
  icon: React.ElementType;
  className: string;
}

const iconStyles: Record<string, IconStyle> = {
  image: { icon: FaImage, className: 'text-blue-500' },
  video: { icon: FaVideo, className: 'text-red-500' },
  audio: { icon: FaFileAudio, className: 'text-purple-500' },
  pdf: { icon: FaFilePdf, className: 'text-orange-500' },
  text: { icon: BsFiletypeTxt, className: 'text-gray-500' },
  default: { icon: FaFileAlt, className: 'text-gray-400' },
};

type FileTypePredicate = (type: string) => boolean;
const fileTypeMatchers: Array<[FileTypePredicate, string]> = [
  [(type: string) => type.startsWith('image/'), 'image'],
  [(type: string) => type.startsWith('video/'), 'video'],
  [(type: string) => type.startsWith('audio/'), 'audio'],
  [(type: string) => type === 'application/pdf', 'pdf'],
  [(type: string) => type === 'text/plain', 'text'],
];

const getGenericFileIcon = (
  fileType: string | undefined,
  sizeClass: string,
  isExpired?: boolean
): React.ReactElement => {
  const currentType = fileType || '';

  let determinedCategoryKey = 'default'; 

  for (const [predicate, categoryKey] of fileTypeMatchers) {
    if (predicate(currentType)) {
      determinedCategoryKey = categoryKey;
      break; 
    }
  }

  // Retrieve the icon style; fallback to default if category somehow not in iconStyles
  const style = iconStyles[determinedCategoryKey] || iconStyles.default;
  let finalClassName = `${sizeClass} ${style.className}`;

  if (isExpired) {
    finalClassName = `${finalClassName} filter grayscale opacity-60`;
  }

  return IconWrapper(style.icon, finalClassName);
};

export const getFileIcon = (fileType: string | undefined): React.ReactElement => {
  return getGenericFileIcon(fileType, 'w-8 h-8');
};

export const getChatMessageFileIcon = (fileType: string | undefined, isExpired?: boolean): React.ReactElement => {
  return getGenericFileIcon(fileType, 'w-5 h-5', isExpired);
};

export const displaySize = (size: number): string => {
  if (size < 1024) return `${size} bytes`;
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
  return `${(size / (1024 * 1024)).toFixed(1)} MB`;
};
