import { useCallback, useRef } from 'react';

/**
 * Hook for async rendering with requestIdleCallback
 * Falls back to setTimeout if requestIdleCallback is not available
 */
export function useAsyncRender() {
  const renderQueueRef = useRef<Set<() => void>>(new Set());
  const isProcessingRef = useRef(false);

  const scheduleRender = useCallback((renderFn: () => void) => {
    renderQueueRef.current.add(renderFn);

    if (!isProcessingRef.current) {
      isProcessingRef.current = true;

      const processQueue = () => {
        if (renderQueueRef.current.size === 0) {
          isProcessingRef.current = false;
          return;
        }

        // Process one render function per idle callback
        const renderFn = renderQueueRef.current.values().next().value;
        if (renderFn) {
          renderQueueRef.current.delete(renderFn);

          try {
            renderFn();
          } catch (error) {
            console.error('[Mermaid Async Render] Error in render function:', error);
          }
        }

        // Schedule next processing if queue is not empty
        if (renderQueueRef.current.size > 0) {
          if ('requestIdleCallback' in window) {
            requestIdleCallback(processQueue, { timeout: 1000 });
          } else {
            setTimeout(processQueue, 0);
          }
        } else {
          isProcessingRef.current = false;
        }
      };

      if ('requestIdleCallback' in window) {
        requestIdleCallback(processQueue, { timeout: 1000 });
      } else {
        setTimeout(processQueue, 0);
      }
    }
  }, []);

  const clearQueue = useCallback(() => {
    renderQueueRef.current.clear();
    isProcessingRef.current = false;
  }, []);

  return {
    scheduleRender,
    clearQueue,
    queueSize: renderQueueRef.current.size,
  };
}
