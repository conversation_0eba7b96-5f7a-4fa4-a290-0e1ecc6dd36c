document.addEventListener("DOMContentLoaded", function () {
  //  displayPopup("fileUpload");

  const displayButton = document.getElementById("sendFileButton");
  const uploadButton = document.getElementById("uploadFile");
  const cancelButton = document.getElementById("cancelFile");
  const fileForm = document.getElementById("fileForm");
  try {
    displayButton.addEventListener("click", displayUpload);
    cancelButton.addEventListener("click", cancelFiles);

    fileForm.addEventListener("submit", (ev) => uploadFiles(ev, true));
  } catch (error) {
    console.error(error);
  }
  checkIfAllowed();
  fileUploadTest();
});
let uploadErrorFlag = false;

/**
 * Displays the file upload popup.
 */
function displayUpload() {
  //   if (showLegalAttestation()) {
  displayPopup("fileUpload");
  //   }
}

/**
 * Sets up drag and drop functionality and file input events for the file upload area.
 */
function fileUploadTest() {
  const dropArea1 = document.getElementById("drop-area-1");
  const fileInput1 = document.getElementById("file-upload-1");
  const dropAreaText1 = document.getElementById("drop-area-text-1");
  const fileList1 = document.getElementById("file-list-1");
  // Drag and Drop event listeners
  dropArea1.addEventListener("dragover", (e) => {
    e.preventDefault();
    dropArea1.classList.add("hover");
  });

  dropArea1.addEventListener("dragleave", () => {
    dropArea1.classList.remove("hover");
  });

  // Adds files to list & Remove button
  dropArea1.addEventListener("drop", (e) => {
    e.preventDefault();
    dropArea1.classList.remove("hover");
    const files = e.dataTransfer.files;
    fileInput1.files = files;

    // Create and add a new list item for each file
    modifyFileList(files);
  });

  // Click to browse
  dropArea1.addEventListener("click", () => {
    fileInput1.click();
  });

  // File input change events
  fileInput1.addEventListener("change", (e) => {
    const files = e.target.files;

    // Create and add a new list item for each file
    modifyFileList(files);
  });
}

/**
 * Modifies the file list to display the selected files.
 * @param {FileList} files - The list of files selected by the user.
 */
function modifyFileList(files) {
  const fileList1 = document.getElementById("file-list-1");
  const uploadFile = document.getElementById("uploadFile");
  clearAlert();
  // Clear previous file list
  fileList1.innerHTML = "";

  // Create and add a new list item for each file
  for (const file of files) {
    // Validate the file before adding it to the list
    if (validateFile(file)) {
      const listItem = document.createElement("li");
      listItem.textContent = file.name;
      fileList1.appendChild(listItem);
      uploadFile.disabled = false;
    } else {
      // Display an error message if the file is not valid
      invalidFiles();
      clearFiles();
    }
  }
}

/**
 * Clears the selected files and resets the file upload form.
 */
function cancelFiles() {
  const fileForm = document.getElementById("fileForm");
  const uploadButton = document.getElementById("uploadFile");
  const fileList1 = document.getElementById("file-list-1");
  const fileUploadmodal = document.getElementById("fileUpload");
  clearFiles();
  uploadButton.disabled = true;
  console.log("Cleared files");
  fileUploadmodal.style.display = "none";
}

/**
 * Handles the file upload form submission.
 * @param {Event} event - The form submission event.
 */
async function uploadFiles(event, persistToSession = false) {
  event.preventDefault();
  const fileForm = document.getElementById("fileForm");
  const uploadButton = document.getElementById("uploadFile");
  const uploadStatusAlert = document.getElementById("upload-status-alert");
  const cancelButton = document.getElementById("cancelFile");
  const submitPrompt = document.getElementById("submitPrompt");

  let firestoreSessionId = ''
  if (typeof(sessionDocument) !== 'undefined' && typeof(sessionDocument) !== 'null') {
    firestoreSessionId = sessionDocument.id
  } else {
    firestoreSessionId = document.getElementById("firestoreSessionId").value
  }

  let sessionType = 'chat'
  if (window.location.href.includes("code")) {
    sessionType = 'code'
  } else if (window.location.href.includes("med")) {
    sessionType = 'mdlm'
  } else {
    sessionType = 'chat'
  }

  submitPrompt.disabled = true;
  cancelButton.disabled = true;

  // Show spinner and change upload button text
  uploadButton.innerHTML = '<div class="spinner"></div>';
  uploadButton.disabled = true;
  uploadStatusAlert.style.display = "none";

  let uploadedForm = new FormData(fileForm);

  // Resets global var in uploadHandlers.js
  fileAndMime = [];
  for (const [key, value] of uploadedForm.entries()) {
    if (value instanceof File) {
      const mimeType = value.type;
      const fileName = value.name;
      const fileSize = value.size;
      fileAndMime.push({
        file_name: fileName,
        mime_type: mimeType,
        file_size: fileSize,
      });
    }
  }

  let bodyVariable = {
    files_mimes: fileAndMime,
  }
  if (persistToSession) {
    bodyVariable["persist_to_session"] = true
    bodyVariable["session_id"] = firestoreSessionId
    bodyVariable["session_type"] = sessionType
  }

  bodyVariable = JSON.stringify(bodyVariable)

  // Handle utf-8
  try {
    uploadedForm = await processForm(uploadedForm);
  } catch (error) {
    console.error(error);
    // Reset form on error
    uploadedForm = new FormData(fileForm);
  }
  //  finally {
  // }
  if (showLegalAttestation()) {
    let response_err;
    try {
      const result_1 = await fetch(fileLink, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: bodyVariable,
      })
        .then((response) => {
          if (response.ok === true) {
            //  uploadStatusAlert.textContent = "Upload successful!";
            //  uploadStatusAlert.className = "alert-success";
            return response.json();
          } else {
            uploadStatusAlert.textContent = "Upload failed!";
            uploadStatusAlert.className = "alert-failure";
            cancelButton.disabled = false;
            submitPrompt.disabled = false;
            response_err = `${response.status} ${response.statusText}`;
            throw new Error(response_err);
            return false;
          }
        })
        .then((data) => {
          if (data === false) {
            return false;
          }
          let file_lists = handleFileResponse(data);
          file_history.push({
            gcs_uri: file_lists[1],
          });

          return file_lists[0];
        });
      if (result_1 != false) {
        const result_2 = await uploadWrapper(uploadedForm, result_1);
      } else {
        throw new Error("An invalid response was received."); //Just making sure if upload fails it doesn't show as a success.
      }
    } catch (error) {
      setUploadError(error);
    } finally {
      // Restore upload button text and re-enable it
      uploadWrapUp();
    }
  } else {
    submitPrompt.disabled = false;
    cancelButton.disabled = false;

    // Show spinner and change upload button text
    uploadButton.innerHTML = "Upload File(s)";
    uploadButton.disabled = false;
    uploadStatusAlert.style.display = "none";
  }
}

function uploadWrapUp() {
  const fileForm = document.getElementById("fileForm");
  const uploadButton = document.getElementById("uploadFile");
  const uploadStatusAlert = document.getElementById("upload-status-alert");
  const cancelButton = document.getElementById("cancelFile");
  const submitPrompt = document.getElementById("submitPrompt");
  if (!uploadErrorFlag) {
    uploadStatusAlert.style.display = "none";
    showUploaded();
    clearFiles();
  } else {
    uploadStatusAlert.style.display = "block";
  }
  uploadButton.disabled = true;
  submitPrompt.disabled = false;
  cancelButton.disabled = false;
  uploadButton.innerHTML = "Upload File(s)";
  console.log("Submitted Files");
}
/**
 * Clears the selected files and resets the file upload form.
 */
function clearFiles() {
  const fileForm = document.getElementById("fileForm");
  const uploadButton = document.getElementById("uploadFile");
  const cancelButton = document.getElementById("cancelFile");
  const submitPrompt = document.getElementById("submitPrompt");

  const fileList1 = document.getElementById("file-list-1");
  const fileUploadmodal = document.getElementById("fileUpload");
  fileForm.reset();

  uploadButton.disabled = true;
  cancelButton.disabled = false;
  submitPrompt.disabled = false;

  while (fileList1.firstChild) {
    fileList1.removeChild(fileList1.firstChild);
  }
}

/**
 * Displays a message indicating that the files have been uploaded.
 */
function showUploaded() {
  document.getElementById("fileUpload").style.display = "none";
  const fileList1 = document.getElementById("file-list-1");
  createMessage(
    "person",
    "prompts",
    `File(s) uploaded:<br>${fileList1.innerHTML}`,
    false
  );
}

// Function to validate file type and size
/**
 * Validates the file type and size.
 * @param {File} file - The file to validate.
 * @returns {boolean} - True if the file is valid, false otherwise.
 */
function validateFile(file) {
  // Define allowed file types
  const allowedFileTypes = [
    "image/jpeg",
    "image/png",
    "video/mp4",
    "video/webm",
    "video/x-matroska",
    "video/quicktime",
    "application/pdf",
    "text/plain",
    "audio/mpeg",
    "audio/wav",
    "audio/webm",
    "audio/ogg",
    "audio/aac",
    "audio/x-flac",
  ]; // Example file types

  // Define maximum file size in bytes
  const maxFileSize = 1025 * 1024 * 1024; // 1074790400 bytes versus actual 1GB in bytes of 1073741824

  const maxPDFFileSize = 51 * 1024 * 1024; //51 MB, just to ensure all PDFs can get uploaded
  const maxTXTFileSize = 8 * 1024 * 1024; //8 MB, just to ensure all TXTs can get uploaded

  // Check if file type is allowed
  if (allowedFileTypes.includes(file.type)) {
    // Check if file size is within the limit
    let valid_file = true;
    if (file.type == "application/pdf" && file.size > maxPDFFileSize) {
      valid_file = false;
    } else if (file.type == "text/plain" && file.size > maxTXTFileSize) {
      valid_file = false;
    }

    if (file.size <= maxFileSize && valid_file) {
      return true;
    }
  }
  return false;
}

/**
 * Displays an error message indicating that the selected files are invalid.
 */
function invalidFiles() {
  const uploadStatusAlert = document.getElementById("upload-status-alert");

  uploadStatusAlert.style.display = "block";
  uploadStatusAlert.textContent =
    "Invalid file type or size. Please choose a file with a valid extension and size.";
  uploadStatusAlert.className = "alert-failure";
}

/**
 * Clears the upload status alert message.
 */
function clearAlert() {
  const uploadStatusAlert = document.getElementById("upload-status-alert");

  uploadStatusAlert.style.display = "none";
}

const invalidUtf8Chars = ["\uFFFD", "\u0000"];

async function processForm(oldForm) {
  let newForm = new FormData();

  for (const [key, value] of oldForm.entries()) {
    if (value instanceof File) {
      const mimeType = value.type;
      const fileName = value.name;
      if (mimeType == "text/plain") {
        const oldText = await value.text();
        let newText;
        for (const char of invalidUtf8Chars) {
          newText = oldText.replace(new RegExp(char, "g"), " ");
        }
        // const newText = oldText.replace("Hello", "Goodbye");
        const blob = new Blob([newText], { type: "text/plain" });
        console.log("TXT files");
        newForm.append(key, blob, fileName);
      } else {
        // Append other files
        console.log("Non-TXT files");
        newForm.append(key, value, fileName);
      }
    }
  }
  return newForm;
}
