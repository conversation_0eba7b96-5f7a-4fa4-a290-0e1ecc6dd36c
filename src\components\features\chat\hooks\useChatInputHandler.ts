import type { PromptTemplate } from '@/types';
import { useState, useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  sendMessage,
  setSelectedTemperature,
  selectIsLoading,
  selectError,
  clearError,
  selectSelectedChatType,
} from '@/store/slices/chatSlice';
import { FileInfo } from '@/types/fileUpload';
import { usePolicyWebChat } from '../../policy_web/hooks/usePolicyWebChat';

export interface SendMessagePayload {
  prompt: string;
  files?: FileInfo[];
  promptTemplateId?: string;
  promptTemplateIsGlobal?: boolean;
}

export interface UseChatInputHandlerResult {
  promptText: string;
  handleInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  handleSubmit: (attachedFiles?: FileInfo[]) => void;
  handlePromptTemplate: (
    promptTemplate: PromptTemplate,
    promptTextOverride: string | null,
    isGlobal?: boolean,
    attachedFiles?: FileInfo[]
  ) => void;
  handleSuggestionClick: (suggestion: string) => void;
  handleTemperatureChange: (temperature: number) => void;
  isPolicyModeActive: boolean;
}

export const useChatInputHandler = (
  onSuggestionClicked?: () => void
): UseChatInputHandlerResult => {
  const [promptText, setPromptText] = useState('');
  const dispatch = useAppDispatch();
  const isLoading = useAppSelector(selectIsLoading);
  const error = useAppSelector(selectError);
  const selectedChatType = useAppSelector(selectSelectedChatType);

  const { submitPolicyQuery } = usePolicyWebChat();

  const isPolicyModeActive = selectedChatType === 'Policy';

  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      setPromptText(e.target.value);
      if (error) {
        dispatch(clearError());
      }
    },
    [dispatch, error]
  );

  const handleSubmit = useCallback(
    (attachedFiles?: FileInfo[]) => {
      const trimmedPrompt = promptText.trim();
      if (isPolicyModeActive) {
        if (trimmedPrompt && !isLoading) {
          submitPolicyQuery(trimmedPrompt);
          setPromptText('');
        }
      } else {
        // TODO: re-enable Allow sending if there are files, even if the prompt is empty.
        // if ((trimmedPrompt || (attachedFiles && attachedFiles.length > 0)) && !isLoading) {
        if (trimmedPrompt && !isLoading) {
          const payload: SendMessagePayload = { prompt: trimmedPrompt };
          if (attachedFiles && attachedFiles.length > 0) {
            payload.files = attachedFiles;
          }
          dispatch(sendMessage(payload));
          setPromptText('');
          // Clearing of files from useFileUpload will be handled by ChatInputArea after this submit
        }
      }
    },
    [promptText, isLoading, dispatch, selectedChatType, submitPolicyQuery, isPolicyModeActive]
  );

  const handlePromptTemplate = useCallback(
    (
      promptTemplate: PromptTemplate,
      promptTextOverride: string | null = null,
      isGlobal: boolean = false,
      attachedFiles?: FileInfo[]
    ) => {
      const promptText = promptTextOverride ?? promptTemplate.prompt;
      const trimmedPrompt = promptText.trim();
      // TODO: re-enable Allow sending if there are files, even if the prompt is empty.
      if (trimmedPrompt && !isLoading) {
        const payload: SendMessagePayload = {
          prompt: trimmedPrompt,
          promptTemplateId: promptTemplate.id,
          promptTemplateIsGlobal: isGlobal,
        };
        if (attachedFiles && attachedFiles.length > 0) {
          payload.files = attachedFiles;
        }
        dispatch(sendMessage(payload));
        setPromptText('');
      }
    },
    [dispatch, error]
  );

  const handleSuggestionClick = useCallback(
    (suggestion: string) => {
      setPromptText(suggestion);
      if (onSuggestionClicked) {
        onSuggestionClicked();
      }

      if (error) {
        dispatch(clearError());
      }
    },
    [dispatch, error, onSuggestionClicked]
  );

  const handleTemperatureChange = useCallback(
    (temperature: number) => {
      dispatch(setSelectedTemperature(temperature));
    },
    [dispatch]
  );

  return {
    promptText,
    handleInputChange,
    handlePromptTemplate,
    handleSubmit,
    handleSuggestionClick,
    handleTemperatureChange,
    isPolicyModeActive,
  };
};
