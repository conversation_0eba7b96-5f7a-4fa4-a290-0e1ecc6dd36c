.sidekick-dropdown {
  &__trigger {
    display: flex;
    height: 40px;
    min-width: 144px;
    width: 100%;
    align-items: center;
    align-self: stretch;
    justify-content: space-between;
    gap: 10px;
    border-radius: var(--border-radius-border-radius-l);
    background-color: var(--button-button-secondary);
    border-width: var(--border-weight-border-weight-s);
    border-color: var(--border-color-border-interactive);
    padding-right: var(--spacing-spacing-m-1);
    padding-left: var(--spacing-spacing-m-2);
    font-size: 16px;
    user-select: none;

    transition: all 0.2s ease-out;
    cursor: pointer;
    &:hover {
      border-color: var(--button-button-secondary);
      background-color: var(--elements-on-surface-onsurface-dark);
    }

  }

  &__icon {
    display: flex;
  }

  &__positioner {
    outline: none;
  }

  &__popup {
    max-height: var(--available-height);
    overflow-y: auto;
    border-radius: var(--border-radius-border-radius-l);
    background-color: var(--elements-on-surface-onsurface-brand);
    padding-top: var(--spacing-spacing-sm-2);
    padding-bottom: var(--spacing-spacing-sm-2);
    transition-property: transform, scale, opacity;
    outline: none;
  }

  &__item {
    background-color: var(--elements-on-surface-onsurface-brand);
    cursor: pointer;
    padding-top: var(--spacing-spacing-sm-3);
    padding-bottom: var(--spacing-spacing-sm-3);
    padding-right: var(--spacing-spacing-m-2);
    padding-left: var(--spacing-spacing-m-1);
    font-size: 14px;
    user-select: none;
    outline: none;
    color: var(--text-text-invert);
    &[data-highlighted] {
      position: relative;
      z-index: 0;
      &::before {
        content: '';
        position: absolute;
        inset-inline: var(--spacing-spacing-sm-2);
        inset-block: 0;
        z-index: -1;
        border-radius: var(--border-radius-border-radius-sm);
        background-color: var(--elements-on-surface-onsurface-dark);
      }
    }

    .group[data-side='none'] & {
      min-width: calc(var(--anchor-width) + 4rem);
      padding-right: var(--spacing-spacing-l-3);
      font-size: 16px;
      line-height: 1;
    }
  }
}
