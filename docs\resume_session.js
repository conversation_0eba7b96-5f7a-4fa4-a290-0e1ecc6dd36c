document.addEventListener("DOMContentLoaded", function () {
  const maxExpirationMinutes = 60 * 24
  for (const [i, turn] of sessionDocument?.conversation_history?.entries() ?? []) {
      const promptData = turn.prompt
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;")
      .replace(/\n/g, "<br>")

      const mockResponseData = {
        ready: true,
        response: turn.response,
        response_id: `${sessionDocument.id}-${i}`,
        response_time_utc: turn.response_time_utc

      }

      createMessage("person", "prompts", promptData, false, new Date(turn.prompt_time_utc))
      handleResponse(promptData, mockResponseData, converter, sessionDocument.id)
  }

  const expiredFiles = new Map()
  for (const fileInfo of sessionDocument?.file_history ?? []) {
    const fileNameWithTimestamp = fileInfo.gcs_path.split('/').at(-1)
    const fileName = fileNameWithTimestamp.split('__').slice(1).join('__')

    const createdDate = new Date(fileInfo.upload_time_utc)
    const now = new Date()
    const timeDifference = now.getTime() - createdDate.getTime()
    const minuteDifference = timeDifference / 1000 / 60

    if (minuteDifference >= maxExpirationMinutes) {
      expiredFiles.set(fileName, fileInfo)
    } else {
      expiredFiles.delete(fileName)
    }
  }

  if (expiredFiles.size > 0) {
    const expiredMessageLines = Array.from(expiredFiles.entries()).map(kv => `${kv[0]} (${kv[1].size_bytes} bytes)`)
    let expiredFilesMessage = `The following file${expiredFiles.size > 1 ? 's have expired' : ' has expired'}`
    expiredFilesMessage = `${expiredFilesMessage} and must be re-uploaded for this session to use them:\n\n${expiredMessageLines.join('\n')}`
    window.setTimeout(() => {
      alert(expiredFilesMessage)
    }, 1000)
  }
})
