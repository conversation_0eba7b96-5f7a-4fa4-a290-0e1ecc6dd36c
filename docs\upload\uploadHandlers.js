function handleFileResponse(data) {
  const signed_urls = data["signed_urls"] || []
  let url_list = [];
  let gcs_uri_list = [];
  for (let i = 0; i < signed_urls.length; i++) {
    let gcs_uri = signed_urls[i]["gs_uri"];
    let mime_type = signed_urls[i]["mime_type"];
    let file_size = signed_urls[i]["file_size"];
    let upload_time_utc = signed_urls[i]["upload_time_utc"];
    let url = signed_urls[i]["url"];

    url_list.push({
      url: url,
      mime_type: mime_type,
    });

    gcs_uri_list.push({
      gcs_path: gcs_uri,
      mime_type: mime_type,
      file_size: file_size,
      upload_time_utc: upload_time_utc,
    });

    if (data.firestore_session_id) {
      document.getElementById("firestoreSessionId").value = data.firestore_session_id;
    }
  }

  return [url_list, gcs_uri_list];
}

/**
 * Wraps the file upload process, sending the files to Google Cloud Storage (GCS).
 * @param {FormData} form - The file upload form data.
 * @param {Array} url_list - The list of URLs and MIME types for the uploaded files.
 * @returns {Promise} - A promise that resolves when the files have been uploaded to GCS.
 */
function uploadWrapper(form, url_list) {
  console.log("Sending file(s) to GCS");
  const result = uploadFilesDirectly(form, url_list);
  return result;
}

/**
 * Uploads the files directly to Google Cloud Storage (GCS).
 * @param {FormData} form - The file upload form data.
 * @param {Array} url_list - The list of URLs and MIME types for the uploaded files.
 * @returns {Promise} - A promise that resolves when the files have been uploaded to GCS.
 */
async function uploadFilesDirectly(form, url_list) {
  let i = 0;
  let val = false;
  for (const [key, value] of form.entries()) {
    if (value instanceof File) {
      val = uploadFileHandler(value, url_list[i]); // Await the uploadFileHandler
      i++;
    }
  }
  return val;
}

/**
 * Handles the upload of a single file to Google Cloud Storage (GCS).
 * @param {File} file - The file to upload.
 * @param {Object} mime_url - An object containing the URL and MIME type of the file.
 * @returns {Promise} - A promise that resolves when the file has been uploaded to GCS.
 */
function uploadFileHandler(file, mime_url) {
  // const formData = new FormData();
  // formData.append("file", file);
  // console.log(mime_url);
  let url = mime_url["url"];
  let mime_type = mime_url["mime_type"];

  const result = fetch(url, {
    method: "PUT",
    headers: { "Content-Type": mime_type },
    body: file,
  })
    .then((response) => {
      if (!response.ok) {
        console.error("Failure to upload files");
        throw new Error(
          `HTTP status: ${response.status} ${response.statusText}`
        );
      }
    })
    .catch((error) => {
      console.error("Fetch error:", error);
      setUploadError(error);
    });
  return result;
}

function setUploadError(error) {
  uploadErrorFlag = true;
  const uploadStatusAlert = document.getElementById("upload-status-alert");
  uploadStatusAlert.style.display = "block";
  console.error("Upload failed:", error);
  uploadStatusAlert.setAttribute("style", "white-space: pre;");
  uploadStatusAlert.textContent = `Upload request failed. Please try again.\r\n${error}`;
  uploadStatusAlert.className = "alert-failure";
}

// Upload checkers

function checkIfShouldAttest() {
  const domainsToAttest = ["ucci.com"];
  let test_email = email.toLowerCase();
  for (const domain of domainsToAttest) {
    let check_email = test_email.endsWith(`@${domain}`);
    return check_email;
  }
}

function showLegalAttestation() {
  //   let attestPopup = document.getElementById("upload-attestation");
  if (!checkIfShouldAttest()) {
    // attestPopup.style.display = "none";
    return true;
  }
  let attestation_language = `We are prohibited from uploading any Active-Duty Dental Program (ADDP) and TRICARE Dental Program (TDP) data into Sidekick.

By clicking OK, I attest that my upload does not contain ADDP or TDP data and understand that uploading such data may result in disciplinary action, including termination of employment.`;
  let attestation = false;
  if (getGenericCookie("ucdAttestation") == "true") {
    attestation = true;
  } else {
    attestation = confirm(attestation_language);
    setGenericCookie("ucdAttestation", true, 720);
  }

  if (attestation) {
    sendLegalAttestation(attestation_language);
  }
  return attestation;
}

let fileAndMime = []; //Global var for tracking file names.
function sendLegalAttestation(attestation_language) {
  console.log("Files attested for");
  let current_url = window.location.href;
  let sessionType = "";
  if (current_url.includes("sidekick/translate")) {
    sessionType = "translate";
  } else {
    sessionType = "chat";
  }
  console.log(fileAndMime);
  let fetch_body = JSON.stringify({
    files_mimes: fileAndMime,
    session_type: sessionType,
    attestation_text: attestation_language,
  });

  console.log(fetch_body);

  let fetch_options = {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: fetch_body,
  };

  fetch(fileAttestLink, fetch_options);
}
