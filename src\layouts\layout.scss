.layout-main {
    height: 100vh;
    display: flex;
    flex-shrink: 0;
    
    .layout-sidebar-container {
        width: 250px;

    }
    .layout-content-container {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow-y: scroll;
        .layout-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            align-self: stretch;
            flex: 1;
            overflow: hidden;
            .layout-content-outlet {
                width: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                flex: 1;
                overflow-y: auto;
            }
        }
    }
}
