import React, { useState, useEffect, useRef } from 'react';
import {
  getLanguages,
  translateText,
  signFiles,
  uploadFile,
  translateFile,
  detectLanguage,
} from '@/api/translateApi';
import './TranslateView.scss';
import {
  MdOutlineFileDownload,
  MdContentCopy,
  MdArrowForward,
  MdTranslate,
  MdAdd,
  MdDelete,
} from 'react-icons/md';
import {
  TranslateDefaults,
  TranslateParams,
  TranslateLanguagesJSON,
  TranslateUploadFile,
  SignedURLInfo,
  TranslateFile,
  TranslateFileParams,
} from '../translateTypes';
import SidekickDropDown from '@/components/common/SidekickDropDown';
import { showToast } from '@/components/common/ToastNotification/ToastNotification';
import { ucdAttestation } from '@/api/ucdAttestationApi';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '@/store/slices/authSlice';

const acceptedFiles = ['.ppt', '.pptx', '.pdf', '.doc', '.docx', '.xls', '.xlsx'];
const filterValidFiles = (files: File[]): File[] => {
  const maxFileSize = 1024 * 1024 * 1024; // 1GB in bytes
  const validFiles: File[] = [];

  for (const file of files) {
    let isValid = true;

    if (file.size > maxFileSize) {
      showToast.error('File too large', `${file.name} exceeds 1GB limit`, { autoClose: 10000 });
      isValid = false;
    }

    const fileExtension = file.name.includes('.')
      ? file.name.toLowerCase().substring(file.name.lastIndexOf('.'))
      : '';
    if (!acceptedFiles.includes(fileExtension)) {
      showToast.error('Invalid file type', `${file.name} is not a supported file format`, {
        autoClose: 10000,
      });
      isValid = false;
    }

    if (isValid) {
      validFiles.push(file);
    }
  }

  return validFiles;
};

const upload = async (files: File[], signedInfo: SignedURLInfo[]) => {
  const uploadPromises = [];
  for (let i = 0; i < signedInfo.length; i++) {
    uploadPromises.push(uploadFile(files[i], signedInfo[i]));
  }
  return Promise.all(uploadPromises);
};

const TranslateView: React.FC = () => {
  const currentUser = useSelector(selectCurrentUser);
  const isUCD = Boolean(currentUser?.includes('@ucci.com'));

  const defaults: TranslateDefaults = {
    message: '',
    inputLanguage: '',
    outputLanguage: 'en',
    inputLanguageName: 'Auto Detect',
    outputLanguageName: 'English',
  };

  const fileInputRef = useRef<HTMLInputElement>(null);

  const [languagesData, setLanguagesData] = useState<TranslateLanguagesJSON>([]); //from backend
  const [message, setMessage] = useState(defaults.message);
  const [output, setOutput] = useState(defaults.message);
  const [inputLanguage, setInputLanguage] = useState(defaults.inputLanguage);
  const [outputLanguage, setOutputLanguage] = useState(defaults.outputLanguage);
  const [filesToTranslate, setFilesToTranslate] = useState<TranslateFile[]>([]);
  const [filesToDownload, setFilesToDownload] = useState<string[]>([]);
  const [disabledTranslate, setDisabledTranslate] = useState(true);
  const [disabledInput, setDisabledInput] = useState(false);

  const payloadText: TranslateParams = {
    message: message,
    inputLanguage: inputLanguage,
    outputLanguage: outputLanguage,
  };

  const payloadFile: TranslateFileParams = {
    translateFiles: filesToTranslate,
    target_language: outputLanguage,
  };

  useEffect(() => {
    const fetchLanguageOptions = async () => {
      const availableLanguages = await getLanguages();
      setLanguagesData(availableLanguages);
    };
    fetchLanguageOptions();
  }, []);

  const selectLanguages = languagesData
    ? languagesData.map(language => ({
        name: language.language_name,
        value: language.language_code,
      }))
    : [];

  const switchIO = () => {
    if (filesToTranslate.length == 0) {
      const tempMessage = message;
      setMessage(output);
      setOutput(tempMessage);
      if (inputLanguage !== '') {
        const tempInputLanguage = inputLanguage;
        setInputLanguage(outputLanguage);
        setOutputLanguage(tempInputLanguage);
      }
    }
  };
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFilesToTranslate([]);
    setMessage(e.target.value);
    setDisabledTranslate(!Boolean(e.target.value.trim().length));
  };

  const handleTranslate = async () => {
    setDisabledTranslate(true);

    if (filesToTranslate.length === 0) {
      detectLanguage(payloadText).then(result => {
        setInputLanguage(result.language_code);
      });

      const newOutput = (await translateText(payloadText))[0];
      setOutput(newOutput);
    } else {
      showToast.info('Translating file...', '');
      const response = await translateFile(payloadFile);
      const downloadURL = response.download_url;
      if (!downloadURL) {
        const maximumPages =
          'Error during file translation: 400 Exceed the maximum PDF page support';
        if (response.message.includes(maximumPages)) {
          showToast.error(
            'PDF page limit exceeded',
            'Maximum 20 pages for scanned PDFs, 300 pages for native PDFs'
          );
        } else {
          showToast.error('An unexpected error occurred.', response.message, { autoClose: 10000 });
        }
      } else {
        setOutput('Click Save Translation to download your files');
        showToast.positive('File translated', '');
        setFilesToDownload([downloadURL]);
      }
    }
    setDisabledTranslate(false);
  };

  const onFileClick = () => {
    // disable upload files temporarily? Clear textarea? show uploaded files in text area?
    fileInputRef.current?.click();
  };

  const onFilesChanged = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event?.currentTarget.files ?? []);
    const validFiles = filterValidFiles(files);

    const filesToSign: TranslateUploadFile[] = [];
    const filesToUpload: File[] = [];
    const translateFiles: TranslateFile[] = [];
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    const userAttested = await ucdAttestation(isUCD, validFiles, 'translate');
    if (validFiles.length > 0 && userAttested) {
      setDisabledTranslate(true);
      setDisabledInput(true);
      setMessage('');
      setOutput('');

      for (const file of files) {
        const filesToTranslate: TranslateUploadFile = {
          file_name: file.name,
          file_size: file.size,
          mime_type: file.type,
        };
        filesToSign.push(filesToTranslate);
        setMessage(oldMessage => `${oldMessage}${file.name} being uploaded\n`);
        // TODO filter out files we cant use, files have to be under 1GB.
        filesToUpload.push(file);
      }

      const signedUrls = (await signFiles(filesToSign)).signed_urls;
      signedUrls.forEach((signedUrl, index) => {
        const translateFile: TranslateFile = {
          fileName: filesToSign[index].file_name,
          gcs_uri: signedUrl.gs_uri,
          mime_type: signedUrl.mime_type,
          file_size: signedUrl.file_size,
        };
        translateFiles.push(translateFile);
      });

      setMessage('');
      setOutput('Click Translate to begin file translation');
      filesToSign.forEach(fileToSign => {
        setMessage(oldMessage => `${oldMessage}${fileToSign.file_name} uploaded\n`);
      });
      try {
        await upload(filesToUpload, signedUrls);
      } catch (error) {
        showToast.error(
          'Error uploading files',
          error instanceof Error ? error.message : 'Unknown error',
          { autoClose: 10000 }
        );
      }
      setFilesToTranslate(translateFiles);
      setDisabledTranslate(false);
    }
  };

  const filesPresent: boolean = filesToTranslate.length === 0;

  return (
    <div className="translate__container">
      <div className="translate__input-output">
        <div className="translate__input-output-select">
          <SidekickDropDown
            value={inputLanguage}
            placeholder={defaults.inputLanguageName}
            select={[
              { name: defaults.inputLanguageName, value: defaults.inputLanguage },
              ...selectLanguages,
            ]}
            onChange={value => {
              setInputLanguage(value);
            }}
          />
        </div>
        <textarea
          placeholder="Input text to translate..."
          disabled={disabledInput}
          value={message}
          onChange={handleInputChange}
          required
        />
        <div className="translate__button-group">
          <button hidden={!filesPresent} disabled={!filesPresent} onClick={onFileClick}>
            <MdAdd className="translate__button-svg" />
            <span className="translate__button-text">Attach a file</span>
          </button>
          <button
            hidden={filesPresent}
            disabled={filesPresent || disabledTranslate}
            className="translate__button-remove"
            onClick={() => {
              setMessage('');
              setOutput('');
              setDisabledTranslate(true);
              setDisabledInput(false);
              setFilesToTranslate([]);
              setFilesToDownload([]);
              if (fileInputRef.current) {
                fileInputRef.current.value = '';
              }
            }}
          >
            <MdDelete className="translate__button-svg" />
            <span className="translate__button-text">Remove file</span>
          </button>
          <input
            ref={fileInputRef}
            type="file"
            multiple={false}
            hidden
            onChange={onFilesChanged}
            accept={acceptedFiles.join(', ')}
          />
          <button
            disabled={disabledTranslate}
            className="translate__button-translate"
            onClick={handleTranslate}
          >
            <MdTranslate className="translate__button-svg" />
            <span className="translate__button-text">Translate</span>
          </button>
        </div>
      </div>
      <div className="translate__switch">
        <button tabIndex={-1} onClick={switchIO}>
          <MdArrowForward className="translate__switch-arrow" />
        </button>
      </div>
      <div className="translate__input-output">
        <div className="translate__input-output-select">
          <SidekickDropDown
            value={outputLanguage}
            placeholder={defaults.outputLanguageName}
            select={selectLanguages}
            onChange={value => {
              setOutputLanguage(value);
            }}
          />
        </div>
        <textarea placeholder="Your translation will appear here..." readOnly value={output} />
        <div className="translate__button-group">
          <button
            onClick={() => {
              filesToDownload.forEach(fileURL => {
                window.open(fileURL, '_blank', 'noopener noreferrer');
              });
            }}
            disabled={filesToDownload.length === 0}
          >
            <MdOutlineFileDownload className="translate__button-svg" />
            <span className="translate__button-text">Save Translation</span>
          </button>
          <button
            onClick={() => {
              navigator.clipboard.writeText(output);
            }}
            className="translate__button-copy"
          >
            <MdContentCopy className="translate__button-svg" />
            <span className="translate__button-text">Copy text</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default TranslateView;
