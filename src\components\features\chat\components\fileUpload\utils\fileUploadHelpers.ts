import { v4 as uuidv4 } from 'uuid';
import { FileRejection } from 'react-dropzone';
import { ValidatedFile } from '@/types/fileUpload';
import { validateFile } from './fileUploadUtils';

/**
 * Creates a ValidatedFile object from a File object
 */
export const createValidatedFileFromFile = (file: File): ValidatedFile => {
  const validationResult = validateFile(file);
  const status: ValidatedFile['uploadStatus'] = validationResult.isValid ? 'pending' : 'error';

  return {
    name: file.name,
    size: file.size,
    type: file.type,
    lastModified: file.lastModified,
    webkitRelativePath: file.webkitRelativePath || '',
    originalFile: file,
    id: uuidv4(),
    validationError: validationResult.isValid ? undefined : validationResult.error,
    uploadStatus: status,
    gcs_uri: undefined,
  };
};

/**
 * Creates a ValidatedFile object from a rejected file
 */
export const createValidatedFileFromRejection = (rejection: FileRejection): ValidatedFile => {
  const rejectedFile = rejection.file;
  return {
    name: rejectedFile.name,
    size: rejectedFile.size,
    type: rejectedFile.type,
    lastModified: rejectedFile.lastModified,
    webkitRelativePath: rejectedFile.webkitRelativePath || '',
    originalFile: rejectedFile,
    id: uuidv4(),
    validationError: rejection.errors.map(e => e.message).join(', '),
    uploadStatus: 'error' as const,
    gcs_uri: undefined,
  };
};

/**
 * Checks if the current chat category allows file uploads
 */
export const checkModelRestriction = (selectedChatType: string): boolean => {
  return selectedChatType === 'General';
};

/**
 * Checks if the file upload UI components should be shown for the current chat category
 */
export const shouldShowFileUploadUI = (selectedChatType: string): boolean => {
  return selectedChatType === 'General';
};

/**
 * Updates a specific file in the files array
 */
export const updateFileInList = (
  files: ValidatedFile[],
  fileId: string,
  updates: Partial<ValidatedFile>
): ValidatedFile[] => {
  return files.map(f => (f.id === fileId ? { ...f, ...updates } : f));
};

/**
 * Removes a file from the files array
 */
export const removeFileFromList = (files: ValidatedFile[], fileId: string): ValidatedFile[] => {
  return files.filter(f => f.id !== fileId);
};

/**
 * Maps chat model to session type for API calls
 */
export const mapChatTypeToSessionType = (chatType: string): string => {
  switch (chatType) {
    case 'General':
      return 'chat';
    case 'Code':
      return 'code';
    case 'Medical':
      return 'mdlm';
    default:
      return 'chat';
  }
};

/**
 * Filters files that are ready for upload
 */
export const getFilesReadyForUpload = (
  files: ValidatedFile[],
  isCancellationRequested: (fileId: string) => boolean
): ValidatedFile[] => {
  return files.filter(
    f =>
      f.uploadStatus === 'pending' &&
      !f.validationError &&
      !f.isCancellationRequested &&
      !isCancellationRequested(f.id)
  );
};

/**
 * Checks if any file is currently uploading
 */
export const hasUploadingFiles = (
  files: ValidatedFile[],
  isFileUploadingToGCS: (fileId: string) => boolean
): boolean => {
  return files.some(f => f.uploadStatus === 'uploading' || isFileUploadingToGCS(f.id));
};

