import React, { Suspense } from 'react';
// Import styles at the wrapper level to avoid lazy loading CSS issues
import './MermaidStyles.scss';

const MermaidRenderer = React.lazy(() => import('./MermaidRenderer'));

export interface MermaidRendererProps {
  code: string;
}

// Loading fallback component
const MermaidLoadingFallback = () => (
  <div className="mermaid-container mermaid-loading">
    <div className="mermaid-loading-indicator">Loading Mermaid renderer...</div>
  </div>
);

// Error boundary for Mermaid rendering
class MermaidErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('[Mermaid Error Boundary]', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="mermaid-container mermaid-error">
          <div className="mermaid-error-display">
            <div>
              <div className="error-title">⚠️ Mermaid Component Error</div>
              <div className="error-message">Failed to load or render the Mermaid component</div>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Main lazy wrapper component
const MermaidRendererLazy: React.FC<MermaidRendererProps> = ({ code }) => {
  // Don't render anything if there's no code
  if (!code.trim()) {
    return null;
  }

  return (
    <MermaidErrorBoundary>
      <Suspense fallback={<MermaidLoadingFallback />}>
        <MermaidRenderer code={code} />
      </Suspense>
    </MermaidErrorBoundary>
  );
};

export default MermaidRendererLazy;
