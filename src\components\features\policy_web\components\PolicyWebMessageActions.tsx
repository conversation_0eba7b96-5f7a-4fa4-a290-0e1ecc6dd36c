import React, { useState } from 'react';
import { IoCopyOutline, IoCheckmarkOutline } from 'react-icons/io5';
import { HiOutlineThumbUp, HiOutlineThumbDown } from 'react-icons/hi';
import clsx from 'clsx';
import { sendPolicyWebFeedback } from '@/api/policyWebApi';
import { useAppDispatch } from '@/store/hooks';
import { setMessageFeedback } from '@/store/slices/chatSlice';
import { showToast } from '@/components/common/ToastNotification/ToastNotification';

interface PolicyWebMessageActionsProps {
  isHovered: boolean;
  messageText: string;
  sessionId: string;
  responseId: string;
  responseClass?: any;
  feedback?: number | null;
  messageId?: string;
}

const PolicyWebMessageActions: React.FC<PolicyWebMessageActionsProps> = ({
  isHovered,
  messageText,
  sessionId,
  responseId,
  responseClass,
  feedback,
  messageId,
}) => {
  const dispatch = useAppDispatch();
  const [copied, setCopied] = useState(false);
  const [currentFeedback, setCurrentFeedback] = useState<number | null>(feedback ?? null);
  const [isSubmittingFeedback, setIsSubmittingFeedback] = useState(false);

  const handleCopy = (e: React.MouseEvent) => {
    e.stopPropagation();
    navigator.clipboard.writeText(messageText);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const handleFeedback = async (likeOrDislike: 1 | 0) => {
    if (isSubmittingFeedback || currentFeedback !== null) return;

    setIsSubmittingFeedback(true);
    try {
      await sendPolicyWebFeedback(sessionId, responseId, likeOrDislike, responseClass);
      setCurrentFeedback(likeOrDislike);

      if (messageId) {
        dispatch(setMessageFeedback({ messageId, feedback: likeOrDislike }));
      }

      showToast.positive('Thank You!', 'Your feedback has been submitted successfully.');

      console.log('PolicyWeb feedback submitted successfully');
    } catch (error) {
      console.error('Failed to submit PolicyWeb feedback:', error);
      showToast.error('Feedback Failed', 'Failed to submit feedback. Please try again.');
    } finally {
      setIsSubmittingFeedback(false);
    }
  };

  const handleThumbsUp = () => handleFeedback(1);
  const handleThumbsDown = () => handleFeedback(0);

  const hasFeedback = currentFeedback === 0 || currentFeedback === 1;
  const isLiked = currentFeedback === 1;
  const isDisliked = currentFeedback === 0;
  const iconStyle = 'w-4.5 h-4.5 text-white';
  const roundButtonBase = 'flex items-center justify-center w-8 h-8 rounded-full transition-colors';
  const roundButtonDefaultBg = 'bg-[#002D4F] hover:bg-[#00417A]';
  const roundButtonDisabled = 'opacity-50 cursor-not-allowed';

  const copyButtonStyle = clsx(roundButtonBase, roundButtonDefaultBg, 'text-white');
  const likeButtonStyle = clsx(
    roundButtonBase,
    isLiked ? 'bg-blue-600 text-white' : roundButtonDefaultBg,
    (isLiked || isSubmittingFeedback) && roundButtonDisabled,
    isDisliked && 'hidden'
  );
  const dislikeButtonStyle = clsx(
    roundButtonBase,
    isDisliked ? 'bg-red-600 text-white' : roundButtonDefaultBg,
    (isDisliked || isSubmittingFeedback) && roundButtonDisabled,
    isLiked && 'hidden'
  );

  return (
    <div
      className={clsx(
        'policy-message-actions',
        'absolute',
        'bottom-1',
        'left-1',
        'flex',
        'flex-wrap',
        'items-center',
        'gap-2',
        'py-1',
        'px-1',
        'transition-opacity',
        'duration-300',
        'ease-in-out',
        hasFeedback ? 'opacity-100' : isHovered ? 'opacity-100' : 'opacity-0 pointer-events-none'
      )}
    >
      <button
        onClick={handleCopy}
        className={clsx(
          'policy-message-actions__button policy-message-actions__button--copy',
          copyButtonStyle,
          'font-roboto'
        )}
        title={copied ? 'Copied!' : 'Copy message'}
      >
        {copied ? (
          <IoCheckmarkOutline className={clsx('policy-message-actions__icon', iconStyle)} />
        ) : (
          <IoCopyOutline className={clsx('policy-message-actions__icon', iconStyle)} />
        )}
      </button>
      <button
        onClick={handleThumbsUp}
        className={clsx(
          'policy-message-actions__button policy-message-actions__button--like',
          likeButtonStyle,
          'font-roboto'
        )}
        title="Thumbs up"
        disabled={isLiked || isSubmittingFeedback}
      >
        <HiOutlineThumbUp className={clsx('policy-message-actions__icon', iconStyle)} />
      </button>
      <button
        onClick={handleThumbsDown}
        className={clsx(
          'policy-message-actions__button policy-message-actions__button--dislike',
          dislikeButtonStyle,
          'font-roboto'
        )}
        title="Thumbs down"
        disabled={isDisliked || isSubmittingFeedback}
      >
        <HiOutlineThumbDown className={clsx('policy-message-actions__icon', iconStyle)} />
      </button>
    </div>
  );
};

export default PolicyWebMessageActions;
