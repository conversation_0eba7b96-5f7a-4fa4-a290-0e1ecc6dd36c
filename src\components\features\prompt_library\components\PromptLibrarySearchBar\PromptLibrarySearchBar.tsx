import React from 'react';

import SearchIcon from '@/components/common/icons/SearchIcon';

import { useThemeStyles } from '@/hooks/useThemeStyles';
import './PromptLibrarySearchBar.scss';

interface PromptLibrarySearchBarProps {
  onSearch: (query: string) => void;
  searchQuery: string;
}

const PromptLibrarySearchBar: React.FC<PromptLibrarySearchBarProps> = ({
  onSearch,
  searchQuery,
}) => {
  const { classes } = useThemeStyles();

  return (
    <div className={`promptLibrary__search-container ${classes.backgroundInput} ${classes.borderPrimary}`}>
      <input
        type="search"
        placeholder='Search prompt library by text...'
        value={searchQuery}
        onChange={e => onSearch(e.target.value)}
        className={`promptLibrary__search-input ${classes.text}`}
      />
      <div className="promptLibrary__search-icon-container">
        <SearchIcon />
      </div>
    </div>
  );
};

export default PromptLibrarySearchBar;
