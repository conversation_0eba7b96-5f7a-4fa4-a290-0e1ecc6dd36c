export interface PowerPointOption {
  value: string;
  text: string;
}

export interface DropdownPositionState {
  top: number;
  left: number;
  width: number;
  orientation: 'up' | 'down';
}

/**
 * Available PowerPoint template options
 */
export const POWERPOINT_OPTIONS: PowerPointOption[] = [
  { value: 'Allegheny Health Network', text: 'Allegheny Health Network' },
  { value: 'enGen', text: 'enGen' },
  { value: 'Highmark Health', text: 'Highmark Health' },
  { value: 'Highmark', text: 'Highmark' },
  { value: 'Highmark Blue Shield', text: 'Highmark Blue Shield' },
  {
    value: 'Highmark Blue Cross Blue Shield',
    text: 'Highmark Blue Cross Blue Shield',
  },
  {
    value: 'UCD',
    text: 'United Concordia Dental',
  },
];

/**
 * Common button styles for export buttons
 */
export const EXPORT_BUTTON_STYLES = {
  base: 'flex items-center justify-center gap-2 px-3 py-1.5 rounded-full text-sm transition-all duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500',
  active: 'bg-[#003865] hover:bg-[#004b87] active:bg-[#005c9e] text-white',
  disabled: 'bg-gray-400 cursor-not-allowed text-gray-700',
  icon: 'w-4.5 h-4.5',
} as const;

/**
 * PowerPoint dropdown styles
 */
export const POWERPOINT_DROPDOWN_STYLES = {
  dropdown:
    'origin-top-left rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none',
  dropdownUp:
    'origin-bottom-left rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none',
  listItem:
    'ppt-export__option px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 cursor-pointer',
  listItemSelected: 'ppt-export__option--selected bg-blue-50 text-blue-700 font-semibold',
  arrow: 'ppt-export__arrow w-4.5 h-4.5 transform transition-transform duration-200',
} as const;

/**
 * Dropdown dimensions for position calculations
 */
export const DROPDOWN_DIMENSIONS = {
  estimatedItemHeight: 36,
  containerPadding: 8,
  dropdownBorderWidth: 2,
  marginFromButton: 4,
  viewportMargin: 20,
} as const;

/**
 * Checks if the export UI components (PowerPoint, Word) should be shown for the current model
 * Export features are not available for Code and Policy models
 */
export const shouldShowExportUI = (selectedChatType: string): boolean => {
  return selectedChatType === 'General' || selectedChatType === 'Medical';
};

/**
 * Calculates smart dropdown position based on button element and available space
 * Automatically determines whether to show dropdown above or below the button
 */
export const calculateDropdownPosition = (
  buttonElement: HTMLButtonElement,
  optionsCount: number = POWERPOINT_OPTIONS.length
): DropdownPositionState => {
  const rect = buttonElement.getBoundingClientRect();
  const {
    estimatedItemHeight,
    containerPadding,
    dropdownBorderWidth,
    marginFromButton,
    viewportMargin,
  } = DROPDOWN_DIMENSIONS;

  const estimatedDropdownHeight =
    optionsCount * estimatedItemHeight + containerPadding + dropdownBorderWidth;

  const viewportHeight = window.innerHeight;
  const spaceBelow = viewportHeight - rect.bottom - viewportMargin;
  const spaceAbove = rect.top - viewportMargin;

  let orientation: 'up' | 'down';

  const canFitBelow = spaceBelow >= estimatedDropdownHeight;
  const canFitAbove = spaceAbove >= estimatedDropdownHeight;

  if (canFitBelow) {
    orientation = 'down';
  } else {
    if (canFitAbove) {
      orientation = 'up';
    } else {
      orientation = spaceAbove > spaceBelow ? 'up' : 'down';
    }
  }

  let top;
  if (orientation === 'up') {
    top = rect.top + window.scrollY - estimatedDropdownHeight - marginFromButton;
  } else {
    top = rect.bottom + window.scrollY + marginFromButton;
  }

  return {
    top,
    left: rect.left + window.scrollX,
    width: rect.width,
    orientation,
  };
};

/**
 * Gets export button text based on loading state
 */
export const getExportButtonText = (
  isLoading: boolean,
  exportType: 'PowerPoint' | 'Word'
): string => {
  return isLoading ? 'Exporting...' : `Export to ${exportType}`;
};

/**
 * Gets icon style classes based on loading state and export type
 */
export const getExportIconStyle = (
  isLoading: boolean,
  exportType: 'PowerPoint' | 'Word'
): string => {
  const baseStyle = EXPORT_BUTTON_STYLES.icon;

  if (exportType === 'PowerPoint') {
    return isLoading ? `${baseStyle} animate-spin` : `${baseStyle} text-orange-400`;
  } else {
    return isLoading ? `${baseStyle} animate-spin text-white` : `${baseStyle} text-blue-300`;
  }
};
