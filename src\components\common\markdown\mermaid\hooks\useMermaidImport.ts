import { useState, useEffect, useRef } from 'react';

interface DynamicImportState<T> {
  loading: boolean;
  data: T | null;
  error: Error | null;
}

interface DynamicImportOptions {
  retryCount?: number;
  retryDelay?: number;
}

/**
 * Hook for dynamic imports with loading states and error handling
 */
function useDynamicImport<T>(
  importFn: () => Promise<T>,
  options: DynamicImportOptions = {}
): DynamicImportState<T> & { retry: () => void } {
  const [state, setState] = useState<DynamicImportState<T>>({
    loading: false,
    data: null,
    error: null,
  });

  const { retryCount = 3, retryDelay = 1000 } = options;
  const retryCountRef = useRef(0);
  const mountedRef = useRef(true);

  const loadModule = async () => {
    if (!mountedRef.current) return;

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const module = await importFn();
      
      if (mountedRef.current) {
        setState({
          loading: false,
          data: module,
          error: null,
        });
        retryCountRef.current = 0;
      }
    } catch (error) {
      if (!mountedRef.current) return;

      const err = error instanceof Error ? error : new Error('Import failed');
      
      if (retryCountRef.current < retryCount) {
        retryCountRef.current++;
        setTimeout(() => {
          if (mountedRef.current) {
            loadModule();
          }
        }, retryDelay);
      } else {
        setState({
          loading: false,
          data: null,
          error: err,
        });
      }
    }
  };

  const retry = () => {
    retryCountRef.current = 0;
    loadModule();
  };

  useEffect(() => {
    mountedRef.current = true;
    loadModule();

    return () => {
      mountedRef.current = false;
    };
  }, []);

  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  return {
    ...state,
    retry,
  };
}

/**
 * Hook specifically for Mermaid dynamic import
 */
export function useMermaidImport() {
  return useDynamicImport(
    () => import('mermaid'),
    { retryCount: 2, retryDelay: 500 }
  );
}

/**
 * Hook specifically for react-zoom-pan-pinch dynamic import
 */
export function useZoomPanPinchImport() {
  return useDynamicImport(
    () => import('react-zoom-pan-pinch'),
    { retryCount: 2, retryDelay: 500 }
  );
}
