import datetime
import json
import traceback
from uuid import uuid4

import jwt
import requests

from website.config import get_config_val
from website.google_cloud_init import fetch_ENV_by_project_id
from website.logger_framework import make_logger
from website.models.Prompt import Prompt
from website.models.Response import Response
from website.utils import db_utils

# from website.utils import db_utils, timings # Are we saving this in history?

workflow = "fetch_workflow"

logger_info, logger_error = make_logger(workflow, __file__)


class PolicyWeb:
    from website.extensions import oidc_variables

    def __init__(self) -> None:
        ENV_VAL = fetch_ENV_by_project_id(self.oidc_variables["cloudrun_project_id"])
        self._auth_token = None
        self._auth_generated = datetime.datetime.now()
        self.policy_web_url = get_config_val("policy_web", ENV_VAL)

    def _token_invalid(self):
        if self._auth_token is None:
            return True

        # print(jwt.decode(self._auth_token, options={"verify_signature": False}))

        exp_time = datetime.datetime.fromtimestamp(
            jwt.decode(self._auth_token, options={"verify_signature": False})["exp"]
        )
        if datetime.datetime.now() > exp_time:
            return True
        return False

    @property
    def auth_token(self):
        try:
            if self._token_invalid():
                metadata_url = "http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/identity"
                headers = {"Metadata-Flavor": "Google"}
                params = {"audience": self.policy_web_url}
                response = requests.get(metadata_url, headers=headers, params=params)

                if response.status_code == 200:
                    logger_info.info(
                        "%s : policyweb.auth_token : Successfully generated policyweb token",
                        workflow,
                    )

                    self._auth_token = response.text
                else:
                    response.raise_for_status()

            return self._auth_token

        except Exception as err:
            logger_error.error(
                "%s : policyweb.auth_token : %s : %s",
                workflow,
                str(err),
                traceback.format_exc(),
            )
            logger_error.error(
                "%s : policyweb.auth_token : Failure to receive token. This is FATAL if not LOCAL.",
                workflow,
            )
            # Add in local token?

    def send_post_request(self, endpoint: str, user_input: dict):
        """Send post request to policy web endpoint"""
        headers = {
            "Authorization": f"Bearer {self.auth_token}",
            "accept": "application/json",
            "Content-Type": "application/json",
        }
        url = self.policy_web_url + endpoint
        logger_info.info(
            "%s : policyweb.send_post_request : Sending a request to %s", workflow, url
        )
        return_response = {
            "response": None,
            "response_success": False,
        }
        try:
            # raise Exception("hello world")
            response = requests.post(url, json=user_input, headers=headers)
            if response.ok:  # add try except
                return_response.update({"response": response.json()})
                return_response.update({"response_success": True})
                return return_response
            else:
                raise response.raise_for_status()
        except Exception as err:
            logger_error.error(
                "%s : policyweb.send_post_request : %s : %s",
                workflow,
                str(err),
                traceback.format_exc(),
            )
            logger_error.error(
                "%s : policyweb.send_post_request : Failure on POST to: %s",
                workflow,
                url,
            )
            # response_success is already false
            return_response.update(
                {
                    "response": f"Something went wrong. Please refresh and try again! Error: {err}"
                }
            )
            return return_response

    # Handle the try excepts in send_post_request not here
    def bq_table(self, user_input: dict):
        """Send request to bq_table endpoint"""
        bq_table_response = self.send_post_request("api/policies", user_input)
        return bq_table_response

    def qa_response(self, user_input: dict):
        """Send request to qa_response endpoint"""
        qa_response = self.send_post_request("api/qa_response", user_input)
        return qa_response


def add_policy_web_record(data_input, data_response, user):
    # This writes to BQ only in current state

    prompt = Prompt(  # TODO Refactor based on GCS removal
        id=data_input["prompt_id"],
        prompt_text=data_input["user_input"],
        prompt_time=data_input["prompt_time"],
        user=user,
        session_id=data_input["session_id"],
    )

    response = Response(  # TODO Refactor based on GCS removal
        prompt_id=data_response["prompt_id"],
        response_text=json.dumps(data_response["response"]),
        response_time=data_response["response_time"],
        user=user,
        prompt_session_id=data_response["session_id"],
    )
    # if current solution doesn't work need to return these .to_dicts() to frontend & store them in a jslist to then send to backend
    db_utils.add_BQ_policyweb_record(prompt.to_dict(), response.to_dict())
    return prompt.to_dict(), response.to_dict()


def handle_policyweb_session(data: dict, user: str):
    policy_session = data.get("session_id", None)
    if policy_session is None or policy_session == "null":
        policy_session = f"plcy{uuid4().hex}"
    prompt_id = uuid4().hex

    prompt_time = datetime.datetime.now()

    return_response = POLICYWEB.qa_response(data)
    data["prompt_id"] = prompt_id
    data["session_id"] = policy_session
    data["prompt_time"] = prompt_time
    return_response["prompt_id"] = prompt_id
    return_response["session_id"] = policy_session
    return_response["response_time"] = datetime.datetime.now()

    prompt, response = add_policy_web_record(data, return_response, user)

    return_response["response_class"] = (
        response  # Sending response back for feedback info, since we aren't storing this in GCS
    )

    return return_response


def handle_policyweb_feedback(feedback_request):
    response_class: dict = feedback_request["response_class"]

    info = True if feedback_request["info"] == 1 else False

    response_class.update({"response_feedback": {"boolean": info}})
    db_utils.update_BQ_response_feedback(response_class)


POLICYWEB = PolicyWeb()  # init auth headers on file import
