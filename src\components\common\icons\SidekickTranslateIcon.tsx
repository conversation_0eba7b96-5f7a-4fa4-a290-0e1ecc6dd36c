import React from 'react';

const SidekickTranslateIcon: React.FC = props => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="25"
      viewBox="0 0 24 25"
      fill="currentcolor"
      {...props}
    >
      <path d="M12.87 15.57L10.33 13.06L10.36 13.03C12.1 11.09 13.34 8.86 14.07 6.5H17V4.5H10V2.5H8V4.5H1V6.49H12.17C11.5 8.42 10.44 10.25 9 11.85C8.07 10.82 7.3 9.69 6.69 8.5H4.69C5.42 10.13 6.42 11.67 7.67 13.06L2.58 18.08L4 19.5L9 14.5L12.11 17.61L12.87 15.57ZM18.5 10.5H16.5L12 22.5H14L15.12 19.5H19.87L21 22.5H23L18.5 10.5ZM15.88 17.5L17.5 13.17L19.12 17.5H15.88Z" />
    </svg>
  );
};

export default SidekickTranslateIcon;
