@import 'tailwindcss';

/* Poppins */

/* 400 - normal */ 
@font-face {
    font-family: 'Poppins';
    src: url('../public/assets/fonts/Poppins/Poppins-Regular.woff2') format('woff2'),
         url('fonts/Poppins/Poppins-Regular.woff2') format('woff2'),
         url('../public/assets/fonts/Poppins/Poppins-Regular.ttf') format('truetype')
         url('fonts/Poppins/Poppins-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

/* 400 - italic */
@font-face {
    font-family: 'Poppins';
    src: url('../public/assets/fonts/Poppins/Poppins-Italic.woff2') format('woff2'),
         url('fonts/Poppins/Poppins-Italic.woff2') format('woff2'),
         url('../public/assets/fonts/Poppins/Poppins-Italic.ttf') format('truetype'),
         url('fonts/Poppins/Poppins-Italic.ttf') format('truetype');
    font-weight: 400;
    font-style: italic;
    font-display: swap;
}

/* 700 - normal (Bold) */
@font-face {
    font-family: 'Poppins';
    src: url('../public/assets/fonts/Poppins/Poppins-Bold.woff2') format('woff2'),
         url('fonts/Poppins/Poppins-Bold.woff2') format('woff2'),
         url('../public/assets/fonts/Poppins/Poppins-Bold.ttf') format('truetype'),
         url('fonts/Poppins/Poppins-Bold.ttf') format('truetype');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

/* 700 - italic */
@font-face {
    font-family: 'Poppins';
    src: url('../public/assets/fonts/Poppins/Poppins-BoldItalic.woff2') format('woff2'),
         url('fonts/Poppins/Poppins-BoldItalic.woff2') format('woff2'),
         url('../public/assets/fonts/Poppins/Poppins-BoldItalic.ttf') format('truetype'),
         url('fonts/Poppins/Poppins-BoldItalic.ttf') format('truetype');
    font-weight: 700;
    font-style: italic;
    font-display: swap;
}

/* Roboto */

/* 400 - normal */ 
@font-face {
    font-family: 'Roboto';
    src: url('../public/assets/fonts/Roboto/Roboto-Regular.woff2') format('woff2'),
         url('fonts/Roboto/Roboto-Regular.woff2') format('woff2'),
         url('../public/assets/fonts/Roboto/Roboto-Regular.ttf') format('truetype'),
         url('fonts/Roboto/Roboto-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

/* 400 - italic */
@font-face {
    font-family: 'Poppins';
    src: url('../public/assets/fonts/Roboto/Roboto-Italic.woff2') format('woff2'),
         url('fonts/Roboto/Roboto-Italic.woff2') format('woff2'),
         url('../public/assets/fonts/Roboto/Roboto-Italic.ttf') format('truetype'),
         url('fonts/Roboto/Roboto-Italic.ttf') format('truetype');
    font-weight: 400;
    font-style: italic;
    font-display: swap;
}

/* 700 - normal (Bold) */
@font-face {
    font-family: 'Poppins';
    src: url('../public/assets/fonts/Roboto/Roboto-Bold.woff2') format('woff2'),
         url('fonts/Roboto/Roboto-Bold.woff2') format('woff2'),
         url('../public/assets/fonts/Roboto/Roboto-Bold.ttf') format('truetype'),
         url('fonts/Roboto/Roboto-Bold.ttf') format('truetype');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

/* 700 - italic */
@font-face {
    font-family: 'Poppins';
    src: url('../public/assets/fonts/Roboto/Roboto-BoldItalic.woff2') format('woff2'),
         url('fonts/Roboto/Roboto-BoldItalic.woff2') format('woff2'),
         url('../public/assets/fonts/Roboto/Roboto-BoldItalic.ttf') format('truetype'),
         url('fonts/Roboto/Roboto-BoldItalic.ttf') format('truetype');
    font-weight: 700;
    font-style: italic;
    font-display: swap;
}

@theme {
    --font-roboto: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;
    --font-sofia-pro: 'Sofia Pro', 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;
}

/* Primitives - Value */
:root {
    --font-roboto: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;
    --font-sofia-pro: 'Sofia Pro', 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif; 
    --brand-hmk-primary-100: #ECF7FFFF;
    --brand-hmk-primary-200: #C5E6FFFF;
    --brand-hmk-primary-300: #55B7FFFF;
    --brand-hmk-primary-400: #0066B1FF;
    --brand-hmk-primary-500: #003963FF;
    --brand-hmk-primary-600: #00223CFF;
    --brand-hmk-primary-logo: #00A2E2FF;
    --brand-hmk-secondary-100: #FEF0ECFF;
    --brand-hmk-secondary-200: #FDE1D9FF;
    --brand-hmk-secondary-300: #F7987DFF;
    --brand-hmk-tertiary-100: #FFFDEBFF;
    --brand-hmk-tertiary-200: #FFFAC4FF;
    --brand-hmk-tertiary-300: #FFF689FF;
    --brand-hmk-shades-100: #F5F6F6FF;
    --brand-hmk-shades-200: #E0E2E4FF;
    --brand-hmk-shades-300: #B6BCC0FF;
    --brand-hmk-shades-400: #6D787FFF;
    --brand-hmk-shades-500: #495055FF;
    --brand-hmk-shades-600: #231F20FF;
    --brand-ahn-primary-100: #E8F8F0FF;
    --brand-ahn-primary-200: #CBEEDDFF;
    --brand-ahn-primary-250: #3CB97CFF;
    --brand-ahn-primary-300: #0A8348FF;
    --brand-ahn-primary-400: #00663DFF;
    --brand-ahn-primary-500: #003963FF;
    --brand-ahn-primary-600: #00223CFF;
    /* --brand-ahn-primary-500-(alt): #235338FF;
    --brand-ahn-primary-600-(alt): #183825FF; */
    --neutral-white: #FFFFFFFF;
    --neutral-100: #F5F6F6FF;
    --neutral-200: #E0E2E4FF;
    --neutral-300: #B6BCC0FF;
    --neutral-400: #6D787FFF;
    --neutral-500: #495055FF;
    --neutral-black: #000000FF;
    --status-positive-100: #E3FDF0FF;
    --status-positive-200: #0A884BFF;
    --status-positive-300: #054727FF;
    --status-negative-100: #FFE6ECFF;
    --status-negative-200: #D30034FF;
    --status-negative-300: #660019FF;
    --brand-ahn-secondary-100: #ECF7FFFF;
    --brand-ahn-secondary-200: #C5E6FFFF;
    --brand-ahn-secondary-300: #2EA6FFFF;
    --status-caution-100: #FFF6E0FF;
    --status-caution-200: #FFC939FF;
    --status-caution-300: #805C00FF;
    --brand-ahn-tertiary-100: #FFFDEBFF;
    --brand-ahn-tertiary-200: #FFFAC4FF;
    --brand-ahn-tertiary-300: #FFF689FF;
    --brand-ahn-shades-100: #F5F6F6FF;
    --brand-ahn-shades-200: #E5E8E7FF;
    --brand-ahn-shades-300: #BCC2C0FF;
    --brand-ahn-shades-400: #626C67FF;
    --brand-ahn-shades-500: #495055FF;
    --brand-hmig-primary-100: #E6ECEEFF;
    --brand-hmig-primary-200: #BFCED3FF;
    --brand-hmig-primary-300: #809FA9FF;
    --brand-hmig-primary-400: #406E7DFF;
    --brand-hmig-primary-500: #003E52FF;
    --brand-hmig-primary-600: #002E3DFF;
    --brand-hmig-secondary-100: #67CBDFFF;
    --brand-hmig-secondary-200: #2BB0CAFF;
    --brand-hmig-secondary-300: #1C7587FF;
    --brand-hmig-tertiary-100: #F3E1BFFF;
    --brand-hmig-tertiary-200: #DCA740FF;
    --brand-hmig-tertiary-300: #D08A00FF;
    --brand-hmig-shades-100: #F5F6F6FF;
    --brand-hmig-shades-200: #E0E2E4FF;
    --brand-hmig-shades-300: #B6BCC0FF;
    --brand-hmig-shades-400: #6D787FFF;
    --status-informational-100: #C5E6FFFF;
    --status-informational-200: #0066B1FF;
}

/* Increments - px */
:root {
    --0px: 0px;
    --1px: 1px;
    --2px: 2px;
    --4px: 4px;
    --8px: 8px;
    --12px: 12px;
    --16px: 16px;
    --24px: 24px;
    --32px: 32px;
    --40px: 40px;
    --48px: 48px;
    --64px: 64px;
    --80px: 80px;
    --96px: 96px;
    --160px: 160px;
    --999px: 999px;
}

/* Breakpoints - Mode 1 */
:root {
    --lg-max-width: 1920px;
    --lg-min-width: 1280px;
    --m-max-width: 1280px;
    --m-min-width: 600px;
    --sm-max-width: 600px;
    --sm-min-width: 320px;
}

/* Tokens - Highmark */
:root {
    --elements-surface-surface-primary: var(--neutral-white);
    --elements-surface-surface-secondary: var(--brand-hmk-primary-100);
    --elements-surface-surface-tertiary: var(--brand-hmk-primary-200);
    --border-color-border-highlight: var(--brand-hmk-primary-300);
    --border-color-border-interactive: var(--brand-hmk-primary-400);
    --border-color-border-pressed: var(--brand-hmk-primary-500);
    --border-color-border-light: var(--brand-hmk-shades-200);
    --border-color-border-medium: var(--brand-hmk-shades-300);
    --border-color-border-dark: var(--brand-hmk-shades-400);
    --border-weight-border-weight-s: var(--1px);
    --border-weight-border-weight-m: var(--2px);
    --border-weight-border-weight-l: var(--4px);
    --border-weight-border-weight-xl: var(--8px);
    --border-radius-border-radius-none: var(--0px);
    --border-radius-border-radius-sm: var(--2px);
    --border-radius-border-radius-m: var(--4px);
    --border-radius-border-radius-l: var(--8px);
    --border-radius-border-radius-xl: var(--12px);
    --border-radius-border-radius-xxl: var(--16px);
    --border-radius-border-radius-xxxl: var(--32px);
    --border-radius-border-radius-full: var(--999px);
    --button-button-primary: var(--brand-hmk-primary-400);
    --button-button-secondary: var(--brand-hmk-primary-500);
    --button-button-tertiary: var(--brand-hmk-primary-600);
    --button-button-invert: var(--neutral-white);
    --button-button-accent: var(--brand-hmk-primary-300);
    --button-button-disable-primary: var(--brand-hmk-shades-200);
    --button-button-disable-secondary: var(--brand-hmk-shades-300);
    --button-button-highlight-primary: var(--brand-hmk-primary-100);
    --button-button-highlight-secondary: var(--brand-hmk-primary-200);
    --text-text-body: var(--brand-hmk-primary-600);
    --text-text-secondary: var(--brand-hmk-shades-500);
    --text-text-interactive: var(--brand-hmk-primary-400);
    --text-text-heading: var(--brand-hmk-primary-500);
    --text-text-invert: var(--neutral-white);
    --text-text-disable: var(--neutral-400);
    --spacing-spacing-sm-1: var(--2px);
    --spacing-spacing-sm-2: var(--4px);
    --spacing-spacing-sm-3: var(--8px);
    --spacing-spacing-m-1: var(--12px);
    --spacing-spacing-m-2: var(--16px);
    --spacing-spacing-m-3: var(--24px);
    --spacing-spacing-l-1: var(--32px);
    --spacing-spacing-l-2: var(--40px);
    --spacing-spacing-l-3: var(--48px);
    --spacing-spacing-l-4: var(--64px);
    --spacing-spacing-xl-1: var(--80px);
    --spacing-spacing-xl-2: var(--96px);
    --spacing-spacing-xl-4: var(--160px);
    --elements-surface-surface-brand: var(--brand-hmk-primary-500);
    --elements-surface-surface-dark: var(--brand-hmk-primary-600);
    --elements-surface-surface-disabled: var(--brand-hmk-shades-200);
    --elements-on-surface-onsurface-primary: var(--neutral-white);
    --elements-on-surface-onsurface-secondary: var(--brand-hmk-primary-100);
    --elements-on-surface-onsurface-tertiary: var(--brand-hmk-primary-200);
    --elements-on-surface-onsurface-brand: var(--brand-hmk-primary-500);
    --elements-on-surface-onsurface-dark: var(--brand-hmk-primary-600);
    --elements-on-surface-onsurface-disabled: var(--brand-hmk-shades-200);
    --accent-accent-1-accent-1-primary: var(--brand-hmk-secondary-300);
    --accent-accent-1-accent-1-secondary: var(--brand-hmk-secondary-200);
    --accent-accent-1-accent-1-tertiary: var(--brand-hmk-secondary-100);
    --accent-accent-2-accent-2-primary: var(--brand-hmk-tertiary-300);
    --accent-accent-2-accent-2-secondary: var(--brand-hmk-tertiary-200);
    --accent-accent-2-accent-2-tertiary: var(--brand-hmk-tertiary-100);
    --status-positive-status-positive-primary: var(--status-positive-200);
    --status-positive-status-positive-secondary: var(--status-positive-300);
    --status-positive-status-positive-tertiary: var(--status-positive-100);
    --status-caution-status-caution-primary: var(--status-caution-200);
    --status-caution-status-caution-secondary: var(--status-caution-300);
    --status-caution-status-caution-tertiary: var(--status-caution-100);
    --status-critical-status-critical-primary: var(--status-negative-200);
    --status-critical-status-critical-secondary: var(--status-negative-300);
    --status-critical-status-critical-tertiary: var(--status-negative-100);
    --status-informational-status-info-primary: var(--status-informational-200);
    --status-informational-status-info-secondary: var(--status-informational-100);
    --shadow-shadow-interactive: var(--brand-hmk-shades-600);
}

/* Tokens - AHN */
/* :root {
    --elements-surface-surface-primary: var(--neutral-white);
    --elements-surface-surface-secondary: var(--brand-ahn-primary-100);
    --elements-surface-surface-tertiary: var(--brand-ahn-primary-200);
    --border-color-border-highlight: var(--brand-ahn-primary-250);
    --border-color-border-interactive: var(--brand-ahn-primary-400);
    --border-color-border-pressed: var(--brand-ahn-primary-500-(alt));
    --border-color-border-light: var(--brand-ahn-shades-200);
    --border-color-border-medium: var(--brand-ahn-shades-300);
    --border-color-border-dark: var(--brand-ahn-shades-400);
    --border-weight-border-weight-s: var(--1px);
    --border-weight-border-weight-m: var(--2px);
    --border-weight-border-weight-l: var(--4px);
    --border-weight-border-weight-xl: var(--8px);
    --border-radius-border-radius-none: var(--0px);
    --border-radius-border-radius-sm: var(--2px);
    --border-radius-border-radius-m: var(--4px);
    --border-radius-border-radius-l: var(--8px);
    --border-radius-border-radius-xl: var(--12px);
    --border-radius-border-radius-xxl: var(--16px);
    --border-radius-border-radius-xxxl: var(--32px);
    --border-radius-border-radius-full: var(--999px);
    --button-button-primary: var(--brand-ahn-primary-400);
    --button-button-secondary: var(--brand-ahn-primary-500-(alt));
    --button-button-tertiary: var(--brand-ahn-primary-600-(alt));
    --button-button-invert: var(--neutral-white);
    --button-button-accent: var(--brand-ahn-primary-250);
    --button-button-disable-primary: var(--brand-ahn-shades-200);
    --button-button-disable-secondary: var(--brand-ahn-shades-300);
    --button-button-highlight-primary: var(--brand-ahn-primary-100);
    --button-button-highlight-secondary: var(--brand-ahn-primary-200);
    --text-text-body: var(--brand-ahn-primary-600);
    --text-text-secondary: var(--brand-ahn-shades-500);
    --text-text-interactive: var(--brand-hmk-primary-400);
    --text-text-heading: var(--brand-ahn-primary-500);
    --text-text-invert: var(--neutral-white);
    --text-text-disable: var(--neutral-400);
    --spacing-spacing-sm-1: var(--2px);
    --spacing-spacing-sm-2: var(--4px);
    --spacing-spacing-sm-3: var(--8px);
    --spacing-spacing-m-1: var(--12px);
    --spacing-spacing-m-2: var(--16px);
    --spacing-spacing-m-3: var(--24px);
    --spacing-spacing-l-1: var(--32px);
    --spacing-spacing-l-2: var(--40px);
    --spacing-spacing-l-3: var(--48px);
    --spacing-spacing-l-4: var(--64px);
    --spacing-spacing-xl-1: var(--80px);
    --spacing-spacing-xl-2: var(--96px);
    --spacing-spacing-xl-4: var(--160px);
    --elements-surface-surface-brand: var(--brand-ahn-primary-500-(alt));
    --elements-surface-surface-dark: var(--brand-ahn-primary-600-(alt));
    --elements-surface-surface-disabled: var(--brand-ahn-shades-200);
    --elements-on-surface-onsurface-primary: var(--neutral-white);
    --elements-on-surface-onsurface-secondary: var(--brand-ahn-primary-100);
    --elements-on-surface-onsurface-tertiary: var(--brand-ahn-primary-200);
    --elements-on-surface-onsurface-brand: var(--brand-ahn-primary-500-(alt));
    --elements-on-surface-onsurface-dark: var(--brand-ahn-primary-600-(alt));
    --elements-on-surface-onsurface-disabled: var(--brand-ahn-shades-200);
    --accent-accent-1-accent-1-primary: var(--brand-ahn-secondary-300);
    --accent-accent-1-accent-1-secondary: var(--brand-ahn-secondary-200);
    --accent-accent-1-accent-1-tertiary: var(--brand-ahn-secondary-100);
    --accent-accent-2-accent-2-primary: var(--brand-ahn-tertiary-300);
    --accent-accent-2-accent-2-secondary: var(--brand-ahn-tertiary-200);
    --accent-accent-2-accent-2-tertiary: var(--brand-ahn-tertiary-100);
    --status-positive-status-positive-primary: var(--status-positive-200);
    --status-positive-status-positive-secondary: var(--status-positive-300);
    --status-positive-status-positive-tertiary: var(--status-positive-100);
    --status-caution-status-caution-primary: var(--status-caution-200);
    --status-caution-status-caution-secondary: var(--status-caution-300);
    --status-caution-status-caution-tertiary: var(--status-caution-100);
    --status-critical-status-critical-primary: var(--status-negative-200);
    --status-critical-status-critical-secondary: var(--status-negative-100);
    --status-critical-status-critical-tertiary: var(--status-negative-100);
    --status-informational-status-info-primary: var(--status-informational-200);
    --status-informational-status-info-secondary: var(--status-informational-100);
    --shadow-shadow-interactive: var(--brand-hmk-shades-600);
} */

/* Custom Scrollbar Styles - 统一样式 */
.custom-scrollbar-minimal::-webkit-scrollbar,
.chat-conversation-view__messages-container::-webkit-scrollbar,
.prompt-input::-webkit-scrollbar,
.chat-input-area__prompt-scroll-wrapper::-webkit-scrollbar,
.chat-input-area__file-preview-wrapper::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar-minimal::-webkit-scrollbar-track,
.chat-conversation-view__messages-container::-webkit-scrollbar-track,
.prompt-input::-webkit-scrollbar-track,
.chat-input-area__prompt-scroll-wrapper::-webkit-scrollbar-track,
.chat-input-area__file-preview-wrapper::-webkit-scrollbar-track {
    border-radius: 9999px;
}

.custom-scrollbar-minimal::-webkit-scrollbar-thumb,
.chat-conversation-view__messages-container::-webkit-scrollbar-thumb,
.prompt-input::-webkit-scrollbar-thumb,
.chat-input-area__prompt-scroll-wrapper::-webkit-scrollbar-thumb,
.chat-input-area__file-preview-wrapper::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background: #e5e7eb;
    border: 2px solid transparent;
}

/* 深色模式统一样式 */
@media (prefers-color-scheme: dark) {
    .custom-scrollbar-minimal::-webkit-scrollbar-thumb,
    .chat-conversation-view__messages-container::-webkit-scrollbar-thumb,
    .prompt-input::-webkit-scrollbar-thumb,
    .chat-input-area__prompt-scroll-wrapper::-webkit-scrollbar-thumb,
    .chat-input-area__file-preview-wrapper::-webkit-scrollbar-thumb {
        background: #fff !important;
        border: 2px solid #0066B1 !important;
    }
}

/* Tokens - HMIG */
/* :root {
    --elements-surface-surface-primary: var(--neutral-white);
    --elements-surface-surface-secondary: var(--brand-hmig-primary-100);
    --elements-surface-surface-tertiary: var(--brand-hmig-primary-200);
    --border-color-border-highlight: var(--brand-hmig-primary-300);
    --border-color-border-interactive: var(--brand-hmig-primary-400);
    --border-color-border-pressed: var(--brand-hmig-primary-500);
    --border-color-border-light: var(--brand-hmig-shades-200);
    --border-color-border-medium: var(--brand-hmig-shades-300);
    --border-color-border-dark: var(--brand-hmig-shades-400);
    --border-weight-border-weight-s: var(--1px);
    --border-weight-border-weight-m: var(--2px);
    --border-weight-border-weight-l: var(--4px);
    --border-weight-border-weight-xl: var(--8px);
    --border-radius-border-radius-none: var(--0px);
    --border-radius-border-radius-sm: var(--2px);
    --border-radius-border-radius-m: var(--4px);
    --border-radius-border-radius-l: var(--8px);
    --border-radius-border-radius-xl: var(--12px);
    --border-radius-border-radius-xxl: var(--16px);
    --border-radius-border-radius-xxxl: var(--32px);
    --border-radius-border-radius-full: var(--999px);
    --button-button-primary: var(--brand-hmig-secondary-300);
    --button-button-secondary: var(--brand-hmig-primary-600);
    --button-button-tertiary: var(--brand-hmig-secondary-300);
    --button-button-invert: var(--neutral-white);
    --button-button-accent: var(--brand-hmig-primary-300);
    --button-button-disable-primary: var(--brand-hmig-shades-200);
    --button-button-disable-secondary: var(--brand-hmig-shades-300);
    --button-button-highlight-primary: var(--brand-hmig-primary-100);
    --button-button-highlight-secondary: var(--brand-hmig-primary-200);
    --text-text-body: var(--brand-hmig-primary-600);
    --text-text-secondary: var(--neutral-500);
    --text-text-interactive: var(--brand-hmig-secondary-300);
    --text-text-heading: var(--brand-hmig-primary-500);
    --text-text-invert: var(--neutral-white);
    --text-text-disable: var(--neutral-400);
    --spacing-spacing-sm-1: var(--2px);
    --spacing-spacing-sm-2: var(--4px);
    --spacing-spacing-sm-3: var(--8px);
    --spacing-spacing-m-1: var(--12px);
    --spacing-spacing-m-2: var(--16px);
    --spacing-spacing-m-3: var(--24px);
    --spacing-spacing-l-1: var(--32px);
    --spacing-spacing-l-2: var(--40px);
    --spacing-spacing-l-3: var(--48px);
    --spacing-spacing-l-4: var(--64px);
    --spacing-spacing-xl-1: var(--80px);
    --spacing-spacing-xl-2: var(--96px);
    --spacing-spacing-xl-4: var(--160px);
    --elements-surface-surface-brand: var(--brand-hmig-primary-500);
    --elements-surface-surface-dark: var(--brand-hmig-primary-600);
    --elements-surface-surface-disabled: var(--brand-hmig-shades-200);
    --elements-on-surface-onsurface-primary: var(--neutral-white);
    --elements-on-surface-onsurface-secondary: var(--brand-hmig-primary-100);
    --elements-on-surface-onsurface-tertiary: var(--brand-hmig-primary-200);
    --elements-on-surface-onsurface-brand: var(--brand-hmig-primary-500);
    --elements-on-surface-onsurface-dark: var(--brand-hmig-primary-600);
    --elements-on-surface-onsurface-disabled: var(--brand-hmig-shades-200);
    --accent-accent-1-accent-1-primary: var(--brand-hmig-secondary-300);
    --accent-accent-1-accent-1-secondary: var(--brand-hmig-secondary-200);
    --accent-accent-1-accent-1-tertiary: var(--brand-hmig-secondary-100);
    --accent-accent-2-accent-2-primary: var(--brand-hmig-tertiary-300);
    --accent-accent-2-accent-2-secondary: var(--brand-hmig-tertiary-200);
    --accent-accent-2-accent-2-tertiary: var(--brand-hmig-tertiary-100);
    --status-positive-status-positive-primary: var(--status-positive-200);
    --status-positive-status-positive-secondary: var(--status-positive-300);
    --status-positive-status-positive-tertiary: var(--status-positive-100);
    --status-caution-status-caution-primary: var(--status-caution-200);
    --status-caution-status-caution-secondary: var(--status-caution-300);
    --status-caution-status-caution-tertiary: var(--status-caution-100);
    --status-critical-status-critical-primary: var(--status-negative-200);
    --status-critical-status-critical-secondary: var(--status-negative-100);
    --status-critical-status-critical-tertiary: var(--status-negative-100);
    --status-informational-status-info-primary: var(--status-informational-200);
    --status-informational-status-info-secondary: var(--status-informational-100);
    --shadow-shadow-interactive: var(--brand-hmk-shades-600);
} */
