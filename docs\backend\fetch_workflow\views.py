import json
import traceback
from datetime import datetime, timedelta, timezone
from typing import List

import google.cloud.exceptions as GCPExceptions
from flask import Blueprint, jsonify, make_response, request, session, url_for
from pydantic_core import ValidationError
from werkzeug.utils import secure_filename

from lib.firestore_client import FirestoreClient
from models.retrieval_workbook.RetrievalEnums import RetrievalWorkbookChunkSize
from models.retrieval_workbook.RetrievalWorkbook import UpdateRetrievalWorkbook
from models.Session import Session, UpdateSessionMessage
from models.shared.enums import SessionTypeEnum
from services.RetrievalWorkbookService import RetrievalWorkbookService
from services.SessionService import SessionService
from website.authorization import authorization_required
from website.fetch_workflow.utils import helpers, policyweb_helpers
from website.google_cloud_init import GCS
from website.logger_framework import make_logger
from website.utils import db_utils, upload_helper

workflow = "fetch_workflow"

logger_info, logger_error = make_logger(workflow, __file__)

fetch = Blueprint("fetch_process", __name__)


@fetch.route("/feedback", methods=["POST"])
def feedback():
    try:
        user_email = session["email"]
        feedback_request: dict = request.json
        session_id = feedback_request.get("sessionId")
        prompt_index = feedback_request.get("id").split("-")[1]
        info = feedback_request.get("info", None)
        response_feedback = None
        if info is not None:
            response_feedback = info == 1

        session_client = FirestoreClient().InstanceClient(user_email)
        session_service = SessionService(persistence_client=session_client)
        session_document = session_service.get_user_session_by_id(
            session_id, user_email, True
        )

        if "plcy" not in feedback_request["sessionId"]:
            message_document = session_document.conversation_history[int(
                prompt_index)]
            if message_document.conversation_role == "model":
                feedback_message = UpdateSessionMessage(
                    feedback=response_feedback,
                    message=message_document.message,
                    id=message_document.id,
                    updated_utc=datetime.now(timezone.utc),
                )
                updated_message = session_service.update_message_feedback(
                    session_id, feedback_message
                )

                prev_index = max(0, int(prompt_index) - 1)
                bq_response_id = f"{session_document.id}-{prev_index}"
                prompt_response_as_bq_dict = updated_message.to_pubsub_message(
                    session_document.id,
                    user_email,
                    session_document.session_type,
                    bq_response_id,
                )

                db_utils.add_BQ_session_prompt_response(
                    prompt_response_as_bq_dict)
        else:
            policyweb_helpers.handle_policyweb_feedback(feedback_request)
        return ""
    except Exception as e:
        logger_error.error(
            f"{workflow} : feedback : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, check logs for more info"}


# File Upload

# @fetch.route("/files/signed_url", methods=["POST"])
# def generate_signed_urls():
#     try:
#         request_body = request.get_json()

#         signed_urls, status_code = helpers.create_upload_url(request, session)
#         response_body = None

#         persist_to_session = req_body.get("persist_to_session", False)
#         if persist_to_session and (status_code >= 200 and status_code < 300):
#             response_body = helpers.upsert_session_from_upload(
#                 req_body, session, signed_urls
#             )
#         else:
#             response_body = {"signed_urls": signed_urls}

#         return make_response(jsonify(response_body), status_code)

#     except Exception as e:
#         logger_error.error(
#             f"{workflow} : multimodalbot : {str(e)} : {traceback.format_exc()}"
#         )
#         return {"msg": "error, check logs for more info"}


@fetch.route("/session/upload_files", methods=["PUT"])
@fetch.route("/fileupload", methods=["PUT"])
def uploaded():
    try:
        req_body = request.get_json()

        signed_urls, status_code = helpers.create_upload_url(request, session)
        response_body = None

        persist_to_session = req_body.get("persist_to_session", False)
        if persist_to_session and (status_code >= 200 and status_code < 300):
            response_body = helpers.upsert_session_from_upload(
                req_body, session, signed_urls
            )
        else:
            response_body = {"signed_urls": signed_urls}

        return make_response(jsonify(response_body), status_code)

    except Exception as e:
        logger_error.error(
            f"{workflow} : multimodalbot : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, check logs for more info"}


@fetch.route("/session/<session_id>", methods=["GET"])
def get_session_by_id(session_id):
    try:
        user_email = session["email"]
        session_client = FirestoreClient().InstanceClient(user_email)
        session_service = SessionService(persistence_client=session_client)

        session_document = session_service.get_user_session_by_id(
            session_id, user_email, include_messages=True
        )
        if not session_document:
            return make_response(
                {"error": f"No Session with id: {session_id} could be found."}, 404
            )
        else:
            response_body = session_document.to_dict(
                date_format_iso=False, to_exclude=Session.FRONTEND_EXCLUDES
            )
            return make_response(jsonify(response_body), 200)
    except Exception as e:
        logger_error.error(
            f"{workflow} : api/v1/session/{session_id}/prompt_response : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, check logs for more info"}


@fetch.route("/history_json", methods=["GET"])
# @authorization_required
def menu_history_json():
    try:
        user_email = session.get("email")  # TODO: verify why .get() here
        session_client = FirestoreClient().InstanceClient(user_email)
        session_service = SessionService(persistence_client=session_client)

        ten_days_ago = datetime.now(timezone.utc) - timedelta(days=10)
        sessions_for_user = session_service.get_sessions_by_user_email(
            user_email, include_messages=True, since_date_utc=ten_days_ago
        )
        filtered_and_sorted_sessions = sorted(
            [
                session
                for session in sessions_for_user
                if session.conversation_history
                and (
                    session.conversation_history[-1].conversation_role == "model"
                    and session.conversation_history[-1].message
                )
            ],
            key=lambda session: max(
                message.updated_utc
                for message in session.conversation_history
                if message.conversation_role == "model"
            ),
            reverse=True,
        )
        json_sessions = [
            session_document.to_dict(
                date_format_iso=True, include_document_id=True)
            for session_document in filtered_and_sorted_sessions
        ]
        return jsonify(json_sessions)

    except Exception as e:
        logger_error.error(
            f"{workflow} : menu_history_json : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, check logs for more info"}


@fetch.route("/history_json/<session_id>", methods=["GET"])
# @authorization_required
def history_json(session_id):
    try:
        user_email = session["email"]
        session_client = FirestoreClient().InstanceClient(user_email)
        session_service = SessionService(persistence_client=session_client)

        session_document = session_service.get_user_session_by_id(
            session_id, user_email, include_messages=True
        )

        # Clean file history to remove non-existent files (e.g., cancelled uploads)
        session_document = helpers.clean_session_file_history(
            session_document, user_email)

        return jsonify(
            session_document.to_dict(
                date_format_iso=True,
                include_document_id=True,
                to_exclude=Session.FRONTEND_EXCLUDES,
            )
        )

    except Exception as e:
        logger_error.error(
            f"{workflow} : history_json : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, check logs for more info"}


@fetch.route("/session/<session_id>/prompt_response", methods=["GET"])
def get_latest_response_for_session(session_id):
    try:
        user_email = session["email"]
        session_client = FirestoreClient().InstanceClient(user_email)
        session_service = SessionService(persistence_client=session_client)

        session_document = session_service.get_user_session_by_id(
            session_id, user_email, include_messages=True
        )
        if not session_document:
            return make_response(
                {"error": f"No Session with id: {session_id} could be found."}, 404
            )

        if session_document.conversation_history:
            latest_prompt = session_document.conversation_history[-1]
            # response_id = f"{session_document.id}-{len(session_document.conversation_history) - 1}"
            response_id = latest_prompt.id
            if latest_prompt.conversation_role != "model":
                response_body = {
                    "ready": False,
                    "response": None,
                    "response_time_utc": datetime.now(timezone.utc).isoformat(),
                }
                return make_response(jsonify(response_body), 200)

            response_body = {
                "ready": latest_prompt.conversation_role == "model",
                "response": latest_prompt.message,
                "response_time_utc": (latest_prompt.updated_utc.isoformat()),
            }
            if latest_prompt.conversation_role == "model":
                response_body["response_id"] = response_id

            return make_response(jsonify(response_body), 200)
        else:
            return make_response(
                jsonify(
                    {
                        "error": f"Session with id: {session_id} exists but has no active prompts.",
                    }
                ),
                422,
            )  # Unprocessable Content - should only happen if someone calls this endpoint in a vacuum

    except Exception as e:
        logger_error.error(
            f"{workflow} : api/v1/session/{session_id}/prompt_response : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, check logs for more info"}


# Policyweb
@fetch.route("/restful/query_policies", methods=["POST"])
def pw_query_policies():
    # try:
    data = request.json
    user = session["email"]
    logger_info.info(
        f"{workflow} : pw_query_policies : Returning API call for USER: {user}"
    )
    return policyweb_helpers.handle_policyweb_session(data, user)


@fetch.route("/word_export", methods=["POST"])
def word_export():
    try:

        data = request.get_json()
        if not data or "input" not in data:
            logger_error.error(
                f"{workflow} : generate_presentation : Missing 'input' in request"
            )
            return jsonify({"error": "Missing 'input' in request"}), 400

        response = data["input"]

        email = session["email"]
        gcs_object_name = helpers.create_word_doc(response, email)

        gcs_util = GCS()
        download_success, download_url = gcs_util.generate_download_signed_url_v4(
            gcs_object_name, email
        )

        if not download_success:
            logger_error.error(
                f"{workflow} : word_export : Failed to generate download URL"
            )
            return jsonify({"error": "Failed to generate download URL"}), 500

        logger_info.info(
            f"{workflow} : word_export : Successfully generated Word document."
        )

        return jsonify(
            {
                "success": True,
                "download_url": download_url,
                "message": "Word doc generated successfully.",
            }
        )

    except Exception as e:
        logger_error.error(
            f"{workflow} : word_export : {str(e)} : {traceback.format_exc()}"
        )
        return jsonify({"error": "Error generating Word document"}), 500


@fetch.route("/file_attestation", methods=["POST"])
def file_attestation():
    files = request.get_json().get("files_mimes", None)
    session_type = request.get_json().get("session_type", None)
    attestation_text = request.get_json().get("attestation_text", None)
    user = session["email"]
    if files is None:
        logger_error.error(
            f"{workflow} : file_attestation : {user} attested with no files"
        )
        return {"err", "Bad Request"}, 400

    filenames = []
    attestation_date = datetime.now(timezone.utc).isoformat()

    for file in files:
        filenames.append(secure_filename(file["file_name"]))

    user_dict = {
        "user": user,
        "filenames": filenames,
        "attestation_date": attestation_date,
        "attestation_text": attestation_text,
        "session_type": session_type,
    }

    db_utils.add_file_attestation(user_dict)
    return ""


@fetch.route("/session/<session_id>/file", methods=["DELETE"])
def delete_session_file(session_id):
    """
    Delete a file from both GCS and session file_history.

    Request body should contain:
    {
        "gcs_path": "gs://bucket/path/to/file.ext"
    }
    """
    try:
        user_email = session["email"]
        request_data = request.get_json()

        if not request_data or "gcs_path" not in request_data:
            return make_response(
                {"error": "Missing 'gcs_path' in request body"}, 400
            )

        gcs_path = request_data["gcs_path"]

        # Validate that gcs_path is provided and not empty
        if not gcs_path or not isinstance(gcs_path, str):
            return make_response(
                {"error": "Invalid 'gcs_path' provided"}, 400
            )

        # Delete the file
        success, error_message = helpers.delete_uploaded_file(
            session_id, gcs_path, user_email)

        if success:
            logger_info.info(
                f"{workflow} : delete_session_file : Successfully deleted file {gcs_path} from session {session_id} for user {user_email}"
            )
            return make_response(
                {"message": "File deleted successfully"}, 200
            )
        else:
            logger_error.error(
                f"{workflow} : delete_session_file : Failed to delete file {gcs_path} from session {session_id}: {error_message}"
            )
            return make_response(
                {"error": error_message}, 400
            )

    except Exception as e:
        logger_error.error(
            f"{workflow} : delete_session_file : {str(e)} : {traceback.format_exc()}"
        )
        return make_response(
            {"error": "Internal server error"}, 500
        )
