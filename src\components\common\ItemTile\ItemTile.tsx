import React from 'react';

import useThemeStyles from '@/hooks/useThemeStyles';

import './ItemTile.scss';

export type ItemTileProps = {
  onClick: (e: React.MouseEvent) => void
  children: React.ReactNode;
  label?: string;
  isCreating?: boolean;
  isUpdating?: boolean;
  isDeleting?: boolean;
};

export const ItemTile: React.FC<ItemTileProps> = (props: ItemTileProps) => {
  const { classes } = useThemeStyles();
  const { onClick, children, label } = props;

  return (
    <div
      className={`itemTile focus-within:ring-2 focus-within:ring-blue-500 ${classes.background}`}
      tabIndex={0}
      role="article"
      aria-labelledby={label}
      onClick={onClick}
    >
      <div className={`itemTile__spine`}/>
      <div className={`itemTile__content`}>{children}</div>
    </div>
  );
};

export default ItemTile;
