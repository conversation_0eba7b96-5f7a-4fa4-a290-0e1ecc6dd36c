{"name": "sidekick", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "lint:fix": "eslint . --fix"}, "dependencies": {"@base-ui-components/react": "^1.0.0-alpha.8", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/base": "^5.0.0-beta.70", "@mui/material": "^7.0.2", "@reduxjs/toolkit": "^2.8.1", "@tailwindcss/vite": "^4.0.12", "@types/react-syntax-highlighter": "^15.5.13", "baseui": "^15.0.0", "date-fns": "^4.1.0", "mermaid": "^11.7.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-icons": "^5.5.0", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.3.0", "react-spinners": "^0.15.0", "react-syntax-highlighter": "^15.6.1", "react-toastify": "^11.0.5", "react-zoom-pan-pinch": "^3.7.0", "rehype-highlight": "^7.0.2", "rehype-katex": "^7.0.1", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "remark-supersub": "^1.0.0", "styletron-engine-atomic": "^1.6.2", "styletron-react": "^6.1.1", "tailwindcss": "^4.0.12", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "prettier": "^3.5.3", "sass-embedded": "^1.86.3", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}