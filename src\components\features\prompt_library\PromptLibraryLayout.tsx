import React from 'react';
import { Outlet, useOutletContext } from 'react-router-dom';

import './PromptLibraryLayout.scss';

const PromptLibraryLayout: React.FC = () => {
  const passThroughContext = useOutletContext();
  return (
    <div className="promptlibrary-layout w-full h-full flex justify-center color-white">
      <Outlet context={passThroughContext}/>
    </div>
  );
};

export default PromptLibraryLayout;
