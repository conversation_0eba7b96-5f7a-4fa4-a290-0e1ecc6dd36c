import React, { useState } from 'react';
import { MdOutlineEdit , MdOutlineSubdirectoryArrowRight } from 'react-icons/md';
import { TfiBookmarkAlt } from 'react-icons/tfi';
import { IoSaveOutline } from 'react-icons/io5';

import PromptLibraryBaseModal from './PromptLibraryBaseModal';
// import { useThemeStyles } from '@/hooks/useThemeStyles';

import './PromptLibraryEditTemplateModal.scss';
import { SessionType } from '@/types';

interface PromptLibraryEditTemplateModalProps {
  isOpen: boolean;
  onOpenChange: (value: boolean) => void;
  onSaveAndRunPromptTemplate: (value: object) => void;
  onSavePromptTemplate: (value: object) => void;
  onDeletePromptTemplateFromEdit: (value: object) => void;
  promptName: string;
  promptContent: string;
  promptSystemInstruction: string;
  promptSessionType: SessionType;
  onPromptNameChange: (value: string) => void;
  onPromptContentChange: (value: string) => void;
  onPromptSystemInstructionChange: (value: string) => void;
  onPromptSessionTypeChange: (value: SessionType) => void;
}

const PromptLibraryEditTemplateModal: React.FC<PromptLibraryEditTemplateModalProps> = ({
  isOpen,
  onOpenChange,
  onSaveAndRunPromptTemplate,
  onSavePromptTemplate,
  onDeletePromptTemplateFromEdit,
  promptName,
  promptContent,
  promptSystemInstruction,
  promptSessionType,
  onPromptNameChange,
  onPromptContentChange,
  onPromptSystemInstructionChange,
  onPromptSessionTypeChange,
}) => {
  // const { classes } = useThemeStyles();

  // Component State
  const [isPromptNameInputFocused, setIsPromptNameInputFocused] = useState(false);
  const [isPromptContentInputFocused, setIsPromptContentInputFocused] = useState(false);
  const [isPromptSystemInstructionInputFocused, setIsPromptSystemInstructionInputFocused] = useState(false);
  const [isLoading] = React.useState(false);

  const handlePromptNameInputFocus = () => {
    setIsPromptNameInputFocused(true);
  };

  const handlePromptNameInputBlur = () => {
    setIsPromptNameInputFocused(false);
  };

  const handlePromptContentInputFocus = () => {
    setIsPromptContentInputFocused(true);
  };

  const handlePromptContentInputBlur = () => {
    setIsPromptContentInputFocused(false);
  };

  const handlePromptSystemInstructionInputFocus = () => {
    setIsPromptSystemInstructionInputFocused(true);
  };

  const handlePromptSystemInstructionInputBlur = () => {
    setIsPromptSystemInstructionInputFocused(false);
  };

  return (
    <PromptLibraryBaseModal
      isOpen={isOpen}
      onClose={() => onOpenChange(false)}
      title="Edit Library Prompt"
      infoText="File sizes cannot exceed 10mb. Only 4 PDF files can be uploaded to a notebook."
    >
      {/* Input fields container */}
      <div className="prompt-edit__fields">
        {/* Prompt SessionType - Dropdown */}
        <div className="prompt-edit__session-select-container">
          <select
            className="prompt-edit__session-select"
            value={promptSessionType}
            onChange={(event) => {
                const asNumber = parseInt(event.target.value) || SessionType.CHAT;
                onPromptSessionTypeChange(asNumber);
              }
            }
          >
            <option key={SessionType.CHAT} value={SessionType.CHAT}>General</option>
            <option key={SessionType.CODE} value={SessionType.CODE}>Code</option>
            <option key={SessionType.MDLM} value={SessionType.MDLM}>Medical</option>
          </select>
        </div>
        {/* Prompt Title - Text Field Container */}
        <div
          className={`prompt-edit__field prompt-edit__field--name ${
            promptName ? 'prompt-edit__field--filled' : 'prompt-edit__field--empty'
          }`}
        >
          {/* Input Container */}
          <div
            className={`prompt-edit__input-container ${
              isPromptNameInputFocused ? 'prompt-edit__input-container--focused' : ''
            }`}
          >
            {/* Content Container */}
            <div className="prompt-edit__input-wrapper">
              <input
                className="prompt-edit__input"
                id="prompt-name-create"
                type="text"
                placeholder="Name your prompt..."
                disabled={isLoading}
                value={promptName}
                onChange={event => onPromptNameChange(event.target.value)}
                onFocus={handlePromptNameInputFocus}
                onBlur={handlePromptNameInputBlur}
              />
              {/* Icon Container */}
              <div className="prompt-edit__icon-wrapper">
                <MdOutlineEdit className="prompt-edit__icon" />
              </div>
            </div>
          </div>
        </div>

        {/* Prompt Content - Text Field Container */}
        <div
          className={`prompt-edit__field prompt-edit__field--content ${
            promptContent ? 'prompt-edit__field--filled' : 'prompt-edit__field--empty'
          }`}
        >
          {/* Input Container */}
          <div
            className={`prompt-edit__input-container prompt-edit__input-container--large ${
              isPromptContentInputFocused ? 'prompt-edit__input-container--focused' : ''
            }`}
          >
            {/* Content Container */}
            <div className="prompt-form__textarea-wrapper">
              <textarea
                className="prompt-edit__textarea"
                id="prompt-content-create"
                placeholder="Enter your prompt..."
                disabled={isLoading}
                value={promptContent}
                onChange={event => onPromptContentChange(event.target.value)}
                onFocus={handlePromptContentInputFocus}
                onBlur={handlePromptContentInputBlur}
              >
                {promptContent}
              </textarea>
              {/* Icon Container */}
              <div className="prompt-edit__icon-wrapper">
                <MdOutlineEdit className="prompt-edit__icon" />
              </div>
            </div>
          </div>
        </div>

        {/* Prompt System Instruction - Text Field Container */}
        <div
          className={`prompt-edit__field prompt-edit__field--content ${
            promptSystemInstruction ? 'prompt-edit__field--filled' : 'prompt-edit__field--empty'
          }`}
        >
          {/* Input Container */}
          <div className={`prompt-edit__input-container prompt-edit__input-container--large ${
              isPromptSystemInstructionInputFocused ? 'prompt-edit__input-container--focused' : ''
            }`}>
            {/* Content Container */}
            <div className="prompt-form__textarea-wrapper">
              <textarea
                className="prompt-edit__textarea"
                id="prompt-system-instruction-update"
                placeholder="Enter prompt system instruction..."
                disabled={isLoading}
                value={promptSystemInstruction}
                onChange={event => onPromptSystemInstructionChange(event.target.value)}
                onFocus={handlePromptSystemInstructionInputFocus}
                onBlur={handlePromptSystemInstructionInputBlur}
              >
                {promptSystemInstruction}
              </textarea>
              {/* Icon Container */}
              <div className="prompt-edit__icon-wrapper">
                <MdOutlineEdit className="prompt-edit__icon" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Interaction row container */}
      <div className="prompt-edit__actions">
        {/* Delete Button */}
        <button
          className={`prompt-edit__button prompt-edit__button--delete ${
            promptContent ? 'prompt-edit__button--delete-active' : 'prompt-edit__button--delete-inactive'
          }`}
          onClick={onDeletePromptTemplateFromEdit}
        >
          {/* Delete Button Contents Container */}
          <div className="prompt-edit__button-content">
            {promptContent ? (
              <span className="prompt-edit__button-icon">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                >
                  <path
                    d="M14.12 10.47L12 12.59L9.87 10.47L8.46 11.88L10.59 14L8.47 16.12L9.88 17.53L12 15.41L14.12 17.53L15.53 16.12L13.41 14L15.53 11.88L14.12 10.47ZM15.5 4L14.5 3H9.5L8.5 4H5V6H19V4H15.5ZM6 19C6 20.1 6.9 21 8 21H16C17.1 21 18 20.1 18 19V7H6V19ZM8 9H16V19H8V9Z"
                    fill="#F7987D"
                  />
                </svg>
              </span>
            ) : (
              <span className="prompt-edit__button-icon">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                >
                  <path
                    d="M16 9V19H8V9H16ZM14.5 3H9.5L8.5 4H5V6H19V4H15.5L14.5 3ZM18 7H6V19C6 20.1 6.9 21 8 21H16C17.1 21 18 20.1 18 19V7Z"
                    fill="#6D787F"
                  />
                </svg>
              </span>
            )}
            <p className="prompt-edit__button-text">
              Delete Prompt
            </p>
          </div>
        </button>

        {/* Save Button - Run Button Container */}
        <div
          className={`prompt-edit__button-group ${
            promptContent ? 'prompt-edit__button-group--filled' : 'prompt-edit__button-group--empty'
          }`}
        >
          {/* Save -->-- Save & Run Button */}
          <button
            className={`prompt-edit__button prompt-edit__button--primary ${
              promptContent ? 'prompt-edit__button--save-run' : 'prompt-edit__button--save'
            }`}
            onClick={onSaveAndRunPromptTemplate}
          >
            {/* Save Button Contents Container */}
            <div className="prompt-edit__button-content">
              {promptContent ? (
                <MdOutlineSubdirectoryArrowRight className="prompt-edit__button-icon" />
              ) : (
                <TfiBookmarkAlt className="prompt-edit__button-icon" />
              )}
              <p className="prompt-edit__button-text">
                {promptContent ? 'Save and Run Prompt' : 'Save Prompt'}
              </p>
            </div>
          </button>

          {/* Run -->-- Save Button */}
          <button
            className={`prompt-edit__button ${
              promptContent
                ? 'prompt-edit__button--save prompt-edit__button--filled' 
                : 'prompt-edit__button--run prompt-edit__button--outline'
            }`}
            onClick={onSavePromptTemplate}
          >
            {/* Run Button Contents Container */}
            <div className="prompt-edit__button-content">
              {promptContent ? (
                <IoSaveOutline className="prompt-edit__button-icon" />
              ) : (
                <MdOutlineSubdirectoryArrowRight className="prompt-edit__button-icon" />
              )}
              <p className="prompt-edit__button-text">
                {promptContent ? 'Save Prompt' : 'Run Prompt'}
              </p>
            </div>
          </button>
        </div>
      </div>
    </PromptLibraryBaseModal>
  );
};

export default PromptLibraryEditTemplateModal;
