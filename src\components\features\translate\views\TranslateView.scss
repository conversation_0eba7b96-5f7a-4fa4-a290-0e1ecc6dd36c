@media (max-width: 992px) { /* add css rules for media widths? */
  .translate__button-group {
    width: 100%;
    flex-direction: column;
    button {
      width: 100%;
    }
  }
}

.translate {
  &__container {
    width: 100%;
    max-width: 1400px;
    padding: var(--spacing-spacing-m-3, 24px) var(--spacing-spacing-m-3, 24px);
    color: var(--text-text-invert);
    display: flex;
    flex-direction: row;
    gap: var(--spacing-spacing-sm-2, 4px);

    button {
      align-items: center;
      justify-content: center;
      padding: var(--spacing-spacing-sm-3) var(--spacing-spacing-m-1);
      border-width: var(--border-weight-border-weight-s);
      border-color: var(--border-color-border-interactive);
      border-radius: var(--border-radius-border-radius-xxxl);
      font-weight: 500;
      cursor: pointer;

      transition: all 0.2s ease-out;
      &:hover {
        background-color: var(--elements-on-surface-onsurface-brand);
      }
      &:active {
        background-color: transparent;
      }

      &:disabled {
        color: var(--text-text-disable);
        border-color: var(--text-text-disable);
        background-color: transparent;
        cursor: not-allowed;
      }
    }
  }
  &__button {
    &-translate {
      float: right;
      background-color: var(--button-button-primary);
    }
    &-remove {
      background-color: var(--status-critical-status-critical-secondary);
      border-color: var(--status-critical-status-critical-primary) !important;
      &:hover {
        background-color: var(--status-critical-status-critical-primary) !important;
      }
      &:active {
        background-color: var(--status-critical-status-critical-secondary) !important;
      }
      &:disabled {
        &:hover {
          background-color: transparent !important;
        }
      }
    }
    &-svg {
      height: 24px;
      width: 24px;
      color: inherit;
      display: inline;
    }
    &-text {
      text-align: center;
      vertical-align: text-top;
      padding-left: var(--spacing-spacing-sm-2);
    }
  }
  &__input-output {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    &-select {
      width: 50%;
    }

    textarea {
      width: 100%;
      height: 150px;
      margin-top: 10px;
      background-color: var(--elements-on-surface-onsurface-brand);
      border: var(--border-weight-border-weight-s) solid var(--border-color-border-interactive);
      border-radius: var(--border-radius-border-radius-l);
      resize: none;
      display: flex;
      padding: var(--spacing-spacing-m-2, 16px) var(--spacing-spacing-m-3);
      justify-content: center;
      align-items: center;
      gap: 10px;
    }
    .translate__button-group {
      display: flex;
      gap: var(--spacing-spacing-m-1, 12px);
      margin-top: var(--spacing-spacing-m-1);
    }
  }
  &__switch {
    height: 150px;
    display: flex;
    align-items: center;
    margin-top: 50px;
    button {
      all: unset;

      &:hover {
        all: unset;
        cursor: pointer;
        svg {
          transition: all 0.2s ease;
          color: var(--text-text-interactive);
        }
      }
      // &:active {
      //     svg {
      //         transition: transform 0.2s ease;
      //         transform: rotate(360deg);

      //     }
      // }
    }
    &-arrow {
      height: 48px;
      width: 49px;
      opacity: 0.7;
      color: var(--text-text-invert);
    }
  }
}

