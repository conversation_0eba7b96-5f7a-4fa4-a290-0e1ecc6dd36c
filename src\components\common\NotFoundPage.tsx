import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useThemeStyles } from '@/hooks/useThemeStyles';

interface NotFoundPageProps {
  title?: string;
  message?: string;
  showBackButton?: boolean;
}

const NotFoundPage: React.FC<NotFoundPageProps> = ({
  title = '404',
  message = 'The page you are looking for does not exist or you do not have permission to access it.',
  showBackButton = true,
}) => {
  const navigate = useNavigate();
  const { classes, isDarkMode } = useThemeStyles();
  
  const handleGoBack = () => {
    navigate(-1);
  };

  const handleGoHome = () => {
    navigate('/workbooks/my');
  };

  return (
    <div className={`flex flex-col items-center justify-center h-full w-full ${classes.background}`}>
      <div className={`text-9xl font-bold mb-4 ${classes.text}`}>{title}</div>
      <div className={`text-xl mb-8 ${classes.text} text-center max-w-lg`}>{message}</div>

      {showBackButton && (
        <div className="flex gap-4">
          <button
            onClick={handleGoBack}
            className={`px-6 py-2 rounded-full bg-blue-500 hover:bg-blue-600 text-white transition-colors ${isDarkMode ? 'shadow-md shadow-blue-500/30' : ''}`}
          >
            Go Back
          </button>
          <button
            onClick={handleGoHome}
            className={`px-6 py-2 rounded-full border ${isDarkMode ? 'border-blue-400 hover:bg-blue-900/20' : 'border-blue-500 hover:bg-blue-50'} ${classes.text} transition-colors`}
          >
            Go to Workbooks
          </button>
        </div>
      )}
    </div>
  );
};

export default NotFoundPage; 
