import React from 'react';
import { AiOutlineBug } from 'react-icons/ai';
import { GiBrain } from 'react-icons/gi';
import { MdOutlineEmail } from 'react-icons/md';
import { FiHelpCircle } from 'react-icons/fi';
import { RiFilePaper2Line, RiArticleLine } from 'react-icons/ri';
import SuggestionButton from './SuggestionButton';
import { PromptSuggestionsProps } from '@/types/layout';

const PromptSuggestions: React.FC<PromptSuggestionsProps> = ({ onClick, selectedChatType = 'General' }) => {
  // Only show suggestions for General and Code Chat Categories
  if (selectedChatType === 'Medical' || selectedChatType === 'Policy') {
    return null;
  }

  return (
    <div className="prompt-suggestions flex w-full p-[12px] justify-center items-start content-start gap-x-[12px] gap-y-[12px] flex-wrap">
      <SuggestionButton
        icon={<AiOutlineBug className="h-5 w-5" />}
        label="Troubleshoot code"
        onClick={() => onClick('Help me troubleshoot this code...')}
      />
      <SuggestionButton
        icon={<RiFilePaper2Line className="h-5 w-5" />}
        label="Summarize text"
        onClick={() => onClick('Summarize this text for me...')}
      />
      <SuggestionButton
        icon={<GiBrain className="h-5 w-5" />}
        label="Brainstorm ideas"
        onClick={() => onClick('Help me brainstorm ideas for...')}
      />
      <SuggestionButton
        icon={<MdOutlineEmail className="h-5 w-5" />}
        label="Write email"
        onClick={() => onClick('Write an email about...')}
      />
      <SuggestionButton
        icon={<FiHelpCircle className="h-5 w-5" />}
        label="Get advice"
        onClick={() => onClick('I need advice on...')}
      />
      <SuggestionButton
        icon={<RiArticleLine className="h-5 w-5" />}
        label="Review proposal"
        onClick={() => onClick('Review this proposal...')}
      />
      <SuggestionButton icon={<FiHelpCircle className="h-5 w-5" />} label="Ask anything" onClick={() => onClick('')} />
    </div>
  );
};

export default PromptSuggestions;
