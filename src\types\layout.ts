import { ReactNode } from 'react';
import {
  RetrievalCitation,
  RetrievalCitationSource,
  RetrievalSession,
  RetrievalSessionAnswer,
  RetrievalSessionQuery,
  RetrievalWorkbook,
} from '@/components/features/workbook';

// Navigation related types
export interface SubNavItem {
  key: string;
  label: string;
  path: string;
}

export interface NavItem {
  key: string;
  label: string;
  icon: ReactNode;
  subItems?: SubNavItem[];
}

// Sidebar related types
export interface SidebarProps {
  activeItem?: string;
  onClick?: (item: string) => void;
}

export interface SidebarHeaderProps {
  onToggle: () => void;
}

export interface ChatHistoryProps {
  items: string[];
  isExpanded: boolean;
  onToggle: () => void;
}

export interface NavigationItemProps {
  item: NavItem;
  isActive: boolean;
  onClick: (key: string) => void;
  isExpanded?: boolean;
  onToggle?: () => void;
  subItems?: SubNavItem[];
}

// Chat related types
export interface ChatViewProps {
  onSendMessage?: (message: string) => void;
  selectedChatType?: 'General' | 'Code' | 'Medical' | 'Policy';
}

export interface PromptInputProps {
  message: string;
  textColor: string;
  placeholderColor: string;
  inputBgColor: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  onSubmit?: () => void;
}

export interface GreetingSectionProps {
  textColor: string;
  selectedChatType?: 'General' | 'Code' | 'Medical' | 'Policy';
}

export interface SuggestionButtonProps {
  icon: ReactNode;
  label: string;
  onClick: () => void;
}

export interface PromptSuggestionsProps {
  onClick: (suggestion: string) => void;
  selectedChatType?: 'General' | 'Code' | 'Medical' | 'Policy';
}

export interface InputLowerTrayProps {
  textColor: string;
  onSubmit: () => void;
  selectedChatType?: 'General' | 'Code' | 'Medical' | 'Policy';
}

//Form related type
export interface AccordionProps {
  children: React.ReactNode;
  nameCollapsed: string;
  nameExpanded: string;
  labelClassName?: string;
}

export interface SimpleAccordionProps {
  title: string;
  children: React.ReactNode;
  defaultExpanded?: boolean;
  disabled?: boolean;
  className?: string;
  headerClassName?: string;
}

export interface ChatMessageProps {
  message: string;
  messageModel: RetrievalSessionQuery | RetrievalSessionAnswer;
  isUser: boolean;
  handleFeedbackUp: (e: React.MouseEvent) => void;
  handleFeedbackDown: (e: React.MouseEvent) => void;
  citations?: RetrievalCitation[];
  citationSources?: RetrievalCitationSource[];
}

export interface ChatMessageListProps {
  workbook: RetrievalWorkbook;
  messages: (RetrievalSessionQuery | RetrievalSessionAnswer)[];
  session?: RetrievalSession | null;
  loading?: boolean;
  isGlobal: boolean;
}
