
:root {
    --Border-Color-border-pressed: #003963;
}

.workbook-details-container {
    position: relative;
    width: 100%;
    height: 100%;
    padding: 24px;
    display: flex;
    flex-direction: column;
    .workbook-details-top {
        flex: 1;
    }
    .workbook-details-content {
        width: 100%;
        display: flex;
        flex-direction: column;
        flex: 10;
        align-items: center;
        margin-bottom: 24px;
        .workbook-details-input {
            max-width: 800px;
            width: 100%;
        }
    }
    .workbook-details-bottom {

    }
}

.worbook-utilitybar-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    min-width: 320px;
    width: 320px;
    border-left: 2px solid var(--Border-Color-border-pressed, red);
}
