.policy-table {
  border-collapse: collapse;
  margin-bottom: 10px;
}

.policy-header {
  background-color: #00223c;
  color: white;
  text-align: center;
}

.policy-header td {
  padding: 15px;
}

.policy-table td {
  /* border-right: 1px solid black; */
  border-left: 1.5px #ecf7ff;
}
.policy-table tr {
  border-bottom: 1px solid #b6bcc0;
}
.lightmode .policy-table tr:nth-child(even) {
  background-color: #ecf7ff;
}
.lightmode .policy-table tr.policy-row:nth-child(odd) {
  background-color: white;
}

.darkmode .policy-table tr:nth-child(even) {
  background-color: #003963;
  background-color: brightness(5%);
}
/* .darkmode .policy-table tr:nth-child(odd) {
} */

#policy-select {
  display: inline-block;
  /* width: 200px; */
  height: 40px;
  border: 1px solid #ccc;
  border-radius: 10px;
  padding: 5px 10px;
  background-color: #fff;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  position: relative;
}

#policy-select::after {
  content: "";
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #333;
}

#policy-select:hover {
  border-color: #999;
}

#policy-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px #007bff;
}
#policy-select {
  margin-bottom: 10px;
}
.col-2 {
  margin-top: -25px;
}
/* #policy-select optgroup {
  background-color: #fff;
  color: #333;
  padding: 5px 10px;
  border-bottom: 1px solid #ccc;
}

#policy-select optgroup:hover {
  background-color: #f2f2f2;
}

#policy-select optgroup:last-child {
  border-bottom: none;
} */