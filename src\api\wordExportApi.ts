import { API_BASE_PATH } from './apiConfig';
import { parseApiError } from './apiUtils';

export async function exportToWord(messageContent: string): Promise<{ download_url?: string; error?: string }> {
  const body = {
    input: messageContent,
  };

  const wordExportEndpoint = '/restful/word_export';
  const url = `${API_BASE_PATH}${wordExportEndpoint}`;

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();

    if (!response.ok) {
      const parsedError = parseApiError(data);
      throw new Error(parsedError.message);
    }

    if (data.success && data.download_url) {
      return { download_url: data.download_url };
    } else {
      const parsedError = parseApiError(data);
      return { error: parsedError.message || 'Export failed or download URL not provided.' };
    }
  } catch (error: unknown) {
    const parsedError = parseApiError(error);
    console.error('Error exporting to Word:', parsedError.originalError || error);
    return { error: parsedError.message };
  }
}
