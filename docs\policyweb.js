document.addEventListener("DOMContentLoaded", function () {
  // queryPolicyWeb("");
  const div_select = document.getElementById("replace_select");
  div_select.innerHTML = select_field;
  prompt = document.getElementById("prompt");
  if (prompt != null) {
    prompt.removeEventListener("keydown", allowControlEnterKey);
    prompt.addEventListener("keydown", allowControlEnterKeyPW);
    prompt.addEventListener("keypress", preventEnterKey);
    let chat = document.getElementById("policychat");
    chat.addEventListener("submit", submitPolicy);
  }
  // policyWebResponse(jsonData);
});

function allowControlEnterKeyPW(event) {
  const promptArea = document.getElementById("prompt");
  if (event.ctrlKey && event.key === "Enter") {
    event.preventDefault();
    if (this.value.trim().length > 0) {
      submitPolicy(event);
    }
  }
}

// Fetch to policy web & table generator
let tableCount = 0;
function createPolicyTable(data) {
  const includeHeaders = [
    // Appears on the table in this order
    "Content_ID",
    "Title",
    "Applicability",
    "Responsible_Area",
    "Content_Type",
    "Owner",
    "Overview_Statement",
    "Last_Approved_Date",
    "Effective_Date",
    // "Relevance_Score", //Confirmed sorted by relevancy score.
  ];

  const policies = data.response.policies;

  const table = document.createElement("table");
  table.classList.add("policy-table");
  table.id = `policyTable-${tableCount}`;
  tableCount++;

  // Create header row
  const headerRow = table.insertRow();
  includeHeaders.forEach((header, index) => {
    const headerCell = headerRow.insertCell();
    // Replace underscores with spaces in header text
    headerCell.textContent = header.replace(/_/g, " ");
    headerCell.classList.add("policy-header");
    headerCell.id = `header-${index}`;
  });

  // Create table rows for each policy
  policies.forEach((policy, policyIndex) => {
    const row = table.insertRow();
    row.classList.add("policy-row");
    row.id = `policy-${policyIndex}`;

    // Arrange cells based on includeHeaders order
    includeHeaders.forEach((header) => {
      const cell = row.insertCell();
      if (header === "Content_ID") {
        const link = document.createElement("a");
        link.href = policy["PolicyWeb_Link"];
        link.textContent = policy["Content_ID"];
        link.target = "_blank";
        link.rel = "noopener noreferrer";
        cell.appendChild(link);
      } else {
        cell.textContent = policy[header];
      }
      cell.classList.add("policy-cell");
    });
  });

  return table;
}

let policy_interval_id = 0;
function submitPolicy(event) {
  event.preventDefault();

  let converter = new showdown.Converter({
    extensions: [startAttributeExtension],
  });

  let promptData = document.getElementById("prompt").value;
  let selectData = document.getElementById("policy-select").value;
  let sessionId = document.getElementById("sessionId").value;

  console.log(selectData);
  let promptClient = promptData
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/\n/g, "<br>");

  createMessage("person", "prompts", promptClient);
  policy_interval_id = setInterval(thinkingIndicator, 1000);
  document.getElementById("prompt").value = "";

  const data = {
    location: selectData, //Generate the full list organizations
    user_input: promptData, //get from user input
    session_id: sessionId,
  };
  queryPolicyWeb(data);
}

async function queryPolicyWeb(input_data) {
  if (!checkAuth()) {
    window.location.reload(); //Force a refresh
    return;
  }

  const response = await fetch(policyLink, {
    method: "POST",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
    },
    body: JSON.stringify(input_data),
  });

  const resHeaders = response.headers.get("Content-Type");

  if (!response.ok) {
    //TODO: put error handling from response.js in here
    const err = new Error(
      `Failure on fetch request, Status: ${response.status}`
    );
    throw err;
  } else if (response.ok && resHeaders.includes("application/json")) {
    const data = await response.json();

    if (data.reauth) {
      console.log("Attempting to reauth");
      await refreshAuthFlow(queryPolicyWeb, input_data);
    } else {
      try {
        stopThinking(policy_interval_id);
      } catch {
        console.log("");
      }
      if (data.response_success) {
        policyWebResponse(data);
      } else {
        handleError(data.response);
      }
    }
    document.getElementById("sessionId").value = data.session_id;
  } else if (resHeaders.includes("text/html")) {
    let errorHTML = await response.text();
    errorHTML = escapeHtml(errorHTML);
    policyOnError(
      `Error: An unexpected response was received:<br>${errorHTML} <br>Response status: ${response.status} ${response.statusText}`
    );
  } else {
    policyOnError(
      `Error: An invalid response was received. <br>Response status: ${response.status} ${response.statusText}`
    );
  }
}

function policyOnError(err) {
  try {
    stopThinking(policy_interval_id);
  } catch {
    console.log("");
  }
  handleError(
    `Sidekick ran into an error. Please refresh or try again later.\n ${err}`
  );
  throw err;
}

function policyWebResponse(data) {
  let converter = new showdown.Converter({
    extensions: [startAttributeExtension],
  });

  let table = createPolicyTable(data);
  let message = data["response"]["qa_response"];
  let response_id = data["prompt_id"];

  let fixedResponse;
  if (/(```)[^ ]+/.test(message)) {
    bad_format = message.indexOf("```") + 3;
    fixedResponse =
      message.substring(0, bad_format) + "\n" + message.substring(bad_format);
  } else {
    fixedResponse = message;
  }

  let thumbs = createFeedbackButtons(response_id, data["response_class"]);
  let thumbsUp = thumbs[0];
  let thumbsDown = thumbs[1];

  createMessageWithTable(converter.makeHtml(fixedResponse), table);
  let feedback_list = document.getElementsByClassName("messages_wrapper");
  let feedback_div = document.createElement("div");
  feedback_div.className = "feedbackDIV feedback";
  feedback_div.appendChild(thumbsUp);
  feedback_div.appendChild(thumbsDown);
  // feedback_div.appendChild(copyButton);

  feedback_list[feedback_list.length - 1].appendChild(feedback_div);
}

function writeTableToScreen(message, table, id = "", cls = "") {
  let responseHTML = document.getElementById("response");
  let divWrapper = document.createElement("DIV");
  divWrapper.classList.add("messages_wrapper");
  let divAppend = document.createElement("DIV");
  divAppend.classList.add("messages_list");
  if (cls.length) {
    divAppend.classList.add(cls);
  }
  divWrapper.id = id;

  divAppend.innerHTML = message;
  if (table instanceof HTMLTableElement) {
    divAppend.appendChild(table);
  }
  divWrapper.appendChild(divAppend);
  responseHTML.appendChild(divWrapper);
  divWrapper.scrollIntoView({
    behavior: "smooth",
    // block: "start",
    // inline: "nearest",
  });
}

function createMessageWithTable(message, table) {
  let dateTime = new Date();
  let timestamp = dateTime.toLocaleTimeString();

  messageText = formMessageTextWithTable(
    message,
    table,
    "Sidekick",
    timestamp,
    (id = undefined)
  );

  writeTableToScreen(messageText);
}

function formMessageTextWithTable(message, table, name, timestamp, id = "") {
  let message_p;
  let copyButton = "";
  if (id !== undefined) {
    message_p = `<p id='${id}'>${message}</p>`;
  } else {
    message_p = `<p>${message}</p>`;
  }

  // Check if message is a table element
  if (table instanceof HTMLTableElement) {
    message_p += table.outerHTML; // Get the HTML of the table
  }

  message_p = `<div class="line line-robot">${message_p}</div>`;
  messageText = `<span class=responses><span class="user"><span class="dot dot-robot"></span><span class="promptname">${name}</span> (${timestamp})</span>${message_p}</span>`;
  return messageText;
}
