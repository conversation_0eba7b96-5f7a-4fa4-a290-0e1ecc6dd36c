import React, { useState, useEffect, ReactNode } from 'react';
import { BiChevronDown, BiChevronUp } from 'react-icons/bi';
import { Link, useLocation, useNavigate } from 'react-router-dom';

import './styles.scss';
import useThemeStyles from '@/hooks/useThemeStyles';

export type SidebarLinkDetails = {
  to?: string;
  label: string;
  IconComponent?: ReactNode;
  onClick?: () => void;
};
export type SidebarLinkProps = SidebarLinkDetails & {
  subLinks?: SidebarLinkDetails[];
};

const SidebarDirectLink: React.FC<SidebarLinkProps> = (props: SidebarLinkProps) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { to, label, IconComponent, onClick } = props;
  const effectiveTo = to ?? '/';
  const isActive = location.pathname.startsWith(effectiveTo) && (effectiveTo !== '/' || location.pathname === '/');

  const handleClick = (e: React.MouseEvent<HTMLDivElement> | React.KeyboardEvent<HTMLDivElement>) => {
    if (onClick) {
      e.preventDefault();
      onClick();
      if (to) {
        navigate(effectiveTo, { state: { fromNewChat: true } });
      }
    } else if (to) {
      navigate(effectiveTo);
    }
  };

  return (
    <div
      onClick={onClick || to ? handleClick : undefined}
      className={`${isActive ? 'font-semibold opacity-100' : ''} sidebar-link-item opacity-70 hover:opacity-100 transition-opacity`}
      role="link"
      aria-current={isActive ? 'page' : undefined}
      tabIndex={0}
      onKeyDown={e => {
        if (e.key === 'Enter' || e.key === ' ') {
          handleClick(e);
        }
      }}
    >
      <Link
        to={effectiveTo}
        style={{ textDecoration: 'none', color: 'inherit', display: 'contents' }}
        tabIndex={-1}
        aria-hidden={true}
        onClick={e => e.preventDefault()}
      >
        {IconComponent && <div className="sidebar-link-item-icon">{IconComponent}</div>}
        <div className="sidebar-link-item-label flex-grow font-roboto text-[18px]">{label}</div>
      </Link>
    </div>
  );
};

const SidebarLinkContainer: React.FC<SidebarLinkProps> = (props: SidebarLinkProps) => {
  const { classes } = useThemeStyles();
  const location = useLocation();
  const { label, IconComponent, subLinks, onClick } = props;
  const [isExpanded, setIsExpanded] = useState(false);

  const hasActiveSublink = subLinks?.some(link => link.to && location.pathname.startsWith(link.to));
  const isActive = hasActiveSublink;

  const onHeaderClick = () => {
    if (onClick) {
      onClick();
    } else {
      setIsExpanded(!isExpanded);
    }
  };

  useEffect(() => {
    setIsExpanded(isActive ?? false); // Ensure isExpanded gets boolean
  }, [isActive]);

  const renderList = subLinks?.map(link => {
    return <SidebarDirectLink key={link.to || link.label} {...link} />;
  });

  return (
    <div
      className={`${isActive ? 'font-semibold opacity-100' : ''} sidebar-link-container opacity-70 hover:opacity-100 transition-opacity`}
    >
      <div className="sidebar-link-container-header cursor-pointer flex items-center" onClick={onHeaderClick}>
        {IconComponent && <div className="sidebar-link-item-icon">{IconComponent}</div>}
        <div className="sidebar-link-container-label flex-grow font-roboto text-[18px]">
          {label}
        </div>
        {subLinks &&
          subLinks.length > 0 &&
          (isExpanded ? (
            <BiChevronUp className="h-5 w-5 flex-shrink-0" />
          ) : (
            <BiChevronDown className="h-5 w-5 flex-shrink-0" />
          ))}
      </div>
      {isExpanded && subLinks && subLinks.length > 0 && (
        <div className={`sidebar-link-container-links border-l ${classes.sidebarBorder}`}>{renderList}</div>
      )}
    </div>
  );
};


const SidebarLink: React.FC<SidebarLinkProps> = (props: SidebarLinkProps) => {
  const { subLinks } = props;
  const Component = subLinks && subLinks.length > 0 ? SidebarLinkContainer : SidebarDirectLink;
  return <Component {...props} />;
};

export default SidebarLink; 
