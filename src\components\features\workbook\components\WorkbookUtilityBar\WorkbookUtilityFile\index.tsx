import type { RetrievalFile } from '@/types'
import React, { useEffect, useRef, useState } from 'react';

import { MdOutlineFilePresent } from "react-icons/md";
// import { HiOutlineDownload } from "react-icons/hi";
import { LuTrash } from "react-icons/lu";
import { <PERSON><PERSON><PERSON><PERSON>, CircleLoader } from 'react-spinners';

import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { 
    fetchFileIndexStatus, 
    removeWorkbookFileById,
    selectGlobalWorkbookFilePendingStatus,
    selectUserWorkbookFilePendingStatus, 
} from '@/store/slices/workbookSlice';

import './styles.scss';

export type WorkbookUtilityFileProps = {
    file: RetrievalFile;
    isGlobal: boolean;
    allowDelete: boolean;
    workbookId?: string;
}

const INDEX_POLL_INTERVAL_MS = 30 * 1000;

const WorkbookUtilityFile: React.FC<WorkbookUtilityFileProps> = (props) => {
    const { workbookId, file, isGlobal, allowDelete } = props;
    const [isDeleting, setIsDeleting] = useState(false);

    const dispatch = useAppDispatch();
    const pendingStatus = isGlobal ? selectGlobalWorkbookFilePendingStatus : selectUserWorkbookFilePendingStatus;
    const filePendingStatus = useAppSelector(state => pendingStatus(state, workbookId ?? "", file?.id ?? ''));
    const indexIntervalRef = useRef<number | null>(null)

    useEffect(() => {
        setIsDeleting(filePendingStatus?.isDeleting ?? false);
      }, [filePendingStatus])

    const checkIndexStatus = async () => {
        if (workbookId && file && file.id !== file.name) {
            await dispatch(fetchFileIndexStatus({
                workbookId: workbookId!,
                fileId: file.id,
                isGlobal: isGlobal
            })).unwrap();
        }
    }

    const onFileDelete = async () => {
        if (allowDelete && !isDeleting && file && file.id && workbookId) {
            await dispatch(removeWorkbookFileById({
                workbookId: workbookId,
                fileId: file.id,
                isGlobal: isGlobal
            }));
        }
    }

    useEffect(() => {
        if (file && !file.isChunked) {
            checkIndexStatus();
            indexIntervalRef.current = setInterval(() => {
                checkIndexStatus()
            }, INDEX_POLL_INTERVAL_MS)
        }
        return () => {
            if (indexIntervalRef.current) {
                clearInterval(indexIntervalRef.current);
            }
        }
    }, [dispatch, workbookId]);

    useEffect(() => {
        if (file.isChunked && indexIntervalRef.current) {
            clearInterval(indexIntervalRef.current);
        }
    }, [file])
    
    const fileLabelIcon = file.isChunked ? 
        <MdOutlineFilePresent fill='#F7987D' width={16} height={16} />
        :
        <DotLoader loading={true} color='#ECF7FF' size={14} />
    return (
        <div className="workbook-utility-file-main">
            <div className="workbook-utility-file-label">
                <div className="file-label-icon">
                    {fileLabelIcon}
                </div>
                <div className="file-label-text">
                    {file?.name}
                </div>
            </div>
            <div className="workbook-utility-file-actions">
                {allowDelete && (
                    <div className="file-action-delete" onClick={onFileDelete}>
                        {isDeleting ? <CircleLoader loading={isDeleting} color="red" size={14}/> : <LuTrash />}
                    </div>
                )}
            </div>
        </div>
    )
}

export default WorkbookUtilityFile;
