pervious markdwonrender
```
import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkBreaks from 'remark-breaks';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus, vs } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { useThemeStyles } from '@/hooks/useThemeStyles';

interface CodeBlockProps {
  inline?: boolean;
  className?: string;
  children: React.ReactNode;
  [key: string]: unknown;
}

interface MarkdownRendererProps {
  content: string;
  className?: string;
  isUser?: boolean;
  isInline?: boolean;
}

// Normalize markdown content: remove excessive line breaks, preserve code blocks
const preprocessMarkdown = (content: string): string => {
  const codeBlocks: string[] = [];

  let processedContent = content.replace(/```[\s\S]*?```/g, match => {
    codeBlocks.push(match);
    return `__CODE_BLOCK_${codeBlocks.length - 1}__`;
  });

  processedContent = processedContent.replace(/\n{3,}/g, '\n\n');
  processedContent = processedContent.replace(/(#{1,6}[^\n]+)\n{2,}(\*|\d+\.)/g, '$1\n$2');

  codeBlocks.forEach((block, index) => {
    processedContent = processedContent.replace(`__CODE_BLOCK_${index}__`, block);
  });

  return processedContent;
};

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  className = '',
  isUser = false,
  isInline = false,
}) => {
  const { isDarkMode } = useThemeStyles();
  const textColor = isUser ? 'text-white' : '';
  const processedContent = isInline ? content : preprocessMarkdown(content);

  // Style config based on theme and user mode
  const codeBlockBg = isUser
    ? 'bg-[#1E3A5F] border border-[#2C4A6F]'
    : 'bg-[#f0f7ff] dark:bg-[#1A2234] border border-[#d0e3ff] dark:border-[#2A3244]';

  const inlineCodeBg = isUser
    ? 'bg-[#1E3A5F] border border-[#2C4A6F]'
    : 'bg-[#f0f7ff] dark:bg-[#1A2234] border border-[#d0e3ff] dark:border-[#2A3244]';

  const tableHeaderBg = isUser
    ? 'bg-[#002D4F] border-b border-[#0A4A7A]'
    : 'bg-[#cfe8ff] dark:bg-[#0A2040] border-b border-[#bdd9f5] dark:border-[#1A3A5A]';

  const tableBorder = isUser ? 'border-[#0A4A7A]' : 'border-[#d0e3ff] dark:border-[#1A3A5A]';

  const syntaxStyle = isDarkMode ? vscDarkPlus : vs;

  // Syntax highlighting for code blocks with optional copy button
  const CodeBlock = ({ inline, className, children, ...props }: CodeBlockProps) => {
    const [copied, setCopied] = useState(false);
    const match = /language-(\w+)/.exec(className || '');
    const lang = match ? match[1] : '';
    const codeText = String(children).replace(/\n$/, '');

    const handleCopy = () => {
      navigator.clipboard.writeText(codeText);
      setCopied(true);
      setTimeout(() => setCopied(false), 1500);
    };

    if (inline) {
      return (
        <code
          className={`px-1 py-0.5 rounded text-blue-700 dark:text-blue-300 ${inlineCodeBg} ${className || ''}`}
          {...props}
        >
          {children}
        </code>
      );
    }

    if (!lang || codeText.length < 10) {
      return (
        <pre className={`my-1.5 rounded overflow-auto shadow-sm ${textColor}`}>
          <code
            className={`block p-3 rounded w-full ${codeBlockBg} text-gray-800 dark:text-gray-200 ${className || ''}`}
            {...props}
          >
            {children}
          </code>
        </pre>
      );
    }

    return (
      <div className={`my-2 rounded overflow-hidden shadow-sm ${isUser ? 'bg-[#002D4F]' : ''}`}>
        <div className="px-3 py-1 text-xs text-gray-500 bg-gray-100 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <span>{lang.toUpperCase()}</span>
          <button
            onClick={handleCopy}
            title={copied ? 'Copied ✅' : 'Copy Code'}
            className="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
          >
            {copied ? '✅ Copied' : 'Copy'}
          </button>
        </div>
        <SyntaxHighlighter
          style={syntaxStyle}
          language={lang}
          customStyle={{
            margin: 0,
            padding: '12px',
            borderRadius: 0,
            background: isUser ? '#002D4F' : isDarkMode ? '#1A2234' : '#f0f7ff',
          }}
        >
          {codeText}
        </SyntaxHighlighter>
      </div>
    );
  };

  const WrapperComponent = isInline ? 'span' : 'div';

  return (
    <WrapperComponent className={`markdown-content text-sm leading-snug ${className} ${isInline ? 'inline' : ''}`}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm, remarkBreaks]}
        components={{
          h1: props =>
            isInline ? (
              <span className={`font-bold ${textColor}`} {...props} />
            ) : (
              <h1 className={`text-2xl font-bold mb-2 ${textColor}`} {...props} />
            ),
          h2: props =>
            isInline ? (
              <span className={`font-bold ${textColor}`} {...props} />
            ) : (
              <h2 className={`text-xl font-bold mb-1 ${textColor}`} {...props} />
            ),
          h3: props =>
            isInline ? (
              <span className={`font-bold ${textColor}`} {...props} />
            ) : (
              <h3 className={`text-lg font-bold mb-1 ${textColor}`} {...props} />
            ),
          h4: props =>
            isInline ? (
              <span className={`font-bold ${textColor}`} {...props} />
            ) : (
              <h4 className={`text-base font-bold mb-1 ${textColor}`} {...props} />
            ),
          h5: props =>
            isInline ? (
              <span className={`font-bold ${textColor}`} {...props} />
            ) : (
              <h5 className={`text-sm font-bold mb-1 ${textColor}`} {...props} />
            ),
          h6: props =>
            isInline ? (
              <span className={`font-bold ${textColor}`} {...props} />
            ) : (
              <h6 className={`text-xs font-bold mb-1 ${textColor}`} {...props} />
            ),

          ul: props =>
            isInline ? <span {...props} /> : <ul className={`list-disc ml-5 mb-1 ${textColor}`} {...props} />,
          ol: props =>
            isInline ? <span {...props} /> : <ol className={`list-decimal ml-5 mb-1 ${textColor}`} {...props} />,
          li: props =>
            isInline ? (
              <span className={textColor} {...props} />
            ) : (
              <li className={`ml-1 leading-tight ${textColor}`} {...props}>
                <div className="mt-0 mb-0">{props.children}</div>
              </li>
            ),

          p: props =>
            isInline ? <span className={textColor} {...props} /> : <p className={`mb-0.5 ${textColor}`} {...props} />,

          strong: props => <strong className={`font-bold ${textColor}`} {...props} />,
          em: props => <em className={`italic ${textColor}`} {...props} />,
          a: props => (
            <a
              className={`text-blue-500 hover:underline ${textColor}`}
              {...props}
              target="_blank"
              rel="noopener noreferrer"
            />
          ),

          // Use custom code block
          // @ts-expect-error - React-markdown has complex typing that's difficult to satisfy with our custom component
          code: CodeBlock,

          pre: ({ children }) => (isInline ? <span>{children}</span> : <div className="my-2">{children}</div>),

          table: props =>
            isInline ? (
              <span {...props} />
            ) : (
              <div className="overflow-x-auto my-2">
                <table
                  className={`min-w-full border-collapse text-sm ${tableBorder} border rounded ${textColor}`}
                  {...props}
                />
              </div>
            ),

          thead: props =>
            isInline ? <span {...props} /> : <thead className={`${tableHeaderBg} text-white`} {...props} />,
          tbody: props => (isInline ? <span {...props} /> : <tbody className={textColor} {...props} />),
          tr: props =>
            isInline ? <span {...props} /> : <tr className={`border-b ${tableBorder} ${textColor}`} {...props} />,
          th: props =>
            isInline ? <span {...props} /> : <th className="px-3 py-2 text-left font-semibold" {...props} />,
          td: props =>
            isInline ? (
              <span {...props} />
            ) : (
              <td
                className="px-3 py-1 align-top border-r last:border-r-0 border-gray-200 dark:border-gray-700"
                {...props}
              />
            ),

          blockquote: props =>
            isInline ? (
              <span className={`italic ${textColor}`} {...props} />
            ) : (
              <blockquote
                className={`border-l-4 border-blue-300 dark:border-blue-700 pl-4 py-1 my-2 italic bg-blue-50 dark:bg-blue-900/20 rounded-r ${textColor}`}
                {...props}
              />
            ),

          hr: props => (isInline ? null : <hr className="my-4 border-gray-300 dark:border-gray-600" {...props} />),
          img: () => <span>[Image]</span>,
        }}
      >
        {processedContent}
      </ReactMarkdown>
    </WrapperComponent>
  );
};

export default MarkdownRenderer;

```


pervious citationrender
```
import React, { useState } from 'react';
import {
  CitationRendererProps,
  RetrievalCitation,
  RetrievalCitationSource,
} from '@/components/features/workbook/workbookTypes';
import MarkdownRenderer from './MarkdownRenderer';
import { FiChevronDown, FiChevronUp, FiFile } from 'react-icons/fi';

interface EnhancedCitationRendererProps extends CitationRendererProps {
  renderWithMarkdown?: boolean;
}

interface InlineCitationProps {
  index: number;
  citation: RetrievalCitation;
  children: React.ReactNode;
}

// Helper function to truncate text with ellipsis
const truncateText = (text: string, maxLength: number = 100): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

const InlineCitation: React.FC<InlineCitationProps> = ({ index, children }) => {
  const circleStyle = 'text-blue-700 dark:text-blue-400 text-xs font-bold cursor-pointer';

  return (
    <span
      style={{
        display: 'inline',
        marginRight: '0.1em',
      }}
    >
      {children}
      <sup
        className={circleStyle}
        style={{
          display: 'inline-block',
          verticalAlign: 'super',
          fontSize: '0.75em',
          lineHeight: 0,
          marginLeft: '0.1em',
          position: 'relative',
          top: '-0.1em',
        }}
        onClick={() => document.getElementById(`citation-detail-${index}`)?.scrollIntoView({ behavior: 'smooth' })}
      >
        [{index + 1}]
      </sup>
    </span>
  );
};

const CitationRenderer: React.FC<EnhancedCitationRendererProps> = ({
  message,
  citations = [],
  citationSources = [],
  isUser,
  textContentStyle,
  renderWithMarkdown = false,
}) => {
  // State: controls expansion/collapse of the bottom citation list
  const [showCitationList, setShowCitationList] = useState(false);
  // Use a proper type for the expandedCitations state
  const [expandedCitations, setExpandedCitations] = useState<{ [key: string]: boolean }>({});

  // If no citations, return the content directly
  if (!citations || citations.length === 0) {
    if (renderWithMarkdown) {
      return <MarkdownRenderer content={message} className={textContentStyle} isUser={isUser} />;
    } else {
      return <div className={textContentStyle}>{message}</div>;
    }
  }

  // Sort by start_index to ensure correct processing order
  const sortedCitations = [...citations].sort((a, b) => a.start_index - b.start_index);

  let lastIndex = 0;
  const parts: React.ReactNode[] = [];

  // Process and split content with citations
  sortedCitations.forEach((citation, idx) => {
    // Text: content before citation
    if (citation.start_index > lastIndex) {
      const textBeforeCitation = message.substring(lastIndex, citation.start_index);
      const cleanedText = textBeforeCitation.replace(/\s+$/, '');

      if (renderWithMarkdown) {
        parts.push(
          <span key={`text-${idx}`} style={{ display: 'inline' }}>
            <MarkdownRenderer content={cleanedText} isUser={isUser} isInline={true} />
          </span>
        );
      } else {
        parts.push(
          <span key={`text-${idx}`} style={{ display: 'inline' }}>
            {cleanedText}
          </span>
        );
      }
    }

    // Get the cited text
    const citedText = message.substring(citation.start_index, citation.end_index);

    const cleanedCitedText = citedText.replace(/\s+$/, '');

    // Add inline citation with InlineCitation wrapper (includes tooltip)
    parts.push(
      <InlineCitation key={`citation-${idx}`} index={idx} citation={citation}>
        {renderWithMarkdown ? (
          <MarkdownRenderer content={cleanedCitedText} isUser={isUser} isInline={true} />
        ) : (
          cleanedCitedText
        )}
      </InlineCitation>
    );

    lastIndex = citation.end_index;
  });

  // Add trailing text
  if (lastIndex < message.length) {
    const textAfterLastCitation = message.substring(lastIndex);
    if (renderWithMarkdown) {
      parts.push(
        <span key="text-end">
          <MarkdownRenderer content={textAfterLastCitation} isUser={isUser} isInline={true} />
        </span>
      );
    } else {
      parts.push(<span key="text-end">{textAfterLastCitation}</span>);
    }
  }

  // Toggle expansion of a citation source
  const toggleCitationExpansion = (idx: number) => {
    setExpandedCitations(prev => ({
      ...prev,
      [idx.toString()]: !prev[idx.toString()],
    }));
  };

  return (
    <div className={textContentStyle}>
      {/* Main content with inline citations */}
      <div
        style={{
          marginBottom: '0.5rem',
          lineHeight: '1.5',
          wordBreak: 'break-word',
          display: 'inline',
        }}
      >
        {parts.map((part, idx) => (
          <React.Fragment key={`part-${idx}`}>{part}</React.Fragment>
        ))}
      </div>

      {/* Bottom citation list with collapse/expand button */}
      <div style={{ marginTop: '0.75rem', paddingTop: '0.25rem' }}>
        <button
          onClick={() => setShowCitationList(prev => !prev)}
          className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 flex items-center gap-1 focus:outline-none transition-colors"
        >
          {showCitationList ? <FiChevronUp className="text-blue-600" /> : <FiChevronDown className="text-blue-600" />}
          {showCitationList ? 'Hide Citations' : 'Show Citations'}
        </button>

        {showCitationList && (
          <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
            {sortedCitations.map((citation, idx) => {
              // Get the cited text for display
              const citedText = message.substring(citation.start_index, citation.end_index);
              const isExpanded = expandedCitations[idx.toString()] || false;

              // Handle long cited text with expand/collapse
              const displayCitedText = isExpanded ? citedText : truncateText(citedText, 150);

              return (
                <div
                  id={`citation-detail-${idx}`}
                  key={idx}
                  className="mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
                >
                  <div className="flex items-center mb-2">
                    <span className="inline-flex items-center justify-center w-5 h-5 text-xs font-bold rounded-full mr-2 bg-blue-100 text-blue-700 dark:bg-blue-800 dark:text-blue-100">
                      {idx + 1}
                    </span>
                    <div className="font-bold text-gray-800 dark:text-gray-200">Citation</div>
                  </div>

                  {/* Display the cited text */}
                  <div className="mb-2 px-3 py-2 bg-white dark:bg-gray-700 border-l-4 border-blue-500 rounded">
                    <div className="text-sm text-gray-800 dark:text-gray-200 italic">
                      "{displayCitedText}"
                      {citedText.length > 150 && (
                        <button
                          className="text-blue-600 dark:text-blue-400 ml-1 text-xs underline focus:outline-none"
                          onClick={() => toggleCitationExpansion(idx)}
                        >
                          {isExpanded ? 'Show less' : 'Show more'}
                        </button>
                      )}
                    </div>
                  </div>

                  {/* List all sources for this citation */}
                  <div className="pl-2">
                    {citation.citation_sources.map((source_index: number, sourceIdx: number) => {
                      const citation_source: RetrievalCitationSource = citationSources[source_index]
                      const sourceKey = `${idx}-${sourceIdx}`;
                      const isSourceExpanded = expandedCitations[sourceKey] || false;
                      const displayContent = isSourceExpanded
                        ? citation_source.chunk_content
                        : truncateText(citation_source.chunk_content, 100);

                      return (
                        <div key={sourceIdx} className="mb-2">
                          <div className="flex items-center">
                            <FiFile className="mr-1 text-blue-600 dark:text-blue-400" size={12} />
                            <div className="italic text-sm text-gray-700 dark:text-gray-300">{citation_source.name}</div>
                          </div>
                          <div className="text-xs text-gray-600 dark:text-gray-400 mt-1 pl-4 border-l border-gray-200 dark:border-gray-700">
                            {displayContent}
                            {citation_source.chunk_content.length > 100 && (
                              <button
                                className="text-blue-600 dark:text-blue-400 ml-1 text-xs underline focus:outline-none"
                                onClick={() =>
                                  setExpandedCitations(prev => ({
                                    ...prev,
                                    [sourceKey]: !isSourceExpanded,
                                  }))
                                }
                              >
                                {isSourceExpanded ? 'Show less' : 'Show more'}
                              </button>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default CitationRenderer;

```