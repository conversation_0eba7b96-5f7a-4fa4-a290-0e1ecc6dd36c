function createPPTButton() {
  let label = "PowerPoint";
  let options = [
    { value: "null", text: "Export to PowerPoint" },
    { value: "Allegheny Health Network", text: "Allegheny Health Network" },
    {value:"enGen", text:"enGen"},
    { value: "Highmark Health", text: "Highmark Health" },
    { value: "Highmark", text: "Highmark" },
    { value: "Highmark Blue Shield", text: "Highmark Blue Shield" },
    {
      value: "Highmark Blue Cross Blue Shield",
      text: "Highmark Blue Cross Blue Shield",
    },
  ];

  const response = "Use Same Pattern as Co<PERSON>";

  const buttonContainer = document.createElement("div");
  buttonContainer.classList.add("ppt-button");
  //   buttonContainer.classList.add("chat-button");

  //   const labelButton = document.createElement("button");
  //   labelButton.textContent = label;
  //   labelButton.type = "button";
  //   labelButton.classList.add("label-ppt-button");
  //   labelButton.addEventListener("click", generatePPT);
  //   buttonContainer.appendChild(labelButton);

  const spinnerDIV = document.createElement("div");
  spinnerDIV.classList.add("spinner");
  spinnerDIV.style.display = "none";
  buttonContainer.appendChild(spinnerDIV);

  const selectPPT = document.createElement("select");
  selectPPT.classList.add("ppt-templates");
  buttonContainer.appendChild(selectPPT);

  options.forEach((option) => {
    const optionElement = document.createElement("option");
    optionElement.value = option.value;
    optionElement.textContent = option.text;
    if (option.value == "null") {
      optionElement.classList.add("centerTemplate");
    }
    selectPPT.appendChild(optionElement);
  });

  selectPPT.addEventListener("change", generatePPT);
  return buttonContainer;
}

//From button
let b;
async function generatePPT(event) {
  const clickedButton = event.target;
  if (clickedButton.tagName === "SELECT") {
    const childElement = clickedButton.children[1];
    b = clickedButton;
    let value =
      clickedButton.parentElement.parentElement.parentElement.childNodes[0]
        .childNodes[0].childNodes[1].textContent;
    let option = clickedButton.value;

    let fetchInput = { input: value, template: option };

    if (option !== "null") {
      clickedButton.selectedIndex = 0;
      clickedButton.style.display = "none";
      clickedButton.disabled = true;
      clickedButton.parentElement.disabled = true;
      const spinner = clickedButton.parentElement.childNodes[0];
      spinner.style.display = "block";
      await fetchPowerPoint(fetchInput);
      spinner.style.display = "none";
      clickedButton.style.display = "block";
      clickedButton.parentElement.disabled = false;
      clickedButton.disabled = false;
    }
  }
}

async function fetchPowerPoint(fetchInput) {
  try {
    const response = await fetch("/sidekick/restful/powerpoint", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(fetchInput),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.success) {
      // Open the download URL in a new tab
      window.open(data.download_url, "_blank", "noopener noreferrer");
    } else {
      console.error("Error fetching PowerPoint:", data.error || data.message);
    }
  } catch (error) {
    console.error("Error sending data:", error);
  }
}
