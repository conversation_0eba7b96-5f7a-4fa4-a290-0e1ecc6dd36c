Z0# Sidekick Frontend

这是Sidekick Frontend项目的主要文档。本文档整合了项目的架构说明、组件结构、布局系统和项目规范。

## 项目概述

Sidekick Frontend 是一个基于 React、TypeScript 和 Tailwind CSS 的现代 Web 应用程序，提供聊天和工作簿（Workbook）功能。该应用程序使用 React Router v6 进行路由管理，采用嵌套布局系统实现灵活的 UI 组织。

## 核心特性

- **聊天系统**: 支持发送消息、加载历史消息和分页功能
- **工作簿功能**: 个人和公共工作簿管理，包括创建、浏览和编辑
- **响应式布局**: 支持桌面和移动设备的完美显示
- **主题系统**: 支持亮色/暗色主题切换

## 文件夹结构

```
src/
├── assets/       - 静态资源文件
├── components/   - 组件库
│   ├── common/   - 通用 UI 组件
│   └── features/ - 功能性组件
│       ├── mainChat/ - 主聊天功能组件
│       ├── theme/    - 主题相关组件
│       └── workbook/ - 工作簿相关组件
├── config/       - 应用配置
├── contexts/     - React Context 存储
├── data/         - 数据和 API 模拟
├── hooks/        - 自定义 React Hooks
├── layouts/      - 布局组件
│   └── content/  - 内容区域布局
├── pages/        - 页面组件
├── types/        - TypeScript 类型定义
└── utils/        - 工具函数
```

## 应用架构

### 路由系统

应用使用嵌套路由结构，支持以下主要路径:

- `/chat` - 主聊天界面
- `/workbooks` - 工作簿列表和管理
  - `/workbooks/personal` - 个人工作簿
  - `/workbooks/public` - 公共工作簿
  - `/workbooks/:workbookId` - 工作簿详情页
  - `/workbooks/:workbookId/chat` - 工作簿聊天界面

### 布局系统

本应用采用嵌套布局架构，使用React Router的Outlet组件实现层级化的UI渲染。布局系统的设计目标是：

1. 职责分离 - UI渲染与业务逻辑分离
2. 代码复用 - 共享布局组件减少重复代码 
3. 可维护性 - 清晰的层次结构便于理解和维护

布局系统采用以下嵌套结构:

```
RootLayout (全局 Providers, 提供全局上下文)
  └── LayoutController (根据路由控制布局渲染)
      ├── ChatLayout (聊天专用布局)
      │   └── MainChatPage (聊天页面内容)
      │
      └── WorkbookLayout (工作簿专用基础布局)
          ├── WorkbookContentLayout (带一致间距的内容容器)
          │   └── WorkbookListView (个人/公共工作簿列表视图)
          │
          └── WorkbookDashboardLayout (单个工作簿仪表板)
              ├── WorkbookOverviewView (工作簿详情页)
              └── WorkbookChatPage (工作簿聊天界面)
```

#### 布局组件职责

##### RootLayout
- 提供全局Context (主题、侧边栏状态、工作簿数据)
- 不包含任何视觉UI元素
- 使用Outlet渲染子路由

##### LayoutController
- 根据当前路由控制布局渲染
- 连接路由逻辑与UI组件
- 决定各布局区域渲染的内容
- 使用useLayoutNavigation hook处理路由和导航逻辑

##### AppLayout
- 提供应用程序的基本UI框架
- 渲染侧边栏、主内容区域和可选的右侧面板
- 纯UI组件，通过props接收所需数据
- 使用Outlet渲染子路由

#### 布局相关Hook

##### useLayoutNavigation
- 集中处理路由匹配和导航逻辑
- 决定当前活动的侧边栏项
- 处理侧边栏导航
- 根据路由准备工作簿相关数据

### 状态管理

- **Context API**: 用于全局状态管理，包括主题和侧边栏状态
- **自定义 Hooks**: 用于组件级状态管理和逻辑封装

## 主要组件

### 聊天组件

- **MainChatView**: 主聊天页面视图
- **useConversation**: 聊天对话管理 Hook，支持消息发送、历史加载和分页

### 工作簿组件

- **WorkbookLayout**: 工作簿基础布局
- **WorkbookListView**: 工作簿列表视图
- **WorkbookCard**: 工作簿卡片组件
- **WorkbookDashboard**: 工作簿详情仪表板
- **WorkbookChatView**: 工作簿聊天界面

### 主题组件

- **ThemeContext**: 主题上下文，提供主题切换功能
- **useThemeStyles**: 主题样式 Hook，根据当前主题提供样式

## API 与数据

- **mockAPI**: 模拟后端 API 请求
- **mockWorkbooks**: 模拟工作簿数据

## 设计原则

1. **单一职责** - 每个组件只负责一个特定功能
2. **关注点分离** - UI渲染与业务逻辑分离
3. **组合优于继承** - 使用组合模式构建UI
4. **自上而下数据流** - 数据通过props从父组件流向子组件

## 添加新页面指南

添加新页面时，应考虑以下步骤：

1. 确定需要使用的布局（现有或新建）
2. 在routes.tsx中添加新路由，确保正确的布局嵌套
3. 如需特定的布局功能，可考虑创建新的布局组件
4. 如需特定的路由逻辑，更新useLayoutNavigation hook

## 项目导入规范

为了使代码更加清晰和易于维护，本项目使用了路径别名系统来简化导入路径。

### 可用的路径别名

以下是您可以在项目中使用的路径别名：

| 别名            | 对应路径                    | 用途           |
| --------------- | --------------------------- | -------------- |
| `@/*`           | `src/*`                     | 所有源码文件   |
| `@components/*` | `src/components/*`          | 通用组件       |
| `@features/*`   | `src/components/features/*` | 功能组件       |
| `@layouts/*`    | `src/layouts/*`             | 布局组件       |
| `@pages/*`      | `src/pages/*`               | 页面组件       |
| `@hooks/*`      | `src/hooks/*`               | React Hooks    |
| `@utils/*`      | `src/utils/*`               | 工具函数       |
| `@types/*`      | `src/types/*`               | 类型定义       |
| `@data/*`       | `src/data/*`                | 数据和常量     |
| `@contexts/*`   | `src/contexts/*`            | React Contexts |
| `@common/*`     | `src/components/common/*`   | 通用UI组件     |

### 导入类型

项目使用统一的类型导入方式：

```typescript
// 直接导入单个类型
import { SomeType } from '@/types/someFile';

// 使用命名空间导入所有类型
import { WorkbookTypes } from '@/types';
```

### 导入组件

通过 index.ts 文件导出的组件可以这样导入：

```typescript
// 导入单个组件
import { Component } from '@features/feature';

// 导入多个组件
import { ComponentA, ComponentB } from '@features/feature';
```

### 导入顺序规范

为保持代码一致性，请按照以下顺序排列导入：

1. React 和第三方库导入
2. 路径别名导入
3. 相对路径导入

```typescript
// React 和第三方库
import React, { useState } from 'react';
import { useRouter } from 'next/router';

// 路径别名导入
import { useAuth } from '@hooks/useAuth';
import { Button } from '@components/ui';

// 相对路径导入
import styles from './Component.module.css';
```
