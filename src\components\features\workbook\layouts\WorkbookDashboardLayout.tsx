import React, { useEffect } from 'react';
import { Outlet, useParams, useLocation, useNavigate } from 'react-router-dom';
import { useWorkbook } from '../WorkbookContext';


const styles = {
  container: "flex flex-col w-full h-full",
  loadingContainer: "flex justify-center items-center w-full h-64"
};


const isWorkbookDashboardPath = (path: string): boolean => {
  return (
    path.includes('/workbooks/') &&
    !path.includes('/workbooks/my') &&
    !path.includes('/workbooks/public')
  );
};

const WorkbookDashboardLayout: React.FC = () => {
  const { workbookId } = useParams<{ workbookId: string }>();
  const location = useLocation();
  const navigate = useNavigate();
  const { currentWorkbook, loading, error, setCurrentWorkbookById, resetWorkbookState } =
    useWorkbook();

  useEffect(() => {
    if (workbookId) {
      setCurrentWorkbookById(workbookId);
    }

    return () => {
      resetWorkbookState();
    };
  }, [workbookId, setCurrentWorkbookById, resetWorkbookState]);


  useEffect(() => {
    if (!loading && error) {
      console.error('Error loading workbook:', error);
      navigate('/workbooks/my', { replace: true });
    }
  }, [loading, error, navigate]);

  // route change detection 
  useEffect(() => {
    const isNowOnWorkbookDashboard = isWorkbookDashboardPath(location.pathname);
    const isThisWorkbookDashboard = location.pathname.includes(`/workbooks/${workbookId}`);

    if (!isNowOnWorkbookDashboard || !isThisWorkbookDashboard) {
      resetWorkbookState();
    }
  }, [location.pathname, workbookId, resetWorkbookState]);


  if (loading) {
    return <div className={styles.loadingContainer}>Loading...</div>;
  }


  if (!currentWorkbook) {
    return null;
  }

  return (
    <div className={styles.container}>
      <Outlet />
    </div>
  );
};

export default WorkbookDashboardLayout;
