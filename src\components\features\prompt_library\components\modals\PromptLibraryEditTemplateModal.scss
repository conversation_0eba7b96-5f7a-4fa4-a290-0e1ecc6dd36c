/* Block: prompt-edit */
.prompt-edit__fields {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    align-self: stretch;
    width: 710px;
    height: 256px;
    gap: 16px;
    overflow-y: auto;
    &::-webkit-scrollbar {
      width: 6px;
    }
    &::-webkit-scrollbar-track {
        border-radius: calc(infinity * 1px);
    }
    &::-webkit-scrollbar-thumb {
        border-radius: 4px;
        background-color: #0066B1;
    }
  }
  
  .prompt-edit__session-select-container {
    display: flex;
    justify-content: center;
    align-items: center;
    background: #00223C;
    width: 120px;
    border-radius: 8px;
    
    .prompt-edit__session-select {
      border: 2px solid #0066B1;
      padding: 8px;
      color: #FFF;
      border-radius: 8px;
      background-color: #00223C;
      font-size: 16px;
      cursor: pointer;
      width: 120px;
      outline: none;
    }

    &:focus {
      outline: none;
    }

    .prompt-edit__session-select option:hover {
      background-color: #003963;
    }
  }
  
  .prompt-edit__field {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    align-self: stretch;
    border-radius: 2px;
    width: 704px;
  }
  
  .prompt-edit__field--name {
    height: 48px;
  }
  
  .prompt-edit__field--content {
    height: 192px;
  }
  
  .prompt-edit__field--filled {
    color: #FFFFFF;
  }
  
  .prompt-edit__field--empty {
    color: #495055;
  }
  
  .prompt-edit__input-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    align-self: stretch;
    width: 704px;
    background-color: #00223C;
    border: 1px solid #6D787F;
    border-radius: 2px;
    padding: 0 12px;
  }
  
  .prompt-edit__input-container--focused {
    border: 2px solid #0066B1;
  }
  
  .prompt-edit__input-container--large {
    height: 192px;
  }
  
  .prompt-edit__input-wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
    align-self: stretch;
    flex-shrink: 0;
    width: 680px;
    min-height: 24px;
    min-width: 24px;
    padding: 8px 0;
  }
  
  .prompt-form__textarea-wrapper {
    display: flex;
    flex-direction: row;
    align-self: stretch;
    flex-shrink: 0;
    width: 680px;
    height: 192px;
    min-height: 26px;
    padding: 8px 0px;
  }
  
  .prompt-edit__input, .prompt-edit__textarea {
    font-weight: 500;
    font-size: 16px;
    outline: none;
    width: 100%;
    line-height: 25.6px;
    letter-spacing: 0.128px;
  }
  
  .prompt-edit__textarea {
    resize: none;
    white-space: pre-wrap;
  }
  
  .prompt-edit__icon-wrapper {
    display: flex;
    align-items: center;
    width: 24px;
    height: 24px;
  }
  
  .prompt-edit__icon {
    width: 24px;
    height: 24px;
  }
  
  .prompt-edit__actions {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    width: 704px;
    height: 40px;
  }
  
  .prompt-edit__button {
    display: flex;
    align-items: center;
    gap: 8px;
    height: 40px;
    padding: 8px 12px;
    border-radius: 50px;
    border: 1px solid;
    outline: none;
    white-space: nowrap;
    cursor: pointer;
  }
  
  .prompt-edit__button--delete {
    width: 169px;
  }
  
  .prompt-edit__button--delete-active {
    border-color: #F7987D;
    color: #F7987D;
  }
  
  .prompt-edit__button--delete-inactive {
    border-color: #6D787F;
    color: #6D787F;
  }
  
  .prompt-edit__button-group {
    display: flex;
    align-items: center;
    gap: 10px;
    height: 40px;
  }
  
  .prompt-edit__button-group--filled {
    width: 386px;
  }
  
  .prompt-edit__button-group--empty {
    width: 313px;
  }
  
  .prompt-edit__button--primary {
    width: 155px;
  }
  
  .prompt-edit__button--save-run {
    width: 220px;
    color: #FFFFFF;
    border-color: #0066B1;
  }
  
  .prompt-edit__button--save {
    width: 155px;
    color: #FFFFFF;
    border-color: #0066B1;
    background-color: #0066B1;
  }
  
  .prompt-edit__button--run {
    width: 149px;
    color: #6D787F;
    border-color: #6D787F;
  }
  
  .prompt-edit__button--outline {
    background-color: transparent;
  }
  
  .prompt-edit__button--filled {
    background-color: #0066B1;
  }
  
  .prompt-edit__button-content {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 24px;
    gap: 4px;
    padding: 0 4px 0 2px;
  }
  
  .prompt-edit__button-icon {
    width: 24px;
    height: 24px;
    flex-shrink: 0;
  }
  
  .prompt-edit__button-text {
    text-align: center;
    font-size: 16px;
    font-weight: 600;
    line-height: 16px;
    letter-spacing: 0.128px;
    height: 11px;
    flex-shrink: 0;
    margin: 0;
  }
