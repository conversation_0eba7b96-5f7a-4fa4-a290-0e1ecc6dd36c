import { client } from './client';
import { FILE_UPLOAD_BASE_URL } from './apiConfig';
import { parseApiError } from './apiUtils';

export interface FileMimeInfo {
  file_name: string;
  mime_type: string;
  file_size: number;
}

export interface RequestSignedUrlsBody {
  files_mimes: FileMimeInfo[];
  persist_to_session: boolean;
  session_id: string | null;
  session_type: string;
}

export interface SignedUrlInfo {
  gs_uri: string;
  mime_type: string;
  file_size: number;
  upload_time_utc: string;
  url: string;
}

export interface RequestSignedUrlsResponse {
  signed_urls: SignedUrlInfo[];
  firestore_session_id?: string;
}

/**
 * Requests signed URLs from the backend to allow direct GCS uploads.
 */
export const requestSignedUrlsForUpload = async (
  body: RequestSignedUrlsBody
): Promise<RequestSignedUrlsResponse> => {
  const endpoint = `${FILE_UPLOAD_BASE_URL}/upload_files`;
  try {
    console.log('Requesting signed URLs from:', endpoint, 'with body:', body);
    const response = await client.put<RequestSignedUrlsResponse>(endpoint, body);
    return response.data;
  } catch (error: unknown) {
    const parsedError = parseApiError(error);
    console.error('Error requesting signed URLs:', parsedError.originalError || error);
    throw new Error(parsedError.message);
  }
};

export const uploadFileToSignedUrl = async (
  signedUrl: string,
  file: File | Blob,
  mimeType: string,
  signal?: AbortSignal
): Promise<boolean> => {
  try {
    const response = await fetch(signedUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': mimeType,
      },
      body: file,
      signal: signal,
    });
    if (!response.ok) {
      console.error(
        `Failed to upload file to GCS. Status: ${response.status} ${response.statusText}`,
        await response.text()
      );
      if (signal?.aborted) {
        throw new Error('Upload to storage cancelled by user.');
      }
      throw new Error(`Storage upload failed: ${response.statusText || response.status}`);
    }
    return true;
  } catch (error: unknown) {
    const parsedError = parseApiError(error);
    if (
      (error as Error).name === 'AbortError' ||
      parsedError.message.includes('cancelled by user')
    ) {
      console.log('Upload to GCS was cancelled.');
      throw new Error('Upload to storage cancelled by user.');
    }
    console.error('Error uploading file directly to GCS:', parsedError.originalError || error);
    throw new Error(parsedError.message);
  }
};

/**
 * Delete a file from both GCS
 */
// export const deleteUploadedFile = async (sessionId: string, gcsPath: string): Promise<boolean> => {
//   try {
//     const deleteEndpoint = `${FILE_UPLOAD_BASE_URL}/${sessionId}/file`;

//     console.log(`[deleteUploadedFile] Calling endpoint: ${deleteEndpoint}`);
//     console.log(`[deleteUploadedFile] Deleting file: ${gcsPath} from session: ${sessionId}`);

//     const response = await fetch(deleteEndpoint, {
//       method: 'DELETE',
//       headers: {
//         'Content-Type': 'application/json',
//       },
//       body: JSON.stringify({
//         gcs_path: gcsPath,
//       }),
//     });

//     console.log(`[deleteUploadedFile] Response status: ${response.status}`);

//     if (!response.ok) {
//       const errorData = await response.json().catch(() => ({}));
//       const errorMessage =
//         errorData.error || `Delete failed: ${response.status} ${response.statusText}`;
//       console.error(
//         `[deleteUploadedFile] Failed to delete file. Status: ${response.status}`,
//         errorMessage
//       );
//       throw new Error(errorMessage);
//     }

//     const responseData = await response.json();
//     console.log('[deleteUploadedFile] File deleted successfully:', responseData.message);
//     return true;
//   } catch (error: unknown) {
//     const parsedError = parseApiError(error);
//     console.error('[deleteUploadedFile] Error deleting file:', parsedError.originalError || error);
//     throw new Error(parsedError.message);
//   }
// };

