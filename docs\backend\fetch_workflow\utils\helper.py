import datetime
import tempfile
import traceback
from typing import Dict, <PERSON>, <PERSON><PERSON>

from docx import Document
from werkzeug.utils import secure_filename

from lib.firestore_client import FirestoreClient
from models.Session import CreateSession, Session, SessionFile
from models.shared.enums import ModelName, SessionTypeEnum
from services.SessionService import SessionService
from website.google_cloud_init import GCS
from website.logger_framework import make_logger
from website.utils import db_utils, timings, upload_helper

workflow = "fetch_workflow"

logger_info, logger_error = make_logger(workflow, __file__)


def process_feedback(feedback_request, user) -> dict:
    # TODO: Revise to allow for Firestore aswell
    """Processes user feedback for a given response.

    Args:
        feedback_request (dict): A dictionary containing the feedback information.
        user (str): The email address of the user providing feedback.

    Returns:
        dict: The updated response record with feedback information.
    """
    info = True if feedback_request["info"] == 1 else False
    response_id = feedback_request["id"]
    session_id = feedback_request["sessionId"]

    session_info = GCS().fetch_user_session(user, session_id)

    updated_fields = {}
    for response in session_info["response"]:
        if response["prompt_id"] == response_id:
            response.update({"response_feedback": {"boolean": info}})
            response.update({"metadata": "response"})
            response.update({"file": ""})
            updated_fields = response

    db_utils.update_BQ_response_feedback(updated_fields)

    return updated_fields


def upsert_session_from_upload(request_body, session, signed_urls):
    user_email = session["email"]
    session_type = request_body.get("session_type", "chat")
    session_id = request_body.get("session_id", None)
    if session_id in ["null", "undefined", ""]:
        session_id = None

    session_client = FirestoreClient().InstanceClient(user_email)
    session_service = SessionService(persistence_client=session_client)

    session_files: List[SessionFile] = [
        SessionFile.from_upload_dict(file) for file in signed_urls
    ]

    if session_id:
        session_document = session_service.get_user_session_by_id(
            session_id, user_email
        )
        if session_document is None:
            return "Session not found", 404
        session_document = session_service.add_files(
            session_document, session_files)
        session_id = session_document.id
    else:
        new_session = CreateSession(
            user_email=user_email,
            model=(
                ModelName.FLASH
                if SessionTypeEnum[session_type.upper()] != SessionTypeEnum.MDLM
                else ModelName.MEDLM
            ),
            session_type=SessionTypeEnum[session_type.upper()],
            file_history=session_files,
        )

        session_document = session_service.create_new_session(new_session)
        session_id = session_document.id

    return {"signed_urls": signed_urls, "firestore_session_id": session_id}


def create_upload_url(request, session):
    """Generates signed URLs for file uploads.

    Args:
        request (flask.Request): The incoming request object.
        session (dict): The user's session information.

    Returns:
        tuple: A tuple containing the list of signed URLs and the HTTP status code.
    """
    user: str = session["email"]
    files = request.get_json().get("files_mimes", None)

    signed_urls = []
    file_metadata = []
    upload_event_time = datetime.datetime.now(datetime.timezone.utc)
    restricted_domains = ["ucci.com"]
    user_domain = f"{user.split('@')[1]}".lower()
    legal_file_name = ""
    if user_domain == restricted_domains[0]:
        legal_file_name = restricted_domains[0]
    for file in files:
        file_time = datetime.datetime.now(datetime.timezone.utc)
        file_name = file["file_name"]
        file_name = (
            f"{legal_file_name}{file_time.strftime('%Y-%m-%d %H-%M')}__{file_name}"
        )
        file_name = secure_filename(file_name)
        mime_type = file["mime_type"]
        file_size = file["file_size"]
        # signed_url = [True, f"http://127.0.0.1:8003/sidekick/test_{i}", f"gs://bkt-d-us-sdk-temp-file-activity-1of7/highmarkhealth.org/{user}/upload_test-{i}.txt"]
        signed_url = GCS().generate_upload_signed_url_v4(user, file_name, mime_type)
        signed_urls.append(
            {
                "url": signed_url[1],
                "gs_uri": signed_url[2],
                "mime_type": mime_type,
                "file_size": file_size,
                "upload_time_utc": upload_event_time.isoformat(),
            }
        )
        file_metadata.append({"mime_type": mime_type, "file_size": file_size})
    logger_info.info(
        "%s : create_upload_url : Created %d signed url(s) for USER: %s MIME_TYPES & FILE_SIZES: %s",
        workflow,
        len(files),
        session["email"],
        file_metadata,
    )

    if all(bool(signed.get("gs_uri", None)) for signed in signed_urls):
        return signed_urls, 200
    else:
        return {"err": "Failure in fetching upload URL(s). Please try again."}, 400


def create_word_doc(response: str, email: str):
    try:

        document = Document()

        document.add_paragraph(response)
        # docx_file = "testing.docx"
        with tempfile.NamedTemporaryFile(delete=True, suffix=".docx") as docx_file:

            document.save(docx_file.name)
            gcs_object_name = upload_helper.handle_file_upload_to_gcs(
                docx_file.name, "SidekickWordDocumentExport.docx", email
            )

        return gcs_object_name
    except Exception as e:
        logger_error.error(f"{workflow} : create_word_doc : {str(e)}")
        raise


def clean_session_file_history(session_document: Session, user_email: str) -> Session:
    """

    CLEANING STRATEGY:
    1. Files existing in GCS: Keep all records
    2. Files NOT existing in GCS:
       - Recent uploads (< 2 hours): REMOVE records (likely cancelled uploads)
       - Older uploads (>= 2 hours): PRESERVE records (expired files for frontend display)

    LOGIC:
    - Recent missing files = User cancelled upload, should be cleaned
    - Older missing files = GCS auto-deleted after 24h, frontend needs to show "expired" status
    - Preserves file history for frontend two-pointer algorithm processing

    Args:
        session_document (Session): The session to clean
        user_email (str): User email for GCS verification

    Returns:
        Session: Session with cleaned file_history
    """
    if not session_document or not session_document.file_history:
        return session_document

    original_count = len(session_document.file_history)
    valid_files = []
    cleaned_files = []
    preserved_expired_files = []

    # Files uploaded within last 2 hours that don't exist = cancelled uploads (REMOVE)
    # Files uploaded >2 hours ago that don't exist =  expired by GCS 24h policy (PRESERVE)
    recent_upload_threshold = datetime.datetime.now(
        datetime.timezone.utc) - datetime.timedelta(hours=2)

    try:
        gcs_client = GCS()

        for file_record in session_document.file_history:
            try:
                # Step 1: Check if file physically exists in GCS storage
                file_exists = gcs_client.verify_file(
                    file_record.gcs_path, user_email)

                if file_exists:
                    # CASE 1: File exists in GCS → Always keep the record
                    valid_files.append(file_record)
                else:
                    # CASE 2: File missing from GCS → Apply time-based cleaning strategy
                    file_upload_time = file_record.created_utc

                    if file_upload_time > recent_upload_threshold:
                        # CASE 2a: Recent upload (< 2h) + missing = Cancelled upload → REMOVE record
                        cleaned_files.append(file_record.gcs_path)
                        logger_info.info(
                            f"{workflow} : clean_session_file_history : Removed recently uploaded non-existent file (cancelled): {file_record.gcs_path} for user: {user_email}"
                        )
                    else:
                        # CASE 2b: Old upload (>= 2h) + missing = Expired file → PRESERVE record for frontend
                        valid_files.append(file_record)
                        preserved_expired_files.append(file_record.gcs_path)
                        logger_info.info(
                            f"{workflow} : clean_session_file_history : Preserved expired file record: {file_record.gcs_path} for user: {user_email}"
                        )

            except Exception as e:
                # CASE 3: GCS verification failed → Apply same time-based strategy as fallback
                file_upload_time = file_record.created_utc

                if file_upload_time > recent_upload_threshold:
                    # CASE 3a: Recent file + verification error = Likely cancelled upload → REMOVE
                    cleaned_files.append(file_record.gcs_path)
                    logger_error.error(
                        f"{workflow} : clean_session_file_history : Removed recent file due to verification error: {file_record.gcs_path}: {str(e)}"
                    )
                else:
                    # CASE 3b: Old file + verification error = Might be GCS access issue → PRESERVE
                    valid_files.append(file_record)
                    preserved_expired_files.append(file_record.gcs_path)
                    logger_error.error(
                        f"{workflow} : clean_session_file_history : Preserved older file despite verification error: {file_record.gcs_path}: {str(e)}"
                    )

        # Apply the cleaned file history to the session
        session_document.file_history = valid_files

        if len(cleaned_files) > 0 or len(preserved_expired_files) > 0:
            logger_info.info(
                f"{workflow} : clean_session_file_history : Session {session_document.id} cleanup completed: "
                f"Removed {len(cleaned_files)} recent cancelled uploads, "
                f"Preserved {len(preserved_expired_files)} expired file records for frontend display. "
                f"Total files: {original_count} -> {len(valid_files)}"
            )

    except Exception as e:
        logger_error.error(
            f"{workflow} : clean_session_file_history : Failed to clean session {session_document.id}: {str(e)}"
        )

    return session_document


def delete_uploaded_file(session_id: str, gcs_path: str, user_email: str) -> Tuple[bool, str]:
    """
    Delete a file from GCS. Session cleanup will be handled automatically by 
    the existing clean_session_file_history mechanism.

    Args:
        session_id (str): The session ID (for validation and logging)
        gcs_path (str): The GCS path of the file to delete
        user_email (str): User email

    Returns:
        tuple[bool, str]: (success, error_message)
    """
    try:
        # Validate session exists
        session_client = FirestoreClient().InstanceClient(user_email)
        session_service = SessionService(persistence_client=session_client)
        session_document = session_service.get_user_session_by_id(
            session_id, user_email)

        if not session_document:
            return False, f"Session {session_id} not found"

        # Check if file exists in session (for validation)
        file_exists_in_session = any(
            file_record.gcs_path == gcs_path
            for file_record in session_document.file_history
        )

        if not file_exists_in_session:
            logger_info.info(
                f"{workflow} : delete_uploaded_file : File {gcs_path} not found in session {session_id}"
            )
            # Still try to delete from GCS in case it exists there

        # Delete from GCS
        gcs_client = GCS()
        gcs_deletion_success = gcs_client.delete_file(gcs_path, user_email)

        if not gcs_deletion_success:
            logger_error.error(
                f"{workflow} : delete_uploaded_file : Failed to delete file from GCS: {gcs_path}"
            )
            return False, "Failed to delete file from storage"

        # Session cleanup will be handled automatically by clean_session_file_history
        # when the session is next accessed, since the file no longer exists in GCS

        logger_info.info(
            f"{workflow} : delete_uploaded_file : Successfully deleted file {gcs_path} for user {user_email}. Session cleanup will occur automatically."
        )
        return True, "File deleted successfully"

    except Exception as e:
        logger_error.error(
            f"{workflow} : delete_uploaded_file : Error deleting file {gcs_path}: {str(e)} : {traceback.format_exc()}"
        )
        return False, f"Error deleting file: {str(e)}"
