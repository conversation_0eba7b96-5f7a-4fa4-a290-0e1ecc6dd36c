:root {
    --toastify-font-family: var(--font-sofia);
}

.Toastify__toast {
    background: transparent;
    padding: 0;
    min-height: auto;
    border-radius: 0;
}

// Ends existing classes

.toastNotification__container {
    height: 100px;
    width: 560px;
    display: flex;
    flex-direction: row;
    align-items: center;
    border-radius: 32px;
    background-color: #003963;
    .toastNotification__container__icon-container {
        width: 80px;
        height: 100px;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
        flex-shrink: 0;
        border-top-left-radius: 32px;
        border-bottom-left-radius: 32px;
        &--positive {
            background-color: #0A884B;
        }
        &--caution {
            background-color: #FFC939;
        }
        &--error {
            background-color: #D30034;
        }
        &--info {
            background-color: #0066B1;
        }
        .toastNotification__container__icon-container__sidekick-icon-container {
            display: flex;
            width: 80px;
            height: 80px;
            padding-bottom: 8px;
            justify-content: center;
            align-items: center;
            gap: 12.5px;
            flex-shrink: 0;
            aspect-ratio: 1/1;
            color: #FFFFFF;
        }
    }
    .toastNotification__container__content-container {
        width: 480px;
        height: 100px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        padding: 0px var(--spacing-spacing-m-3, 24px);
        align-items: center;
        gap: 10px;
        flex: 1 0 0;
        align-self: stretch;
        color: #FFFFFF;
        .toastNotification__container__content-container__message-container {
            width: 390px;
            height: 61px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            flex: 1 0 0;
            .toastNotification__container__content-container__message-container__title-text {
                width: 100%;
                height: 37px;
                font-family: var(--font-sofia);
                font-size: 24px;
                font-style: normal;
                font-weight: 700;
                line-height: normal;
            }
            .toastNotification__container__content-container__message-container__content-text {
                width: 100%;
                height: 24px;
                font-family: var(--font-roboto);
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 23.616px;
            }
        }
        .toastNotification__container__content-container__action-close {
            height: 32px;
            width: 32px;
            aspect-ratio: 1/1;
        }
        .toastNotification__container__content-container__action-close-icon {
            height: 32px;
            width: 32px;
            aspect-ratio: 1/1;
        }
    }
}
