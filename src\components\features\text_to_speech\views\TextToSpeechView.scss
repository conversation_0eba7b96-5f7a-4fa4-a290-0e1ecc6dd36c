.text-to-speech {
  &__container {
    height: 50%;
    // width: 50vw;
    // height: 300px;
    // width: 800px;
    color: var(--text-text-invert);
  }

  &__flex {
    display: flex;
    flex-direction: row;
    margin-top: var(--spacing-spacing-m-2);
  }

  &__select-flex {
    display: flex;
    flex: 1;
    flex-direction: row;
    justify-content: center;
    margin-right: var(--spacing-spacing-m-1);

    &:last-child {
      margin-right: 0;
    }
  }

  &__advanced-section {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    gap: var(--spacing-spacing-m-1);
    margin-top: var(--spacing-spacing-m-1);
  }

  &__advanced-container {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
  }

  &__select-label {
    display: block;
    margin-bottom: var(--spacing-spacing-sm-3);
    margin-top: var(--spacing-spacing-sm-3);
    font-size: 14px;
    text-align: center;
  }

  &__input {
    height: 168px;
    width: 100%;
    border: var(--border-weight-border-weight-s) solid var(--border-color-border-interactive);
    background-color: var(--elements-on-surface-onsurface-brand);
    border-radius: var(--border-radius-border-radius-l);
    padding: var(--spacing-spacing-m-2) var(--spacing-spacing-m-3);
    display: flex;
    flex-direction: column;

    &__textarea {
      flex: 1;
      width: 100%;
      padding-top: var(--spacing-spacing-sm-2);
      padding-bottom: var(--spacing-spacing-sm-2);
      background-color: var(--elements-on-surface-onsurface-brand);
      border: none;
      outline: none;
      resize: none;
      opacity: 0.7;
      overflow-y: auto;

      &::placeholder {
        opacity: 0.7;
      }
    }

    &__counter {
      align-self: flex-end;
      font-size: 12px;
      color: var(--text-text-invert);
      opacity: 0.6;
      margin-top: var(--spacing-spacing-sm-1);
      padding: 2px 4px;
      border-radius: 2px;
      flex-shrink: 0; 
    }
  }
  &__button {
    &-flex {
      display: flex;
      justify-content: center;
      width: 100%;
      gap: 10px;
    }
    &-svg {
      height: 20px;
      width: 20px;
      color: inherit;
      display: inline;
    }
    &__root {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: var(--spacing-spacing-sm-3) var(--spacing-spacing-m-1);
      border-width: var(--border-weight-border-weight-s);
      border-color: var(--border-color-border-interactive);
      border-radius: var(--border-radius-border-radius-full);
      font-weight: 500;
      cursor: pointer;

      transition: all 0.2s ease-out;
      &:hover {
        background-color: var(--elements-on-surface-onsurface-brand);
      }
      &:active {
        background-color: transparent;
      }

      &:disabled {
        color: var(--text-text-disable);
        border-color: var(--text-text-disable);
        background-color: transparent;
        cursor: not-allowed;
      }
    }

    &__icon {
      svg {
        color: inherit;
      }
      display: flex;
      margin-right: var(--spacing-spacing-sm-3);
      flex-shrink: 0;
    }

    &__text {
      font-size: 14px;
    }
  }
}

