import React from 'react';
import { IoCopyOutline, IoCheckmarkOutline } from 'react-icons/io5';
import { HiOutlineThumbUp, HiOutlineThumbDown } from 'react-icons/hi';
import { MessageActionsProps } from '@/components/features/chat/chatTypes';
import clsx from 'clsx';
import PowerPointExportButton from './PowerPointExportButton';
import WordExportButton from './WordExportButton';
import { shouldShowExportUI } from './utils/messageActionUtils';

/**
 * MessageActions - Component that renders the action bar for AI response in the general chat.
 *
 * Provides buttons for copying, liking/disliking, and now exporting.
 */
const MessageActions: React.FC<MessageActionsProps> = ({
  isHovered,
  feedback,
  messageText,
  selectedChatType,
  state,
  handlers,
}) => {
  const { copied } = state;

  const { handleCopy, handleThumbsUp, handleThumbsDown } = handlers;

  const hasFeedback = feedback === 0 || feedback === 1;
  const isLiked = feedback === 1;
  const isDisliked = feedback === 0;
  const iconStyle = 'w-4.5 h-4.5 text-white';
  const roundButtonBase = 'flex items-center justify-center w-8 h-8 rounded-full transition-colors';
  const roundButtonDefaultBg = 'bg-[#002D4F] hover:bg-[#00417A]';
  const roundButtonDisabled = 'opacity-50 cursor-not-allowed';

  const copyButtonStyle = clsx(roundButtonBase, roundButtonDefaultBg, 'text-white');
  const likeButtonStyle = clsx(
    roundButtonBase,
    isLiked ? 'bg-blue-600 text-white' : roundButtonDefaultBg,
    isLiked && roundButtonDisabled,
    isDisliked && 'hidden'
  );
  const dislikeButtonStyle = clsx(
    roundButtonBase,
    isDisliked ? 'bg-red-600 text-white' : roundButtonDefaultBg,
    isDisliked && roundButtonDisabled,
    isLiked && 'hidden'
  );

  return (
    <div
      className={clsx(
        'message-actions',
        'absolute',
        'bottom-1',
        'left-1',
        'flex',
        'flex-wrap',
        'items-center',
        'gap-2',
        'py-1',
        'px-1',
        'transition-opacity',
        'duration-300',
        'ease-in-out',
        hasFeedback ? 'opacity-100' : isHovered ? 'opacity-100' : 'opacity-0 pointer-events-none'
      )}
    >
      <button
        onClick={handleCopy}
        className={clsx('message-actions__button message-actions__button--copy', copyButtonStyle)}
        title={copied ? 'Copied!' : 'Copy message'}
      >
        {copied ? (
          <IoCheckmarkOutline className={clsx('message-actions__icon', iconStyle)} />
        ) : (
          <IoCopyOutline className={clsx('message-actions__icon', iconStyle)} />
        )}
      </button>
      <button
        onClick={handleThumbsUp}
        className={clsx('message-actions__button message-actions__button--like', likeButtonStyle)}
        title="Thumbs up"
        disabled={isLiked}
      >
        <HiOutlineThumbUp className={clsx('message-actions__icon', iconStyle)} />
      </button>
      <button
        onClick={handleThumbsDown}
        className={clsx(
          'message-actions__button message-actions__button--dislike',
          dislikeButtonStyle
        )}
        title="Thumbs down"
        disabled={isDisliked}
      >
        <HiOutlineThumbDown className={clsx('message-actions__icon', iconStyle)} />
      </button>
      {shouldShowExportUI(selectedChatType) && (
        <>
          <PowerPointExportButton messageText={messageText} />
          <WordExportButton messageText={messageText} />
        </>
      )}
    </div>
  );
};

export default MessageActions;
