:root {
  --mermaid-error-border: #ff6b6b;
  --mermaid-error-bg: #ffe0e0;
  --mermaid-error-text: #d63031;
  --mermaid-loading-text: #666;
  --mermaid-modal-overlay: rgba(0, 0, 0, 0.2);
}

/* Base Mermaid Container */
.mermaid-container {
  cursor: pointer;
  overflow: auto;
  min-height: 100px;
  position: relative;
  width: 100%;
  box-sizing: border-box;
  border: 1px solid var(--border-color);
  margin-bottom: 10px;
  border-radius: 4px;
  padding: 10px;
  background-color: var(--white);

  &:hover {
    border-color: var(--link-color);
    transition: border-color 0.2s ease;
  }

  &.mermaid-loading {
    cursor: default;
    opacity: 0.7;
  }

  &.mermaid-error {
    cursor: default;
    border-color: var(--mermaid-error-border);
  }

  &.mermaid-clickable {
    cursor: pointer;
  }

  svg {
    max-width: 100%;
    height: auto;
    display: block;
  }
}

/* Loading Indicator */
.mermaid-loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--mermaid-loading-text);
  font-size: 14px;
  pointer-events: none;
  z-index: 1;
}

/* Error Display */
.mermaid-error-display {
  padding: 16px;
  border: 1px solid var(--mermaid-error-border);
  border-radius: 4px;
  background-color: var(--mermaid-error-bg);
  color: var(--mermaid-error-text);
  font-family: monospace;
  font-size: 12px;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;

  .error-title {
    font-weight: bold;
  }

  .error-message {
    font-size: 11px;
    opacity: 0.8;
    margin-top: 4px;
  }
}

/* Modal Overlay */
.mermaid-modal-overlay {
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--mermaid-modal-overlay);
}

/* Modal Container */
.mermaid-modal-container {
  position: relative;
  border-radius: 12px;
  width: 80vw;
  height: 75vh;
  max-width: 1000px;
  max-height: 700px;
  min-width: 600px;
  min-height: 400px;
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.18);
  z-index: 10;
  overflow: hidden;
  animation: fadeIn 0.2s ease-out;
}

/* Modal Close Button */
.mermaid-modal-close {
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 20;
  padding: 8px;
  border-radius: 50%;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    opacity: 0.8;
  }

  .close-icon {
    width: 24px;
    height: 24px;
  }
}

/* Modal Content */
.mermaid-modal-content {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Modal Title */
.mermaid-modal-container .mermaid-modal-title {
  margin: 0;
  padding: 16px 24px 12px 24px;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  text-align: center;
  border-bottom: 1px solid var(--border-color);
  flex-shrink: 0;
}

/* Modal SVG Container */
.mermaid-modal-svg {
  flex: 1;
  position: relative;
  overflow: hidden;
  padding: 8px;

  /* Specific SVG styling for modal */
  svg {
    width: 100%;
    height: 100%;
    max-width: none;
    max-height: none;
    object-fit: contain;
    display: block;
  }
}

.mermaid-transform-wrapper {
  width: 100%;
  height: 100%;
  border: 1px solid var(--border-color, #e0e0e0);
  border-radius: 8px;
  background-color: var(--white, #fff);
  position: relative;
  overflow: hidden;
}

.mermaid-transform-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  > div {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.mermaid-modal-svg {
  .react-transform-wrapper,
  .react-transform-component,
  .react-transform-element {
    width: 100%;
    height: 100%;
  }

  .react-transform-component,
  .react-transform-element {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* Zoom Controls */
.mermaid-zoom-controls {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  flex-direction: row;
  gap: 6px;
  z-index: 20;
  background: rgba(255, 255, 255, 0.95);
  padding: 4px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(4px);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Zoom Control Buttons */
.mermaid-zoom-btn {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.95);
  color: #2c3e50;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 1);
    color: #1a252f;
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  }

  .zoom-icon {
    font-size: 14px;
    font-weight: 600;
  }
}

[data-theme="dark"] {
  .mermaid-container {
    background-color: var(--code-content-bg);
    border-color: var(--border-color);
  }

  .mermaid-zoom-controls {
    background: rgba(40, 40, 40, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .mermaid-transform-wrapper {
    background-color: var(--background-input, #2d2d2d);
    border-color: var(--border-color, #444);
  }

  .mermaid-zoom-btn {
    background: rgba(60, 60, 60, 0.95);
    color: #ffffff;
    border-color: rgba(255, 255, 255, 0.2);

    &:hover {
      background: rgba(80, 80, 80, 1);
      color: #ffffff;
    }
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}