import React from 'react';
import { IoClose } from 'react-icons/io5';
import { IoMdInformationCircleOutline } from 'react-icons/io';
import { useThemeStyles } from '@hooks/useThemeStyles';

import './PromptLibraryBaseModal.scss';

interface PromptLibraryBaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  title?: string;
  infoText?: string;
}

const PromptLibraryBaseModal: React.FC<PromptLibraryBaseModalProps> = ({ isOpen, onClose, title, infoText, children }) => {
  const { classes } = useThemeStyles();

  if (!isOpen) return null;

  return (
    // Overlay
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-[#000E1A]/90 transition-opacity duration-300 ease-out"
      onClick={onClose}
    >
      {/* Modal Container */}
      <div
        className={`promptLibraryTemplateCreateEditModalContainer ${classes.text}`}
        onClick={event => event.stopPropagation()}
      >
        {/* Modal body Container */}
        <div className="promptLibraryTemplateCreateEditModalContainer__body-container">
          {/* Title Container */}
          <div className="promptLibraryTemplateCreateEditModalContainer__body-container__title-container">
            <h2 className="promptLibraryTemplateCreateEditModalContainer__body-container__title-container__title-text">
              {title}
            </h2>
            <button
              onClick={onClose}
              className={`
                ${classes.text}
                promptLibraryTemplateCreateEditModalContainer__body-container__title-container__action-close-button
              `}
              aria-label="Close modal"
              tabIndex={0}
            >
              <IoClose className="promptLibraryTemplateCreateEditModalContainer__body-contgainer__title-container__action-close-button__icon" />
            </button>
          </div>
          {/* Children */}
          {children}
        </div>
        {/* Modal Info Container */}
        <div className="promptLibraryTemplateCreateEditModalContainer__info-container">
          <div className="promptLibraryTemplateCreateEditModalContainer__info-container__content-container">
            <IoMdInformationCircleOutline className="promptLibraryTemplateCreateEditModalContainer__info-container__content-container__icon" />
            <p className="promptLibraryTemplateCreateEditModalContainer__info-container__content-container__text">
              {infoText}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PromptLibraryBaseModal;
