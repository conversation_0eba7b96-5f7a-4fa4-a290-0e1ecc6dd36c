/**
 * A reducer is used here to handle the state transitions involved in the file upload
 * lifecycle (adding, updating status, deleting, etc.) in a predictable and immutable way.
 * Using a Map for the state for performance optimization, allowing for O(1) lookups,
 * insertions, and deletions by file ID.
 */

import { ValidatedFile } from '@/types/fileUpload';
import { UploadResult } from '../utils/uploadResultHandlers';
import { applyUploadResults } from '../utils/uploadResultHandlers';

export type FilesState = {
  files: Map<string, ValidatedFile>;
};

export type FilesAction =
  | { type: 'ADD_FILES'; payload: ValidatedFile[] }
  | { type: 'UPDATE_FILE'; payload: { fileId: string; updates: Partial<ValidatedFile> } }
  | {
      type: 'UPDATE_MULTIPLE_FILES';
      payload: { fileIds: string[]; updates: Partial<ValidatedFile> };
    }
  | {
      type: 'APPLY_UPLOAD_RESULTS';
      payload: {
        uploadResults: PromiseSettledResult<UploadResult>[];
        validFiles: ValidatedFile[];
      };
    }
  | { type: 'REMOVE_FILE'; payload: { fileId: string } }
  | { type: 'REMOVE_CANCELLED' }
  | { type: 'CLEAR_ALL' }
  | { type: 'CLEAR_UPLOADED' };

/**
 * The reducer function that handles all state transitions for the file upload list.
 * It takes the current state and an action, and returns a new, updated state object.
 * It is a pure function and ensures all updates are immutable by creating a new Map.
 */
export const filesReducer = (state: FilesState, action: FilesAction): FilesState => {
  const newFiles = new Map(state.files);

  switch (action.type) {
    case 'ADD_FILES':
      action.payload.forEach(file => newFiles.set(file.id, file));
      return { files: newFiles };

    case 'UPDATE_FILE': {
      const { fileId, updates } = action.payload;
      const file = newFiles.get(fileId);
      if (file) {
        newFiles.set(fileId, { ...file, ...updates });
      }
      return { files: newFiles };
    }

    case 'UPDATE_MULTIPLE_FILES': {
      const { fileIds, updates } = action.payload;
      fileIds.forEach(fileId => {
        const file = newFiles.get(fileId);
        if (file) {
          // Create a new object for the updated file.
          newFiles.set(fileId, { ...file, ...updates });
        }
      });
      return { files: newFiles };
    }

    case 'APPLY_UPLOAD_RESULTS': {
      const { uploadResults, validFiles } = action.payload;
      const newFileMap = new Map(state.files);
      const updatedFiles = applyUploadResults(
        Array.from(newFileMap.values()),
        uploadResults,
        validFiles
      );
      updatedFiles.forEach(file => newFileMap.set(file.id, file));
      return { files: newFileMap };
    }

    case 'REMOVE_FILE':
      newFiles.delete(action.payload.fileId);
      return { files: newFiles };

    case 'REMOVE_CANCELLED': {
      // Filters out files with the 'cancelled' status.
      const filteredFiles = new Map<string, ValidatedFile>();
      for (const [id, file] of newFiles.entries()) {
        if (file.uploadStatus !== 'cancelled') {
          filteredFiles.set(id, file);
        }
      }
      return { files: filteredFiles };
    }

    case 'CLEAR_ALL':
      // Resets the state to an empty Map.
      return { files: new Map() };

    case 'CLEAR_UPLOADED': {
      // Filters out files that have been successfully uploaded.
      const filteredFiles = new Map<string, ValidatedFile>();
      for (const [id, file] of newFiles.entries()) {
        if (file.uploadStatus !== 'success') {
          filteredFiles.set(id, file);
        }
      }
      return { files: filteredFiles };
    }

    default:
      return state;
  }
};
