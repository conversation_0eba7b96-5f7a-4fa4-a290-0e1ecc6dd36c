import React from 'react';
import { toast, ToastContentProps } from 'react-toastify';

import { IoClose } from 'react-icons/io5';

import './ToastNotification.scss';

export enum ToastType {
  Positive = 'positive',
  Caution = 'caution',
  Error = 'error',
  Info = 'info',
}

// A record mapping ToastType and appropriate icon
const defaultIcons: Record<ToastType, React.ReactNode> = {
  [ToastType.Positive]: (
    <svg xmlns="http://www.w3.org/2000/svg" width="44" height="46" viewBox="0 0 44 46" fill="none">
      <path d="M5.02734 16.6426C2.25104 16.6426 0 18.8936 0 21.6699V40.9482C0 43.7245 2.25104 45.9756 5.02734 45.9756H38.9727C41.749 45.9756 44 43.7245 44 40.9482V21.6699C44 18.8936 41.749 16.6426 38.9727 16.6426H5.02734ZM30.8008 34.6094C30.8008 36.9432 29.874 39.1817 28.2236 40.832C26.5733 42.4823 24.3348 43.4092 22.001 43.4092C19.6671 43.4092 17.4286 42.4823 15.7783 40.832C14.128 39.1817 13.2012 36.9432 13.2012 34.6094H30.8008ZM34.0996 20.3086C37.1371 20.3086 39.5995 22.7712 39.5996 25.8086C39.5996 28.8461 37.1372 31.3086 34.0996 31.3086C31.0622 31.3084 28.5996 28.846 28.5996 25.8086C28.5998 22.7713 31.0623 20.3088 34.0996 20.3086ZM9.89844 20.3086C12.9359 20.3086 15.3983 22.7712 15.3984 25.8086C15.3984 28.8461 12.936 31.3086 9.89844 31.3086C6.8611 31.3083 4.39844 28.8459 4.39844 25.8086C4.39858 22.7714 6.86118 20.3089 9.89844 20.3086Z" fill="white"/>
      <ellipse cx="21.3687" cy="4.77262" rx="4.74831" ry="4.74821" fill="white"/>
      <path d="M21.3685 19.0199L18.9944 13.0847H23.7427L21.3685 7.14941" stroke="white" stroke-width="2.5"/>
    </svg>
  ),
  [ToastType.Caution]: (
    <svg xmlns="http://www.w3.org/2000/svg" width="44" height="46" viewBox="0 0 44 46" fill="none">
      <path d="M5.02734 16.6426C2.25104 16.6426 0 18.8936 0 21.6699V40.9492C0.000110626 43.7254 2.25111 45.9756 5.02734 45.9756H38.9727C41.7489 45.9756 43.9999 43.7254 44 40.9492V21.6699C44 18.8936 41.749 16.6426 38.9727 16.6426H5.02734ZM22 34.4863C24.3339 34.4863 26.5723 35.4141 28.2227 37.0645C29.8729 38.7148 30.7998 40.9533 30.7998 43.2871H13.2002C13.2002 40.9533 14.1271 38.7148 15.7773 37.0645C17.4277 35.4141 19.6661 34.4863 22 34.4863ZM33.4727 19.1709C37.2791 19.1711 40.3652 22.2569 40.3652 26.0635C40.365 29.8698 37.279 32.9558 33.4727 32.9561C29.6661 32.9561 26.5803 29.87 26.5801 26.0635C26.5801 22.2568 29.666 19.1709 33.4727 19.1709ZM10.0391 18.7773C13.8457 18.7773 16.9316 21.8633 16.9316 25.6699C16.9315 29.4765 13.8456 32.5625 10.0391 32.5625C6.23259 32.5624 3.14664 29.4764 3.14648 25.6699C3.14648 21.8633 6.23249 18.7774 10.0391 18.7773Z" fill="white"/>
      <ellipse cx="21.3687" cy="4.77273" rx="4.74831" ry="4.74832" fill="white"/>
      <path d="M21.3685 19.0212L18.9944 13.0858H23.7427L21.3685 7.15039" stroke="white" stroke-width="2.5"/>
    </svg>
  ),
  [ToastType.Error]: (
    <svg xmlns="http://www.w3.org/2000/svg" width="44" height="46" viewBox="0 0 44 46" fill="none">
      <path d="M5.02734 16.6426C2.25104 16.6426 0 18.8936 0 21.6699V40.9482C0 43.7245 2.25104 45.9756 5.02734 45.9756H38.9727C41.749 45.9756 44 43.7245 44 40.9482V21.6699C44 18.8936 41.749 16.6426 38.9727 16.6426H5.02734ZM22 34.4873C24.3339 34.4873 26.5723 35.4142 28.2227 37.0645C29.8729 38.7147 30.7998 40.9533 30.7998 43.2871H13.2002C13.2002 40.9533 14.1271 38.7147 15.7773 37.0645C17.4277 35.4142 19.6661 34.4873 22 34.4873ZM34.251 23.6123L37.9277 19.9365L40.3779 22.3867L36.7012 26.0625L40.3779 29.7393L37.9268 32.1895L34.251 28.5137L30.5752 32.1895L28.125 29.7393L31.8008 26.0635L28.125 22.3877L30.5752 19.9365L34.251 23.6123ZM15.8721 22.3867L12.1953 26.0635L15.8711 29.7393L13.4209 32.1895L9.74512 28.5137L6.07031 32.1895L3.61914 29.7383L7.29395 26.0625L3.61816 22.3877L6.06836 19.9375L9.74414 23.6133L13.4219 19.9365L15.8721 22.3867Z" fill="white"/>
      <ellipse cx="21.3687" cy="4.77262" rx="4.74831" ry="4.74821" fill="white"/>
      <path d="M21.3685 19.0199L18.9944 13.0847H23.7427L21.3685 7.14941" stroke="white" stroke-width="2.5"/>
    </svg>
  ),
  [ToastType.Info]: (
    <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 64 64" fill="none">
      <path d="M32 5.33301C17.28 5.33301 5.33337 17.2797 5.33337 31.9997C5.33337 46.7197 17.28 58.6663 32 58.6663C46.72 58.6663 58.6667 46.7197 58.6667 31.9997C58.6667 17.2797 46.72 5.33301 32 5.33301ZM34.6667 45.333H29.3334V29.333H34.6667V45.333ZM34.6667 23.9997H29.3334V18.6663H34.6667V23.9997Z" fill="#E3FDF0"/>
    </svg>
  ),
};

export interface ToastNotificationProps {
  type: ToastType;
  title: string;
  description: string;
  icon?: React.ReactNode;
  closeToast?: () => void;
  toastProps?: ToastContentProps;
}

const ToastNotification: React.FC<ToastNotificationProps> = ({
  type,
  title,
  description,
  icon,
  closeToast,
}) => {
  const toastIcon = icon || defaultIcons[type];

  return (
    <div className="toastNotification__container">
      {/* toast-icon-container */}
      <div
        className={`toastNotification__container__icon-container toastNotification__container__icon-container--${type}`}
      >
        <div className="toastNotification__container__icon-container__sidekick-icon-container">
          {toastIcon}
        </div>
      </div>
      {/* toast-content-container */}
      <div className="toastNotification__container__content-container">
        <div className="toastNotification__container__content-container__message-container">
          <h4 className="toastNotification__container__content-container__message-container__title-text">
            {title}
          </h4>
          <p className="toastNotification__container__content-container__message-container__content-text">
            {description}
          </p>
        </div>
        <div
          className="toastNotification__container__content-container__action-close"
          onClick={closeToast}
        >
          <IoClose className="toastNotification__container__content-container__action-close-icon" />
        </div>
      </div>
    </div>
  );
};

// Helper functions to show different types of toasts
export const showToast = {
  positive: (title: string, description: string, options = {}) => {
    toast(<ToastNotification type={ToastType.Positive} title={title} description={description} />, {
      ...options,
      className: 'Toastify__toast-container Toastify__toast-container--bottom-center',
    });
  },
  caution: (title: string, description: string, options = {}) => {
    toast(<ToastNotification type={ToastType.Caution} title={title} description={description} />, {
      ...options,
    });
  },
  error: (title: string, description: string, options = {}) => {
    toast(<ToastNotification type={ToastType.Error} title={title} description={description} />, {
      ...options,
    });
  },
  info: (title: string, description: string, options = {}) => {
    toast(<ToastNotification type={ToastType.Info} title={title} description={description} />, {
      ...options,
    });
  },
};
