import type { ChangeEvent } from 'react';
import { RetrievalWorkbookChunkSize } from '../workbookTypes';

import { useState } from 'react';

import { Radio } from '@base-ui-components/react/radio';
import { RadioGroup } from '@base-ui-components/react/radio-group';
import { toast } from 'react-toastify';

import { FaPlus } from 'react-icons/fa';
import { RxCross2 } from 'react-icons/rx';
import { MdOutlineFilePresent } from 'react-icons/md';
import { TbNotebook } from 'react-icons/tb';
import { RiInformationFill } from 'react-icons/ri';

import { WORKBOOK_CONSTANTS } from '@/config/constants';
import SimpleAccordion from '@/components/common/SimpleAccordion';
import { withUCDAttestation } from '@/utils/ucdFileUploadWrapper';
import { useAppSelector } from '@/store/hooks';
import { selectCurrentUser } from '@/store/slices/authSlice';
import useThemeStyles from '@hooks/useThemeStyles';

import './CreateWorkbookModalContent.scss';

interface CreateWorkbookModalProps {
  isGlobal: boolean;
  onSubmit: (
    workbookName: string,
    chunkSize: RetrievalWorkbookChunkSize,
    files: File[]
  ) => Promise<void>;
  onClose: () => void;
}

interface CreateWorkbookFileProps {
  file: File;
  onDelete: () => void;
}

interface CreateWorkbookChunkSizeProps {
  selectedChunkSize: RetrievalWorkbookChunkSize;
  onChunkSizeSelect: (chunkSize: RetrievalWorkbookChunkSize) => void;
}

const CreateWorkbookModalFileButton = (props: CreateWorkbookFileProps) => {
  const { file, onDelete } = props;
  return (
    <div className="add-files-file border-[#0066B1] rounded border-2">
      <div className="add-files-file-icon">
        <MdOutlineFilePresent size={18} />
      </div>
      <div className="add-files-file-name">{file.name}</div>
      <div className="add-files-file-delete" onClick={onDelete}>
        <button id={`${file.name}-button`} className="hover:cursor-pointer flex items-center">
          <RxCross2 size={16} />
        </button>
      </div>
    </div>
  );
};

const ChunkSizeOptionsRadioGroup = (props: CreateWorkbookChunkSizeProps) => {
  const { onChunkSizeSelect } = props;
  const onRadioGroupChange = (chunkSize: RetrievalWorkbookChunkSize) => {
    if (onChunkSizeSelect) {
      onChunkSizeSelect(chunkSize);
    }
  };

  return (
    <RadioGroup
      aria-labelledby="create-workbook__chunksize-radio-group"
      defaultValue={RetrievalWorkbookChunkSize.Large}
      className={`chunksize-radio-group__main`}
      onValueChange={value => onRadioGroupChange(value as RetrievalWorkbookChunkSize)}
    >
      <div className={`chunksize-radio-group__title`} id="create-workbook__chunksize-radio-group">
        Document chunk size
      </div>

      <label className={`chunksize-radio-group__label`}>
        <Radio.Root
          value={RetrievalWorkbookChunkSize.Small}
          className={`chunksize-radio-group__item`}
        >
          <Radio.Indicator className={`chunksize-radio-group__indicator`} />
        </Radio.Root>
        Small
      </label>
      <label className={`chunksize-radio-group__label`}>
        <Radio.Root
          value={RetrievalWorkbookChunkSize.Medium}
          className={`chunksize-radio-group__item`}
        >
          <Radio.Indicator className={`chunksize-radio-group__indicator`} />
        </Radio.Root>
        Medium
      </label>
      <label className={`chunksize-radio-group__label`}>
        <Radio.Root
          value={RetrievalWorkbookChunkSize.Large}
          className={`chunksize-radio-group__item`}
        >
          <Radio.Indicator className={`chunksize-radio-group__indicator`} />
        </Radio.Root>
        Large (recommended)
      </label>
    </RadioGroup>
  );
};

const CreateWorkbookModalContent = (props: CreateWorkbookModalProps) => {
  const { classes, colors: themeColors } = useThemeStyles();
  const currentUser = useAppSelector(selectCurrentUser);

  const { isGlobal, onSubmit, onClose } = props;
  const [workbookName, setWorkbookName] = useState('');
  const [workbookChunkSize, setWorkbookChunkSize] = useState(RetrievalWorkbookChunkSize.Large);
  const [filesToUpload, setFilesToUpload] = useState<File[]>([]);
  const createIsDisabled = workbookName.length === 0;

  const onFormSubmit = async () => {
    if (!createIsDisabled) {
      await onSubmit(workbookName, workbookChunkSize, filesToUpload);
    }
  };

  const onWorkbookNameChange = async (event: ChangeEvent<HTMLInputElement>) => {
    setWorkbookName(event.currentTarget.value);
  };

  const onWorkbookChunkSizeChange = async (chunkSize: RetrievalWorkbookChunkSize) => {
    setWorkbookChunkSize(chunkSize);
  };

  const onWorkbookFilesChanged = async (event: ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.currentTarget.files ?? []);

    const processFiles = (validatedFiles: File[]) => {
      const filteredFiles = [];
      for (const file of validatedFiles) {
        switch (file.type) {
          case 'text/plain':
            if (file.size > WORKBOOK_CONSTANTS.fileSizeLimits['text/plain']) {
              toast.error(`${file.name} exceeds the 10 MB size limit for text files`);
            } else {
              filteredFiles.push(file);
            }
            break;
          case 'application/pdf':
            if (file.size > WORKBOOK_CONSTANTS.fileSizeLimits['application/pdf']) {
              toast.error(`${file.name} exceeds the 200 MB size limit for PDFs`);
            } else {
              filteredFiles.push(file);
            }
            break;
          default:
            toast.error('Unsupported file type');
        }
      }
      setFilesToUpload([...filteredFiles, ...filesToUpload]);
    };

    await withUCDAttestation(files, 'workbook', currentUser, processFiles, files);
  };

  const renderFileButtons = (files: File[]) => {
    return files.map(file => (
      <CreateWorkbookModalFileButton
        key={file.name}
        file={file}
        onDelete={() => setFilesToUpload(filesToUpload.filter(f => f.name !== file.name))}
      />
    ));
  };

  return (
    <div
      className={`create-workbook__container h-full text-[${themeColors.text}] font-roboto break-all ${classes.text}`}
    >
      <div className={`create-workbook__main`}>
        <h1 className="text-3xl mb-5">New {isGlobal ? 'Public' : ''} Workbook</h1>
        <div className="bg-white border-2 border-[#0066B1] w-full p-2 rounded">
          <input
            className="text-black w-full outline-none"
            value={workbookName}
            placeholder="My New Workbook"
            onChange={onWorkbookNameChange}
          />
        </div>
        <div className="add-files-main">
          <div className="add-files-button">
            <label
              htmlFor="createWorkbookFiles"
              className={`add-files-label items-center point gap-2 px-4 py-2 rounded border-[#0066B1] border-2 ${themeColors.text} hover:bg-[#0066B1] hover:cursor-pointer`}
            >
              <div className="add-files-label-icon">
                <FaPlus size={10} />
              </div>
              <div className="add-files-label-text font-roboto">Add files</div>
            </label>
            <input
              id="createWorkbookFiles"
              type="file"
              multiple
              hidden
              onChange={onWorkbookFilesChanged}
              accept=".txt, .pdf"
            />
          </div>
          <div
            className="add-files-file-list
              dark:[&::-webkit-scrollbar-track]:bg-[#002D4F]
              dark:[&::-webkit-scrollbar-thumb]:bg-[#0066B1]"
          >
            {renderFileButtons(filesToUpload)}
          </div>
        </div>

        <SimpleAccordion title="Advanced options">
          <div
            className="create-workbook__chunkSize"
            style={{ backgroundColor: 'transparent', color: 'white' }}
          >
            <ChunkSizeOptionsRadioGroup
              selectedChunkSize={RetrievalWorkbookChunkSize.Large}
              onChunkSizeSelect={onWorkbookChunkSizeChange}
            />
          </div>
        </SimpleAccordion>

        <div className="create-workbook__actions">
          <div className="create-workbook__actions-cancel">
            <button
              className={`hover:underline items-center point gap-2 px-4 py-2 rounded ${themeColors.text} hover:cursor-pointer font-roboto`}
              onClick={onClose}
            >
              Cancel
            </button>
          </div>
          <button
            className={`create-workbook__actions-create ${createIsDisabled ? 'disabled' : ''}`}
            disabled={createIsDisabled}
            onClick={onFormSubmit}
          >
            <div className={`create-workbook__actions-create-icon ${themeColors.text} font-roboto`}>
              <TbNotebook size={24} />
            </div>
            <div className={`create-workbook__actions-create-label ${themeColors.text}`}>
              Create Workbook
            </div>
          </button>
        </div>
      </div>

      <div className={`create-workbook__footer ${themeColors.text}`}>
        <RiInformationFill
          className="create-workbook__footer-icon"
          fill={`${themeColors.text}`}
          size={20}
        />
        <div className="create-workbook__footer-text">
          Text files cannot exceed 10 MB in size, PDF files cannot exceed 200 MB in size
        </div>
      </div>
    </div>
  );
};

export default CreateWorkbookModalContent;
