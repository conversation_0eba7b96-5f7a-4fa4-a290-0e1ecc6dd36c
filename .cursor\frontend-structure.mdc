---
description: 
globs: 
alwaysApply: false
---
# Frontend Project Structure (sidekick-frontend)

This document outlines the structure of the `sidekick-frontend` React/TypeScript project.

## Key Files

*   **Entry Point**: The application starts in [`sidekick-frontend/src/main.tsx`](mdc:sidekick-frontend/src/main.tsx).
*   **Main App Component**: The root React component is [`sidekick-frontend/src/App.tsx`](mdc:sidekick-frontend/src/App.tsx).
*   **HTML Template**: The main HTML file is [`sidekick-frontend/index.html`](mdc:sidekick-frontend/index.html).
*   **Routing**: Application routes are defined in [`sidekick-frontend/src/routes.tsx`](mdc:sidekick-frontend/src/routes.tsx).
*   **Package Management**: Dependencies and scripts are managed in [`sidekick-frontend/package.json`](mdc:sidekick-frontend/package.json).
*   **Build Configuration**: The Vite build setup is configured in [`sidekick-frontend/vite.config.ts`](mdc:sidekick-frontend/vite.config.ts).
*   **TypeScript Configuration**: Base TS config is in [`sidekick-frontend/tsconfig.json`](mdc:sidekick-frontend/tsconfig.json). App-specific TS config is in [`sidekick-frontend/tsconfig.app.json`](mdc:sidekick-frontend/tsconfig.app.json).
*   **Styling Configuration**: Tailwind CSS is configured in [`sidekick-frontend/tailwind.config.js`](mdc:sidekick-frontend/tailwind.config.js). Global styles are in [`sidekick-frontend/src/index.css`](mdc:sidekick-frontend/src/index.css).
*   **Linting**: ESLint configuration can be found in [`sidekick-frontend/eslint.config.js`](mdc:sidekick-frontend/eslint.config.js) or [`sidekick-frontend/.eslintrc.json`](mdc:sidekick-frontend/.eslintrc.json).

## Source Code Directory (`src/`)

The [`sidekick-frontend/src/`](mdc:sidekick-frontend/src) directory contains the main application code:

*   [`sidekick-frontend/src/components/`](mdc:sidekick-frontend/src/components): Reusable UI components.
*   [`sidekick-frontend/src/pages/`](mdc:sidekick-frontend/src/pages): Components representing distinct pages/views.
*   [`sidekick-frontend/src/layouts/`](mdc:sidekick-frontend/src/layouts): Components defining page layouts.
*   [`sidekick-frontend/src/store/`](mdc:sidekick-frontend/src/store): State management logic.
*   [`sidekick-frontend/src/hooks/`](mdc:sidekick-frontend/src/hooks): Custom React Hooks.
*   [`sidekick-frontend/src/api/`](mdc:sidekick-frontend/src/api): Code related to interacting with backend APIs.
*   [`sidekick-frontend/src/contexts/`](mdc:sidekick-frontend/src/contexts): React Context providers and consumers.
*   [`sidekick-frontend/src/utils/`](mdc:sidekick-frontend/src/utils): Utility functions.
*   [`sidekick-frontend/src/types/`](mdc:sidekick-frontend/src/types): TypeScript type definitions.
*   [`sidekick-frontend/src/config/`](mdc:sidekick-frontend/src/config): Application configuration files.
*   [`sidekick-frontend/src/assets/`](mdc:sidekick-frontend/src/assets): Static assets like images and fonts.

