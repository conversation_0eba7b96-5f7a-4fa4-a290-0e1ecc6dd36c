export interface ParsedApiError {
  message: string;
  statusCode?: number;
  originalError?: any;
}

export const parseApiError = (error: any): ParsedApiError => {
  if (error && typeof error.response_success === 'boolean' && typeof error.response === 'string') {
    return { message: error.response, originalError: error };
  }

  if (error && error.response && error.response.data && typeof error.response.data.message === 'string') {
    return { message: error.response.data.message, statusCode: error.response.status, originalError: error };
  }

  if (error && error.msg && typeof error.msg === 'string') {
    return { message: error.msg, originalError: error };
  }

  if (error instanceof Error) {
    return { message: error.message, originalError: error };
  }

  if (typeof error === 'string') {
    return { message: error, originalError: error };
  }

  return { message: 'An unexpected error occurred.', originalError: error };
};
