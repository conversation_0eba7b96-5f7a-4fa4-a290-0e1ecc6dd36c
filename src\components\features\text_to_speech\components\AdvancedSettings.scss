.advanced-settings {
  &__root {
    display: flex;
    width: 384px;
    max-width: calc(100vw - 128px);
    flex-direction: column;
    justify-content: center;
  }

  &__item {
    width: 75%;
  }

  &__trigger {
    display: flex;
    justify-content: space-between;
    gap: var(--spacing-spacing-m-2);
    padding-top: var(--spacing-spacing-sm-3);
    padding-bottom: var(--spacing-spacing-sm-3);
    text-align: left;
    font-weight: 500;
    border-width: var(--border-weight-border-weight-s);
    border-color: var(--border-color-border-interactive);
    border-radius: var(--border-radius-border-radius-full);
    cursor: pointer;

    transition: all 0.2s ease-out;
    &:hover {
      background-color: var(--elements-on-surface-onsurface-brand);
    }
    &:active {
      background-color: transparent;
    }
  }

  &__icon {
    svg {
      color: inherit;
    }
    margin-left: var(--spacing-spacing-sm-3);
    margin-right: var(--spacing-spacing-sm-3);
    flex-shrink: 0;
    transition: all 0.3s ease-out;
  }

  [data-panel-open] &__icon {
    transform: scale(1.1) rotate(90deg);
  }

  &__trigger-text {
    margin-right: var(--spacing-spacing-m-2);
  }

  &__panel {
    height: var(--accordion-panel-height);
    overflow: hidden;
    font-size: var(--spacing-spacing-m-2);
    transition: height 0.3s ease-out;

    &[data-ending-style],
    &[data-starting-style] {
      height: 0;
    }
  }

  &__panel-content {
    padding-bottom: var(--spacing-spacing-m-1);
  }

  &__select-label {
    display: block;
    margin-bottom: var(--spacing-spacing-sm-3);
    font-size: 14px;
  }
}
