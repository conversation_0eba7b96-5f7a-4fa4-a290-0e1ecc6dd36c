.ChatType__radio-group-container {
  display: flex;
  justify-content: left;
  align-items: center;
  gap: 10px;
  .ChatType__radio-group-header {
    width: 25%;
    font-family: var(--font-sofia-pro);
    font-size: 14px;
    font-style: normal;
    line-height: 160%;
    letter-spacing: 0.112px;
  }
  .ChatType__radio-group {
    display: flex;
    height: 36px;
    width: 75%;
    align-items: center;
    gap: var(--spacing-spacing-m-2, 16px);
    align-self: stretch;
  
    .ChatType__radio-item {
        display: flex;
        height: 44px;
        padding: var(--spacing-spacing-sm-3, 6px) var(--spacing-spacing-sm-2, 4px);
        align-items: center;
        gap: var(--spacing-spacing-sm-3, 8px);
  
        font-family: var(--font-roboto);
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 160%;
        letter-spacing: 0.112px;
        cursor: pointer;
        .ChatType__radio {
            box-sizing: border-box;
            display: flex;
            width: 1rem;
            height: 1rem;
            align-items: center;
            justify-content: center;
            border-radius: 100%;
            outline: 0;
            padding: 0;
            margin: 0;
            border: none;
          
            &[data-unchecked] {
              border: 2px solid lightgray;
              background-color: transparent;
            }
          
            &[data-checked] {
                border: 2px solid white;
                background-color: var(--elements-surface-surface-dark);
            }
          
            &:focus-visible {
              outline: 2px solid var(--color-blue);
              outline-offset: 2px;
            }
            .ChatType__radio-indicator {
                display: flex;
                align-items: center;
                justify-content: center;
              
                &[data-unchecked] {
                  display: none;
                }
              
                &::before {
                  content: '';
                  border-radius: 100%;
                  width: 0.5rem;
                  height: 0.5rem;
                  background-color: white;
                }
            }
        }
    }
  }
  
  .ChatType__radio-group[data-disabled] {
    .ChatType__radio-item {
        cursor: not-allowed;
    }
  }
}
