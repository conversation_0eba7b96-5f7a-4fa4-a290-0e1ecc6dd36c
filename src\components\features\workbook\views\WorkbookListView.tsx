import React, { useState, useMemo, useEffect } from 'react';

import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { 
  fetchGlobalWorkbooks, 
  fetchMyWorkbooks, 
  createMyWorkbook,
  createPublicWorkbook,
  selectAllGlobalWorkbooks, 
  selectAllUserWorkbooks 
} from '@/store/slices/workbookSlice';
import {
  fetchCurrentUser,
  selectCurrentUserRoles
} from '@/store/slices/authSlice';

import { WorkbookCard } from '../components/cards';
import WorkbookContainer from '../layouts/WorkbookContainer';
import { AddWorkbookCard } from '../components/cards';
import Modal from '@/components/common/Modal';
import CreateWorkbookModalContent from './CreateWorkbookModalContent'
import { RetrievalWorkbookChunkSize, RetrievalWorkbookCreate, WorkbookListViewProps } from '../workbookTypes';
import { useNavigate } from 'react-router-dom';

const WorkbookListView: React.FC<WorkbookListViewProps> = (props: WorkbookListViewProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const { isGlobal } = props;
  const selectAllWorkbooks = isGlobal ? selectAllGlobalWorkbooks : selectAllUserWorkbooks;
  const workbooks = useAppSelector(selectAllWorkbooks);
  const currentUserRoles = useAppSelector(selectCurrentUserRoles);

  useEffect(() => {
    const fetchWorkbooks = isGlobal ? fetchGlobalWorkbooks : fetchMyWorkbooks;
    dispatch(fetchWorkbooks()).unwrap();
    dispatch(fetchCurrentUser()).unwrap();
  }, [])


  const handleAddClick = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleCreateWorkbook = async (workbookName: string, chunkSize: RetrievalWorkbookChunkSize, files: File[]) => {
    const toCreate: RetrievalWorkbookCreate = {
      name: workbookName,
      chunkSize: chunkSize
    }

    const createWorkbook = (isGlobal && currentUserRoles.some(r => r === "Sidekick-CoreTeam")) ? createPublicWorkbook : createMyWorkbook;
    const { workbook } = await dispatch(createWorkbook(toCreate)).unwrap()
    if (workbook && workbook.id) {
      navigate(`${workbook.id}`, { state: {
        filesToUpload: files
      }})
    }
  }

  const workbookCards = useMemo(
    () =>
      Object.entries(workbooks).map(([workbookId, workbook]) => (
        <WorkbookCard
          key={workbook.id}
          workbook={workbook}
          navigateTo={`${workbookId}`}
          isGlobal={isGlobal}
        />
      )),
    [workbooks]
  );

  return (
    <>
      <WorkbookContainer>
        <div
          className="workbook-list-view__cards-container flex flex-wrap gap-6 w-full self-start"
          role="list"
          aria-label={isGlobal ? 'Public workbooks' : 'My workbooks'}
        >
          {workbookCards}
          {(!isGlobal || currentUserRoles.some(r => r === "Sidekick-CoreTeam")) && <AddWorkbookCard onClick={handleAddClick} isGlobal={isGlobal} />}
        </div>
      </WorkbookContainer>

      <Modal isOpen={isModalOpen} onClose={handleCloseModal}>
        <CreateWorkbookModalContent onSubmit={handleCreateWorkbook} onClose={handleCloseModal} isGlobal={isGlobal}/>
      </Modal>
    </>
  );
};

export default WorkbookListView;
