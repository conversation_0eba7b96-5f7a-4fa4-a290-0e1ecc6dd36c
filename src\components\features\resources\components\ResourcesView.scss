.resourcesview-layout  {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-spacing-l-3, 48px);
    flex: 1 0 0;
    align-self: stretch;
    padding: var(--spacing-spacing-l-4, 64px) 64px;
    color: var(--text-text-invert, #FFFFFF);
    font-family: var(--font-sofia);
    max-width: 680px;
    width: 100%;
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: 36px;
    letter-spacing: 0.192px;
    .resources-container {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        gap: var(--spacing-spacing-m-3, 24px);
        max-width: 680px;
        width: 100%;
        flex: 1 0 0;
        align-self: stretch;
        padding-bottom: var(--spacing-spacing-l-4, 64px);
    }
    .resources-text-content {
        color: var(--text-text-invert, #FFFFFF);
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        letter-spacing: 0.096px;
    }
    .resource-link-list-container {
        padding-left: var(--spacing-spacing-m-2, 16px);
        .resource-link-list {
            list-style-type: disc;
            margin: 0;
            padding-left: 0;
            list-style-position: inside;
            .resource-link-item {
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
                align-items: flex-start;
                .resource-link-item-text {
                    margin-bottom: 0;
                    padding-bottom: 0;
                    color: var(--text-text-invert, #FFFFFF);
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: normal;
                    letter-spacing: 0.096px;
                    text-decoration-line: underline;
                    text-decoration-style: solid;
                    text-decoration-skip-ink: auto;
                    text-decoration-thickness: auto;
                    text-underline-offset: auto;
                    text-underline-position: under;
                }
            }
        }
    }
    .model-information-title {
        color: var(--text-text-invert, #FFFFFF);
        font-family: var(--font-sofia);
        font-size: 20px;
        font-style: normal;
        font-weight: 700;
        line-height: 36px;
        letter-spacing: 0.16px;
        margin-bottom: var(--spacing-spacing-sm-3, 8px);
    }
    .model-information-list-container {
        padding-left: var(--spacing-spacing-m-2, 16px);
        .model-information-list {
            list-style-type: disc;
            margin: 0;
            padding-left: var(--spacing-spacing-m-2, 16px);
            .model-information-item {
                margin-bottom: 0;
                padding-bottom: 0;
                color: var(--text-text-invert, #FFFFFF);
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                letter-spacing: 0.096px;
            }
        }
    }
}

