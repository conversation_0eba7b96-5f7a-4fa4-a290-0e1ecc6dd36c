const getCookieExpiryDate = (hours: number) => {
  const now = new Date();
  now.setTime(now.getTime() + hours * 60 * 60 * 1000);
  return now;
};

export const setGenericCookie = (cookieName: string, cookieValue: string, expires: number) => {
  document.cookie = `${cookieName}=${cookieValue}; expires=${getCookieExpiryDate(
    expires
  ).toUTCString()}; path=/; secure`;
};

export const getGenericCookie = (cookieName: string) => {
  cookieName = `${cookieName}=`;
  const cookies = document.cookie.split(';');
  for (let i = 0; i < cookies.length; i++) {
    let cookie = cookies[i].trim();
    if (cookie.indexOf(cookieName) === 0) {
      return cookie.substring(cookieName.length);
    }
  }
  return null; // Cookie not found
};
