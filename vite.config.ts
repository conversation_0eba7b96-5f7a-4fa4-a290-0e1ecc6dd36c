import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import tailwindcss from '@tailwindcss/vite';

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), tailwindcss()],
  resolve: {
    alias: {
      '@': '/src',
      '@components': '/src/components',
      '@features': '/src/components/features',
      '@layouts': '/src/layouts',
      '@pages': '/src/pages',
      '@hooks': '/src/hooks',
      '@utils': '/src/utils',
      '@types': '/src/types',
      '@data': '/src/data',
      '@contexts': '/src/contexts',
      '@common': '/src/components/common',
    },
  },
  build: {
    outDir: '../src/website/static/ui/',
    emptyOutDir: true,
    rollupOptions: {
      output: {
        name: 'scalableRAG',
        dir: '../src/website/static/ui',
        entryFileNames: `assets/[name].js`,
        chunkFileNames: `assets/[name].js`,
        assetFileNames: `assets/[name].[ext]`,
        manualChunks: {
          // Only separate Mermaid and zoom library for dynamic loading optimization
          mermaid: ['mermaid'],
          'zoom-pan-pinch': ['react-zoom-pan-pinch'],
        },
      },
    },
  },
});
