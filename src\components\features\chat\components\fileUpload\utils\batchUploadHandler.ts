/**
 * This module orchestrates the process of getting signed URLs, uploading files to GCS
 * concurrently, and dispatching state updates based on the outcomes.
 */

import pLimit from 'p-limit';
import { Dispatch } from 'react';
import { ValidatedFile, UploadStatus } from '@/types/fileUpload';
import { mapChatTypeToSessionType } from './fileUploadHelpers';
import { processUploadResults, showUploadResultToasts, UploadResult } from './uploadResultHandlers';
import { SignedUrlEntry, GCSUploadManagerResult } from '../hooks/useGCSUploadManager';
import { FilesAction } from '../state/fileUploadReducer';

// The maximum number of files to upload to GCS at the same time.
const UPLOAD_CONCURRENCY_LIMIT = 5;
// The number of files for which to request signed URLs in a single API call to the backend.
const SIGNED_URL_CHUNK_SIZE = 20;

type GCSUploadManager = Pick<
  GCSUploadManagerResult,
  'fetchBatchSignedUrls' | 'uploadFileToGCSWithSignedUrl' | 'isCancellationRequested'
>;

interface BatchUploadParams {
  filesToProcess: ValidatedFile[];
  gcsUploadManager: GCSUploadManager;
  dispatch: Dispatch<FilesAction>;
  session: {
    id: string | null;
    type: string;
  };
}

/**
 * Handles the end-to-end process of uploading a batch of files. This involves:
 * 1. Fetching signed URLs for all files in chunks.
 * 2. Concurrently uploading the file content to those URLs.
 * 3. Processing the results and dispatching a final state update.
 */
export const handleBatchUpload = async (params: BatchUploadParams): Promise<void> => {
  const { filesToProcess, gcsUploadManager, dispatch, session } = params;
  const { fetchBatchSignedUrls, uploadFileToGCSWithSignedUrl, isCancellationRequested } =
    gcsUploadManager;

  const limit = pLimit(UPLOAD_CONCURRENCY_LIMIT);

  dispatch({
    type: 'UPDATE_MULTIPLE_FILES',
    payload: {
      fileIds: filesToProcess.map(f => f.id),
      updates: { uploadStatus: 'uploading' as UploadStatus },
    },
  });

  try {
    // 1. Fetch all signed URLs in chunks
    const fileChunks = [];
    for (let i = 0; i < filesToProcess.length; i += SIGNED_URL_CHUNK_SIZE) {
      fileChunks.push(filesToProcess.slice(i, i + SIGNED_URL_CHUNK_SIZE));
    }

    const allSignedUrlEntries: { fileId: string; entry: SignedUrlEntry }[] = [];
    for (const chunk of fileChunks) {
      try {
        const signedResp = await fetchBatchSignedUrls({
          files_mimes: chunk.map(f => ({
            file_name: f.name,
            mime_type: f.type,
            file_size: f.size,
          })),
          persist_to_session: false,
          session_id: session.id,
          ...(session.id ? { session_id: session.id } : {}),
          session_type: mapChatTypeToSessionType(session.type),
        });

        // If a new session was created, update the local session object
        // so that subsequent chunks in this same batch can reuse it.
        if (!session.id && signedResp.firestore_session_id) {
          session.id = signedResp.firestore_session_id;
        }

        chunk.forEach((file, index) => {
          allSignedUrlEntries.push({ fileId: file.id, entry: signedResp.signed_urls[index] });
        });
      } catch (e) {
        console.error('Failed to fetch a chunk of signed URLs:', e);
        chunk.forEach(file => {
          allSignedUrlEntries.push({
            fileId: file.id,
            entry: { url: 'ERROR', gs_uri: '', mime_type: file.type, file_size: file.size },
          });
        });
      }
    }

    // 2. Upload files to GCS concurrently
    const finalUploadPromises = filesToProcess.map(vf => {
      const signedUrlData = allSignedUrlEntries.find(s => s.fileId === vf.id);
      if (!signedUrlData || signedUrlData.entry.url === 'ERROR') {
        return Promise.resolve<UploadResult>({
          fileId: vf.id,
          result: { uploadStatus: 'error', uploadError: 'Failed to get a signed URL.' },
        });
      }

      // `limit` wraps the async function to ensure no more than UPLOAD_CONCURRENCY_LIMIT
      // promises are running at the same time.
      return limit(async (): Promise<UploadResult> => {
        if (isCancellationRequested(vf.id)) {
          return {
            fileId: vf.id,
            result: { uploadStatus: 'cancelled', uploadError: 'Upload cancelled by user.' },
          };
        }
        try {
          const res = await uploadFileToGCSWithSignedUrl(vf, signedUrlData.entry);
          return { fileId: vf.id, result: res };
        } catch (e) {
          console.error(`Upload failed for ${vf.id}:`, e);
          const errorMessage =
            e instanceof Error ? e.message : 'Upload failed due to an unknown error';
          return { fileId: vf.id, result: { uploadStatus: 'error', uploadError: errorMessage } };
        }
      });
    });

    const settled = await Promise.allSettled(finalUploadPromises);

    // Step 3: Process all settled promises and dispatch a single action to update the UI state.
    const batchResult = processUploadResults(settled, filesToProcess);
    dispatch({
      type: 'APPLY_UPLOAD_RESULTS',
      payload: { uploadResults: settled, validFiles: filesToProcess },
    });
    showUploadResultToasts(batchResult);
  } catch (err) {
    console.error('A critical error occurred during the batch upload process:', err);
    dispatch({
      type: 'UPDATE_MULTIPLE_FILES',
      payload: {
        fileIds: filesToProcess.map(f => f.id),
        updates: {
          uploadStatus: 'error' as UploadStatus,
          uploadError: err instanceof Error ? err.message : 'Batch upload failed critically.',
        },
      },
    });
  }
};

