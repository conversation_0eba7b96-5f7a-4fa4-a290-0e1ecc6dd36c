import { client } from '@/api/client';

import {
  TranslateParams,
  TranslateLanguagesJSON,
  DetectResponse,
  TranslateTextPayload,
  TranslateTextResponse,
  TranslateUploadFile,
  TranslateUploadPayload,
  FileUploadResponse,
  SignedURLInfo,
  TranslateFileResponse,
  TranslateFileParams,
  TranslateFilePayload,
} from '@/components/features/translate/translateTypes';

const { hostname, pathname } = window.location;
const baseHost = hostname.includes('localhost') ? 'http://localhost:5000' : ``;
let baseApiPath = '/sidekick';
const apiPathPrefix = pathname.split(baseApiPath)[0];
baseApiPath = `${apiPathPrefix}${baseApiPath}`;

const baseRoute = `${baseHost}${baseApiPath}`;

const staticRoute = `${baseHost}${baseApiPath}/static`;

const convertToPayload = (body: TranslateParams): TranslateTextPayload => {
  return {
    text: body.message,
    source_language: body.inputLanguage,
    target_language: body.outputLanguage,
  };
};

export const getLanguages = async (): Promise<TranslateLanguagesJSON> => {
  const response = await client.get<TranslateLanguagesJSON>(`${staticRoute}/language.json`);
  return response.data;
};

export const detectLanguage = async (body: TranslateParams): Promise<DetectResponse> => {
  const payload = convertToPayload(body);
  const response = await client.post<DetectResponse>(`${baseRoute}/detect/language`, payload);
  return response.data;
};

export const translateText = async (body: TranslateParams): Promise<TranslateTextResponse> => {
  const payload = convertToPayload(body);
  const response = await client.post<TranslateTextResponse>(`${baseRoute}/translate/text`, payload);
  return response.data;
};

export const translateFile = async (body: TranslateFileParams): Promise<TranslateFileResponse> => {
  const payload: TranslateFilePayload = [];

  body.translateFiles.forEach(fileInfo => {
    payload.push({
      fileName: fileInfo.fileName,
      gcs_uri: fileInfo.gcs_uri,
      mime_type: fileInfo.mime_type,
      file_size: fileInfo.file_size,
      target_language: body.target_language,
    });
  });
  if (payload.length === 0) {
    throw new Error('No files to translate');
  }
  const response = await client.post<TranslateFileResponse>(`${baseRoute}/translate/file`, payload[0]);
  return response.data;
};

export const signFiles = async (body: TranslateUploadFile[]): Promise<FileUploadResponse> => {
  const payload: TranslateUploadPayload = { files_mimes: body };
  const response = await client.put<FileUploadResponse>(`${baseRoute}/restful/fileupload`, payload);
  return response.data;
};

export const uploadFile = async (file: File, info: SignedURLInfo) => {
  const uploadResponse: Response = await fetch(info.url, {
    method: 'PUT',
    headers: { 'Content-Type': info.mime_type },
    body: file,
  });
  
  if (!uploadResponse.ok) {
    throw new Error(`Upload failed with status: ${uploadResponse.status}`);
  }
  
  return uploadResponse;
};

