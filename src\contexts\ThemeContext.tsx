import React, { createContext, useContext, useState, useEffect } from 'react';
import { Theme, ThemeContextType, ThemeProviderProps } from '@/types/theme';

const defaultContext: ThemeContextType = {
  theme: 'dark',
  isDarkMode: true,
  toggleTheme: () => {},
  setTheme: () => {},
};

export const ThemeContext = createContext<ThemeContextType>(defaultContext);

export const useTheme = () => useContext(ThemeContext);

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  // Force dark theme - commented original code for future restoration
  /*
  // Initialize theme from localStorage or default to 'light'
  const [theme, setThemeState] = useState<Theme>(() => {
    const savedTheme = 'dark';//localStorage.getItem('theme');
    return savedTheme === 'dark' || savedTheme === 'light' ? savedTheme : 'light';
  });

  const isDarkMode = theme === 'dark';
  */

  const [theme] = useState<Theme>('dark');
  const isDarkMode = true;

  useEffect(() => {
    localStorage.setItem('theme', 'dark');
    document.documentElement.classList.add('dark');

    /* Original code for restoration
    localStorage.setItem('theme', theme);

    if (theme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
    */
  }, []);

  // Original toggle functions commented out for future restoration
  /*
  const toggleTheme = () => {
    setThemeState(prevTheme => (prevTheme === 'light' ? 'dark' : 'light'));
  };

  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
  };
  */

  // These functions are kept for compatibility but don't do anything
  const toggleTheme = () => {
    // Disabled - always stay in dark mode
  };

  const setTheme = () => {
    // Disabled - always stay in dark mode
  };

  const value = {
    theme,
    isDarkMode,
    toggleTheme,
    setTheme,
  };

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>;
};
