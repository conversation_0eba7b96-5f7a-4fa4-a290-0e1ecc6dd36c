async function startAuthFlow() {
  const fetchOptions = {
    method: "POST",
    headers: { "Content-Type": "application/json" },
  };

  let a = await fetch(authLink, fetchOptions)
    .then((response) => response.json())
    .then((data) => {
      // console.log(data);
      auth_user(data);
    })
    .catch((error) => console.error("Error fetching data:", error));
}
let last_attempt = new Date();
async function refreshAuthFlow(haltedFetchFunction, haltedFetchArgs) {
  //Unsure why this runs twice
  const fetchOptions = {
    method: "POST",
    headers: { "Content-Type": "application/json" },
  };

  let a = await fetch(authLink, fetchOptions)
    .then((response) => response.json())
    .then((data) => {
      // console.log(data);
      refresh_auth(data);
    })
    .catch((error) => console.error("Error fetching data:", error));
  let now = new Date();
  // console.log(compareDates(last_attempt, now));
  // console.log(haltedFetchFunction);
  // console.log(haltedFetchArgs);

  // if (compareDates(last_attempt, now)) {
  // True if five seconds have passed
  haltedFetchFunction(haltedFetchArgs); //Can be any function.
  // }

  last_attempt = new Date();
}

function auth_user(data) {
  //step 1

  let callback_url = data.callback_url;
  let code_value = getAuthCode();
  let client_id = data.client_id;
  let client_secret = data.client_secret;
  let code_verifier = data.code_verifier;
  let token_url = data.token_url;

  let myHeaders = new Headers();
  myHeaders.append("X-OAUTH-IDENTITY-DOMAIN-NAME", "HighmarkHealthGenAI");
  myHeaders.append("Content-Type", "application/x-www-form-urlencoded");

  let urlencoded = new URLSearchParams();
  urlencoded.append("grant_type", "authorization_code");
  urlencoded.append("redirect_uri", `${callback_url}`); // Might need to mess with this to see if I can have it redirect -> where the user wants to go.
  urlencoded.append("code", `${code_value}`);
  urlencoded.append("client_id", `${client_id}`);
  urlencoded.append("client_secret", `${client_secret}`);
  urlencoded.append("code_verifier", `${code_verifier}`);

  let requestOptions = {
    method: "POST",
    headers: myHeaders,
    body: urlencoded,
    redirect: "follow",
  };

  fetch(`${token_url}`, requestOptions)
    .then((response) => response.text())
    .then((result) => send_token(result, false))
    .catch((error) => console.log("error", error));
}

function refresh_auth(data) {
  //outputs a new auth token that needs to go back to the backend to verify & store it in session/cookie
  // Step 1, refresh token sets new token cookie //unverified
  // Step 2 send token -> backend for verification //being verified
  // // Step 2 response ->

  let client_id = data.client_id;
  let client_secret = data.client_secret;
  let token_url = data.token_url;
  let refresh_token = getGenericCookie("refresh_token");

  if (refresh_token === null) {
    console.log("User isn't authenticated. Redirecting");
    // Send user to /login
  }

  const cics = btoa(`${client_id}:${client_secret}`);

  const headers = {
    "X-OAUTH-IDENTITY-DOMAIN-NAME": "HighmarkHealthGenAI",
    "Content-Type": "application/x-www-form-urlencoded",
    Authorization: `Basic ${cics}`,
  };

  let new_data = {
    grant_type: "REFRESH_TOKEN",
    refresh_token: refresh_token,
  };

  let urlencoded = new URLSearchParams(new_data);

  let requestOptions = {
    method: "POST",
    headers: headers,
    body: urlencoded,
    redirect: "follow",
  };

  fetch(`${token_url}`, requestOptions)
    .then((response) => response.text())
    .then((result) => send_token(result, true))
    .catch((error) => console.log("error", error));
}

function send_token(token, refresh = false) {
  // In here set token to be cookie AND send it to backend.

  const auth_token = JSON.parse(token);
  console.log("In send_token");
  // console.log(auth_token);
  // console.log(typeof token);
  // console.log(typeof auth_token);
  // console.log(auth_token["access_token"]);
  // console.log(auth_token["expires_in"]);
  let timeout = 1;
  if (auth_token["expires_in"] == 3600) {
    timeout = 1; //1 hour
  } else {
    timeout = 0.25; //15 minutes
  }

  //This is getting set as undefined from refresh_auth.
  setGenericCookie("token", auth_token["access_token"], timeout); //Mostly for tokens generated via refresh token.

  let token_json = { auth: auth_token, refresh: refresh };

  // step 2 // Could be in backend with socketio forcing a redirect here.
  // verify token process, will redirect back to /login if bad token, redirects to onboard_user -> chat if good.
  const url = `/sidekick/token`;
  fetch(url, {
    method: "POST",
    redirect: "follow",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(token_json),
  })
    .then((response) => {
      // console.log(response);
      if (response.redirected) {
        window.location.href = response.url;
      }
    })
    .catch(function (err) {
      console.info(err + " url: " + url);
    });
}

function getAuthCode() {
  //Assuming  in /callback
  const queryString = window.location.search; //only run in auth.html
  const urlParams = new URLSearchParams(queryString.substring(1));
  const code = urlParams.get("code");
  return code;
  // socket.emit("get_auth", { room: room, auth_code: code });
}

function compareDates(date1, date2) {
  // Calculate the time difference in milliseconds
  const timeDelta = Math.abs(date1.getTime() - date2.getTime());

  // Convert milliseconds to minutes
  const minutesDelta = timeDelta / 1000;

  // Return true if the time difference is 5 minutes or more, false otherwise
  return minutesDelta >= 5;
}

function checkAuth() {
  //If true auth tokens still exist, if false
  const refresh_token = getGenericCookie("refresh_token");
  const token = getGenericCookie("token");
  if (refresh_token !== null && token !== null) {
    //Refresh token and Auth token exists
    return true;
  } else if (token !== null) {
    // Check for access token first
    //Only auth token exists, website will work as normal.
    return true;
  } else if (refresh_token !== null) {
    //Refresh token exists, auth token doesn't, refresh auth process will happen.
    return true;
  } else {
    // No tokens exist
    //No token exists, redirect user to normal auth flow.
    return false;
  }
  return false; //This case should never exist
}
