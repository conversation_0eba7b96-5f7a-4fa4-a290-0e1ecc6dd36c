import datetime
import json
import os
import time
import traceback

import google.auth
import requests as req
import vertexai
from google.auth import impersonated_credentials
from google.auth.transport import requests
from google.cloud import pubsub_v1, storage
from google.cloud.storage import Blob

from website.config import get_config_val
from website.extensions import oidc_variables
from website.logger_framework import make_logger

workflow = "common"
logger_info, logger_error = make_logger(workflow, __file__)


def vertex_init_cloud(project_id="gcp-genai-3-d-services-dciw") -> bool:
    """Initializes the Vertex AI environment.
    Args:
        project_id (str, optional): The Google Cloud Project ID. Defaults to "gcp-genai-3-d-services-dciw".
    Returns:
        bool: True if initialization is successful, False otherwise.
    """
    try:
        args = {
            "project": project_id,
            "location": "us-central1",
        }
        vertexai.init(**args)
        return True
    except Exception as e:
        logger_error.error(
            f"{workflow} : vertex_init_cloud : {str(e)} : {traceback.format_exc()}"
        )
        return None


def fetch_ENV_by_project_id(cloudrun_project_id):
    """Fetches the environment based on the Cloud Run project ID.
    Args:
        cloudrun_project_id (str): The Cloud Run project ID.
    Returns:
        str: The environment (e.g., "dev", "stage", "prod").
    """
    try:
        cloud_project_list = {
            "gcp-genai-3-d-services-dciw": "dev",
            "gcp-genai-3-t-services-ynj2": "stage",
            "gcp-genai-3-p-services-tfkr": "prod",
        }
        if cloudrun_project_id not in cloud_project_list.keys():
            logger_info.info(
                f"{workflow} : fetch_ENV_by_project_id : This is pipeline unit test scenario"
            )
            cloudrun_project_id = "gcp-genai-3-d-services-dciw"
            ENV_VAL = "dev"
        else:
            ENV_VAL = cloud_project_list[cloudrun_project_id]
        return ENV_VAL
    except Exception as e:
        logger_error.error(
            f"{workflow} : fetch_env_by_project_id : {str(e)} : {traceback.format_exc()}"
        )
        return None


def init_oidc_variables():
    """Initializes OIDC variables.
    Returns:
        dict: A dictionary containing OIDC variables.
    """
    try:
        scope = "resource.READ"
        oauth_domain = "HighmarkHealthGenAI"
        cloudrun_project_id = get_cloudrun_project_id()
        # cloudrun_project_id = "gcp-genai-3-d-services-dciw"
        logger_info.info(
            f"{workflow} : init_oidc_variables : Cloud run project id recognized: {cloudrun_project_id}"
        )
        ENV_VAL = fetch_ENV_by_project_id(cloudrun_project_id)
        client_credentials = get_config_val("credentials", ENV_VAL)
        response = client_credentials
        response.update(
            {
                "scope": scope,
                "oauth_domain": oauth_domain,
                "cloudrun_project_id": cloudrun_project_id,
            }
        )
        return response
    except Exception as e:
        logger_error.error(
            f"{workflow} : init_oidc_variables : {str(e)} : {traceback.format_exc()}"
        )
        return None


def get_cloudrun_project_id():
    """Retrieves the Cloud Run project ID from the Google Metadata server.
    Returns:
        str: The Cloud Run project ID.
    """
    try:
        url = "http://metadata.google.internal/computeMetadata/v1/project/project-id"
        Header = {"Metadata-Flavor": "Google"}
        logger_info.info(
            f"{workflow} : get_cloudrun_project_id : Cloud run project id initialized: "
        )
        response = req.get(url, headers=Header)
        logger_info.info(
            f"{workflow} : get_cloudrun_project_id : request to google meta server to retrive cloud run project id: {response}"
        )
        cloudrun_project_id = response.text
        logger_info.info(
            f"{workflow} : get_cloudrun_project_id : Cloud run project id from google meta server: {cloudrun_project_id}"
        )
        return cloudrun_project_id
    except Exception as e:
        logger_error.error(
            f"{workflow} : get_cloudrun_project_id : {str(e)} : {traceback.format_exc()}"
        )
        logger_error.error(
            f"{workflow} : get_cloudrun_project_id : Setting up cloudrun id to use gcp-genai-3-d-services-dciw.\n This is FATAL if not LOCAL."
        )
        return "gcp-genai-3-d-services-dciw"


class GCS:
    """Class for interacting with Google Cloud Storage (GCS)."""

    indent_num = 4

    def __init__(self) -> None:
        """Initializes the GCS client and retrieves configuration values."""
        self.storage_client = storage.Client()
        self.project_id = oidc_variables["cloudrun_project_id"]
        ENV_VAL = fetch_ENV_by_project_id(self.project_id)
        self.emails_to_buckets = get_config_val("emails_to_buckets", ENV_VAL)
        # self.file_bucket = get_config_val("file_bucket", ENV_VAL)
        # self.file_signed_url_sa = get_config_val("file_signed_url_sa", ENV_VAL)
        self.file_upload = get_config_val("file_upload", ENV_VAL)

    def get_file_bucket(self, user: str) -> dict:
        """Retrieves the GCS upload bucket name for a given user.
        Args:
            user (str): The user's email address.
        Returns:
            dict: keys: file_bucket & file_signed_url_sa
        """
        bucket_sa: dict = self.file_upload
        domain = f"{user.split('@')[1]}".lower()
        return_info = bucket_sa.get(domain, bucket_sa["default"])
        logger_info.info(
            f"{workflow} : GCS - gcs_bucket : Returning {return_info} for {domain}"
        )
        return return_info

    def gcs_bucket(self, user: str):
        """Retrieves the GCS bucket name for a given user.
        Args:
            user (str): The user's email address.
        Returns:
            str: The GCS bucket name.
        """
        buckets = self.emails_to_buckets
        domain = f"@{user.split('@')[1]}".lower()
        return_bucket = buckets.get(domain, buckets["default"])
        logger_info.info(
            f"{workflow} : GCS - gcs_bucket : Returning {return_bucket} for {domain}"
        )
        return return_bucket

    @staticmethod
    def get_blob_list(bucket, user, filename, all_session=False, user_session=False):
        """Retrieves a list of blobs from a GCS bucket.
        Args:
            bucket (google.cloud.storage.bucket.Bucket): The GCS bucket object.
            user (str): The user's email address.
            filename (str): The filename to search for.
            all_session (bool, optional): Whether to retrieve all sessions. Defaults to False.
            user_session (bool, optional): Whether to retrieve the user session file. Defaults to False.
        Returns:
            google.cloud.storage.blob.Blob | google.cloud.storage.blob.BlobIterator: The blob object or a list of blobs.
        """
        try:
            if user_session:
                destination_blob_name = f"{user}/user_info.json"
                logger_info.info(
                    f"{workflow} : get_blob_list : Requesting the attestation: {destination_blob_name}"
                )
                return bucket.blob(destination_blob_name)
            if not all_session:
                destination_blob_name = f"{user}/{filename}"
                return bucket.blob(destination_blob_name)
            destination_blob_name = f"{user}/session/**"
            logger_info.info(
                f"{workflow} : get_blob_list : Requesting all sessions: {destination_blob_name}"
            )
            blob_list = bucket.list_blobs(match_glob=destination_blob_name)
            return blob_list
        except Exception as e:
            logger_error.error(
                f"{workflow} : get_blob_list : {str(e)} : {traceback.format_exc()}"
            )
            return None

    @staticmethod
    def retrieve_session_data(blob):  # TODO:
        """Retrieves data from a blob in GCS.
        Args:
            blob (google.cloud.storage.blob.Blob): The blob object.
        Returns:
            dict: The data retrieved from the blob.
        """
        try:
            session_file = blob.open(mode="r")
            session_data = json.load(session_file)
            return session_data
        except Exception as e:
            logger_error.error(
                f"{workflow} : retrieve_session_data : {str(e)} : {traceback.format_exc()}"
            )
            return None

    @staticmethod
    def extract_bucket_and_blob_names(
        gcs_uri,
    ):  # TODO: GCS Removal - ? - Unused, Verify if needed.
        """Extracts the bucket and blob names from a GCS URI.
        Args:
            gcs_uri (str): The GCS URI (e.g., 'gs://bucket_name/path/to/file.pdf').
        Returns:
            tuple: A tuple containing the bucket name and blob name.
        """
        parts = gcs_uri.replace("gs://", "").split("/", 1)
        bucket_name = parts[0]
        blob_name = parts[1] if len(parts) > 1 else ""
        return bucket_name, blob_name

    def download_blob(self, gcs_path: str, local_file_path: str, email) -> bool:
        """Downloads a blob from GCS to a local file.
        Args:
            gcs_path (str): The GCS path of the file (e.g., "bucket_name/folder/file.pdf").
            local_file_path (str): The local path where the file should be saved.
        Returns:
            bool: True if download is successful, False otherwise.
        """
        try:
            bucket_info = self.get_file_bucket(email)
            bucket_name = bucket_info["file_bucket"]
            if gcs_path.startswith(f"gs://{bucket_name}/"):
                gcs_path = gcs_path[len(f"gs://{bucket_name}/"):]
            bucket = self.storage_client.bucket(bucket_name)
            blob = bucket.blob(gcs_path)
            # Download the blob to the local file path
            blob.download_to_filename(local_file_path)
            logger_info.info(
                f"{workflow} : download_blob : Successfully downloaded {gcs_path} to {local_file_path}"
            )
            return True
        except Exception as e:
            logger_error.error(
                f"{workflow} : download_blob : Failed to download {gcs_path} to {local_file_path}: {str(e)} : {traceback.format_exc()}"
            )
            return False

    # v2 only
    def fetch_user_session(
        self, user: str, session_id: str
    ) -> (
        dict
    ):  # TODO: GCS Removal - R - History (Currently required for Feedback, will need a Firestore implementation)
        """Retrieves a user's session data from GCS.
        Args:
            user (str): The user's email address.
            session_id (str): The session ID.
        Returns:
            dict: The user's session data.
        """
        try:
            filename = f"session/{session_id}.json"
            bucket = self.gcs_bucket(user)
            bucket = self.storage_client.bucket(bucket)
            blob = GCS.get_blob_list(bucket, user, filename)
            if not blob.exists():
                return {}
            logger_info.info(
                f"{workflow} : fetch_user_session : Session retrieved: {session_id}"
            )
            return GCS.retrieve_session_data(blob)
        except Exception as e:
            logger_error.error(
                f"{workflow} : fetch_user_session : {str(e)} : {traceback.format_exc()}"
            )
            return None

    def upload_file(self, local_file_path, gcs_path, email):
        """Only used by Sidekick. Not used by users. Uploads file for download"""
        try:
            bucket_info = self.get_file_bucket(email)
            bucket_name = bucket_info["file_bucket"]
            bucket = self.storage_client.bucket(bucket_name)
            blob = bucket.blob(gcs_path)
            blob.upload_from_filename(local_file_path)
            gs_uri = f"{gcs_path}"
            return gs_uri
        except Exception as e:
            logger_error.error(
                f"{workflow} : upload_file : {str(e)} : {traceback.format_exc()}"
            )
            raise

    def generate_upload_signed_url_v4(self, user: str, file_name: str, mime_type: str):
        """Generates a signed URL for uploading a file to GCS.
        Args:
            user (str): The user's email address.
            file_name (str): The name of the file to upload.
            mime_type (str): The MIME type of the file.
        Returns:
            tuple: A tuple containing a boolean indicating success, the signed URL, and the GCS path.
        """
        try:
            user = user.lower()
            bucket_info = self.get_file_bucket(user)
            bucket = bucket_info["file_bucket"]
            service_account_email = bucket_info["file_signed_url_sa"]
            bucket = self.storage_client.bucket(bucket)
            credentials, project_id = google.auth.default()
            target_scopes = [
                "https://www.googleapis.com/auth/cloud-platform",
                "https://www.googleapis.com/auth/devstorage.read_write",
            ]
            if credentials.token is None:
                credentials.refresh(requests.Request())
            impersonated_creds = impersonated_credentials.Credentials(
                source_credentials=credentials,
                target_principal=service_account_email,
                target_scopes=target_scopes,
            )
            if impersonated_creds.token is None:
                impersonated_creds.refresh(requests.Request())
            email_domain = user.split("@")[1]
            blob_name = f"{email_domain}/{user}/{file_name}"
            blob = bucket.blob(blob_name)
            url = blob.generate_signed_url(
                version="v4",
                expiration=datetime.timedelta(minutes=45),
                service_account_email=service_account_email,
                method="PUT",
                access_token=impersonated_creds.token,
                content_type=mime_type,
            )
            gs_path = f"gs://{blob.bucket.name}/{blob.name}"
            return True, url, gs_path
        except Exception as e:
            logger_error.error(
                f"{workflow} : generate_upload_signed_url_v4 : {str(e)} : {traceback.format_exc()}"
            )
            return False, "", ""

    def generate_download_signed_url_v4(
        self, file_path: str, user: str, mime_type: str = "", force_download=False
    ):
        """Generates a v4 signed URL for downloading a blob.
        Note that this method requires a service account key file. You can not use
        this if you are using Application Default Credentials from Google Compute
        Engine or from the Google Cloud SDK.
        """
        try:
            bucket_info = self.get_file_bucket(user)
            bucket = bucket_info["file_bucket"]
            service_account_email = bucket_info["file_signed_url_sa"]

            bucket = self.storage_client.bucket(bucket)
            credentials, project_id = google.auth.default()
            target_scopes = [
                "https://www.googleapis.com/auth/cloud-platform",
                "https://www.googleapis.com/auth/devstorage.read_write",
            ]
            if credentials.token is None:
                credentials.refresh(requests.Request())
            impersonated_creds = impersonated_credentials.Credentials(
                source_credentials=credentials,
                target_principal=service_account_email,
                target_scopes=target_scopes,
            )
            if impersonated_creds.token is None:
                impersonated_creds.refresh(requests.Request())
            # email_domain = user.split("@")[1]
            # blob_name = f"{email_domain}/{user}/{file_name}"
            blob = bucket.blob(file_path)

            response_disposition = None
            if force_download:
                filename = os.path.basename(file_path)
                response_disposition = f'attachment; filename="{filename}"'

            url = blob.generate_signed_url(
                version="v4",
                expiration=datetime.timedelta(minutes=45),
                service_account_email=service_account_email,
                method="GET",
                access_token=impersonated_creds.token,
                response_disposition=response_disposition,
                # content_type=mime_type,
            )
            return True, url
        except Exception as e:
            logger_error.error(
                f"{workflow} : generate_download_signed_url_v4 : {str(e)} : {traceback.format_exc()}"
            )
            return False, ""

    def verify_file(self, file_path: str, email):
        """Verifies if a file exists in GCS.
        Args:
            file_path (str): The GCS path to the file.
        Returns:
            bool: True if the file exists, False otherwise.
        """
        try:
            bucket_info = self.get_file_bucket(email)
            bucket = bucket_info["file_bucket"]
            remove_string = f"gs://{bucket}/"
            if file_path.startswith(remove_string):
                file_path = file_path[len(remove_string):]
            bucket = self.storage_client.bucket(bucket)
            blob = bucket.get_blob(file_path)
            if blob.exists():
                return True
            else:
                logger_error.error(
                    f"{workflow} : verify_file : File not found, waiting two seconds "
                )
                time.sleep(2)
                if blob.exists():
                    return True
                raise Exception("File not found.")
        except Exception as e:
            logger_error.error(
                f"{workflow} : verify_file : {str(e)} : {traceback.format_exc()}"
            )
            return False

    def delete_file(self, file_path: str, email: str) -> bool:
        """Deletes a file from GCS.
        Args:
            file_path (str): The GCS path to the file (gs://bucket/path or just path).
            email (str): User email for bucket identification.
        Returns:
            bool: True if deletion is successful or file doesn't exist, False otherwise.
        """
        try:
            bucket_info = self.get_file_bucket(email)
            bucket_name = bucket_info["file_bucket"]

            # Remove gs://bucket/ prefix if present
            remove_string = f"gs://{bucket_name}/"
            if file_path.startswith(remove_string):
                file_path = file_path[len(remove_string):]

            bucket = self.storage_client.bucket(bucket_name)
            blob = bucket.blob(file_path)

            # Check if file exists before attempting deletion
            if not blob.exists():
                logger_info.info(
                    f"{workflow} : delete_file : File not found (already deleted?): {file_path}"
                )
                return True  # Consider non-existent file as successful deletion

            # Attempt to delete the file
            blob.delete()
            logger_info.info(
                f"{workflow} : delete_file : Successfully deleted file: {file_path} for user: {email}"
            )
            return True

        except Exception as e:
            logger_error.error(
                f"{workflow} : delete_file : Failed to delete {file_path}: {str(e)} : {traceback.format_exc()}"
            )
            return False


def pubsub_init_cloud():
    """Initializes the Pub/Sub publisher client.
    Returns:
        google.cloud.pubsub_v1.PublisherClient: The Pub/Sub publisher client.
    """
    return pubsub_v1.PublisherClient()
