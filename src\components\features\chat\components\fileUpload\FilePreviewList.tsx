import React from 'react';
import { ValidatedFile } from '@/types/fileUpload';
import SelectedFileItem from './SelectedFileItem';

interface FilePreviewListProps {
  selectedFiles: ValidatedFile[];
  onRemoveFile: (fileId: string) => void;
}

const FilePreviewList: React.FC<FilePreviewListProps> = ({ selectedFiles, onRemoveFile }) => {
  const block = 'file-preview-list'; 
  if (selectedFiles.length === 0) {
    return null; 
  }

  return (
    <div className={`${block} mt-2 mb-2`}>
      {selectedFiles.map(file => (
        <SelectedFileItem key={file.id} file={file} onRemove={onRemoveFile} />
      ))}
    </div>
  );
};

export default FilePreviewList;
