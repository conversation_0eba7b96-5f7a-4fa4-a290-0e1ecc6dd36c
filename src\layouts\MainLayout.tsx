import React, { useContext } from 'react';
import { Outlet, useMatches } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';

import { SidebarContext } from '@contexts/SidebarContext';
import Sidebar from '@/components/layout/Sidebar';
import { TopBar, TopBarHandle, ExpandButton } from '@/components/layout/main';
import { useThemeStyles } from '@hooks/useThemeStyles';

import './layout.scss';

const MainLayout: React.FC = () => {
  const { classes } = useThemeStyles();
  const reversedMatches = [...useMatches()].reverse();
  const { toggleSidebar, isSidebarExpanded } = useContext(SidebarContext);

  const topBarTitle =
    (
      reversedMatches.find(match => {
        return Boolean(match.handle && (match.handle as TopBarHandle).topBarTitle);
      })?.handle as TopBarHandle
    )?.topBarTitle ?? 'Untitled';

  const topBarIcon = (
    reversedMatches.find(match => {
      return Boolean(match.handle && (match.handle as TopBarHandle).topBarIcon);
    })?.handle as TopBarHandle
  )?.topBarIcon;

  const topBarContent =
    (
      reversedMatches.find(match => {
        return Boolean(match.handle && (match.handle as TopBarHandle).topBarContent);
      })?.handle as TopBarHandle
    )?.topBarContent ?? null;

  return (
    <div className="main-layout flex h-screen overflow-hidden layout-main">
      {isSidebarExpanded && (
        <div
          className={`main-layout__sidebar-container layout-sidebar-container ${classes.sidebar} transition-all duration-300 ease-in-out`}
        >
          <Sidebar />
        </div>
      )}

      <div className="main-layout__content-wrapper flex flex-col flex-1 overflow-hidden layout-content-container">
        {!isSidebarExpanded && <ExpandButton onToggle={toggleSidebar} />}
        <main className={`layout-content ${classes.background} transition-colors duration-300`}>
          <TopBar
            topBarTitle={topBarTitle}
            topBarIcon={topBarIcon}
            sidebarCollapsed={!isSidebarExpanded}
          >
            {topBarContent}
          </TopBar>
          <div className="layout-content-outlet">
            <Outlet />
          </div>
        </main>
      </div>
      <ToastContainer
        stacked
        hideProgressBar
        position="bottom-center"
        newestOnTop={true}
        autoClose={5000}
        customProgressBar={false}
        closeButton={false}
      />
    </div>
  );
};

export default MainLayout;
