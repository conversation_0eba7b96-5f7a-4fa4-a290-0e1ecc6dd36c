.sidekick-value-slider {
  &__container {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-spacing-m-2);
  }

  &__label {
    font-size: 14px;
    color: var(--text-text-invert);
    margin-right: var(--spacing-spacing-sm-3);
    min-width: var(--spacing-spacing-m-3);
    width: 25%;
    text-align: center;
  }

  &__label-display {
    font-size: 14px;
    margin-left: var(--spacing-spacing-sm-3);
    min-width: var(--spacing-spacing-l-2);
    text-align: center;
    font-weight: 500;
    width: 25%;
  }

  &__control {
    display: flex;
    width: 160px;
    touch-action: none;
    align-items: center;
    padding-top: 12px;
    padding-bottom: 12px;
    user-select: none;
  }

  &__track {
    height: 4px;
    width: 100%;
    border-radius: 4px;
    background-color: var(--brand-hmk-primary-200);
    box-shadow: inset 0 0 0 1px;
    user-select: none;

    &--disabled {
      opacity: 0.5;
    }
  }

  &__indicator {
    border-radius: 4px;
    background-color: var(--brand-hmk-primary-400);
    user-select: none;

    &--disabled {
      background-color: var(--brand-hmk-shades-300);
    }
  }

  &__thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: var(--neutral-white);
    outline: 1px solid;
    outline-color: var(--brand-hmk-shades-300);
    user-select: none;
    &:hover {
      cursor: pointer;
    }
    &:active {
      cursor: grab;
    }

    &--disabled {
      background-color: var(--brand-hmk-shades-200);
      &:hover {
        cursor: not-allowed;
      }
    }
  }
}

