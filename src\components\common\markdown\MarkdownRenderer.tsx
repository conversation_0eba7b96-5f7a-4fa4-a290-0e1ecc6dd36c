import React, {
  useMemo,
  useState,
  useRef,
  isValidElement,
  ElementType,
  ComponentPropsWithoutRef,
  useEffect,
} from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkBreaks from 'remark-breaks';
import remarkSuperSub from 'remark-supersub';
import { PrismLight as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import clsx from 'clsx';
import { SessionType } from '@/types/promptTemplateTypes';
import { useTheme } from '@/contexts/ThemeContext';
import MermaidRendererLazy from './mermaid/MermaidRendererLazy';
import './MarkdownStyles.scss';

export interface MarkdownRendererProps {
  content: string;
  className?: string;
  isUser?: boolean;
  isInline?: boolean;
  onCitationClick?: (citationNumber: number) => void;
  isPolicyContext?: boolean;
  sessionType?: SessionType;
}

/* ---------- wrapper with copy button ---------- */
const PreCode = ({ children, ...props }: any) => {
  const [copied, setCopied] = useState(false);
  const [mermaidCode, setMermaidCode] = useState('');
  const preRef = useRef<HTMLPreElement>(null);

  const childProps = isValidElement(children) ? (children.props as any) : {};
  const lang = childProps.className ? (/language-(\w+)/.exec(childProps.className) || ['', ''])[1] : '';

  // Check for Mermaid code
  useEffect(() => {
    if (preRef.current) {
      const mermaidDom = preRef.current.querySelector('code.language-mermaid');
      if (mermaidDom) {
        setMermaidCode((mermaidDom as HTMLElement).innerText);
      }
    }
  }, [children]);

  const onCopy = () => {
    const txt = preRef.current?.textContent ?? '';
    if (!txt) return;
    navigator.clipboard.writeText(txt).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 1500);
    });
  };

  return (
    <>
      <div className="code-block-wrapper">
        <div className="code-block-header">
          <span className="code-language">{lang ? lang.toUpperCase() : 'TEXT'}</span>
          <button className="copy-button" onClick={onCopy} title={copied ? 'Copied ✅' : 'Copy Code'}>
            {copied ? '✅ Copied' : 'Copy'}
          </button>
        </div>
        <pre ref={preRef} className="code-block-content" {...props}>
          {children}
        </pre>
      </div>
      {mermaidCode.length > 0 && <MermaidRendererLazy code={mermaidCode} key={mermaidCode} />}
    </>
  );
};

/* ---------- code block ---------- */
type CustomCodeProps = ComponentPropsWithoutRef<'code'> & {
  inline?: boolean;
  isDarkTheme?: boolean;
};

const CustomCode = ({ inline, className, children, isDarkTheme = false, ...props }: CustomCodeProps) => {
  const match = /language-(\w+)/.exec(className || '');
  const lang = match ? match[1] : 'text';
  const codeText = String(children).replace(/\n$/, '');

  const isInlineCode = inline !== false && !/\n/.test(codeText);

  if (isInlineCode) {
    return (
      <code className={clsx('inline-code', className)} {...props}>
        {children}
      </code>
    );
  }

  return (
    <SyntaxHighlighter
      language={lang}
      style={vscDarkPlus as any}
      PreTag="div"
      customStyle={{ margin: 0, padding: '1rem' }}
      {...props}
    >
      {codeText}
    </SyntaxHighlighter>
  );
};

// Create static components with theme support
const createStaticComponents = (isDarkTheme: boolean) => ({
  pre: PreCode,
  code: (props: any) => <CustomCode {...props} isDarkTheme={isDarkTheme} />,
  h1: ({ children, ...p }: React.HTMLAttributes<HTMLHeadingElement>) => <h1 {...p}>{children}</h1>,
  h2: ({ children, ...p }: React.HTMLAttributes<HTMLHeadingElement>) => <h2 {...p}>{children}</h2>,
  h3: ({ children, ...p }: React.HTMLAttributes<HTMLHeadingElement>) => <h3 {...p}>{children}</h3>,
  img: () => <span>[Image]</span>,
  a: (props: React.ComponentPropsWithoutRef<'a'>) => <a {...props} target="_blank" rel="noopener noreferrer" />,
});

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  className = '',
  isUser = false,
  isInline = false,
  onCitationClick,
  isPolicyContext = false,
  sessionType = SessionType.CHAT,
}) => {
  const { isDarkMode } = useTheme();

  // Pre-process content to ensure all URLs are render by remark-gfm
  const processedContent = useMemo(() => {
    if (!content) return '';
    // This regex finds URLs starting with 'www.' that are not preceded by '://' or other characters
    // to avoid corrupting existing links (e.g., in query params).
    const wwwRegex = /(^|[\s\n[(])((?:www\.)[^\s<)]+)/gi;
    let txt = content.replace(wwwRegex, (_, p1, p2) => `${p1}https://${p2}`);
    txt = txt.replace(/^[ \t]{4,}(?=[*+-]\s)/gm, '');
    return txt;
  }, [content]);

  //plug‑in list
  const remarkPlugins = useMemo(() => {
    const plugins: any[] = [remarkGfm, remarkSuperSub];
    if (isUser) plugins.splice(1, 0, remarkBreaks);
    return plugins;
  }, [isUser]);

  // Processed content ready for rendering
  const mdSource = processedContent;

  /* ---------- component renderers ---------- */
  const components = useMemo(
    () => ({
      ...createStaticComponents(isDarkMode),
      table: ({ children, ...p }: React.HTMLAttributes<HTMLTableElement>) => {
        const isPolicy = isPolicyContext || sessionType === SessionType.POLICY;
        const wrapperClass = isPolicy ? 'policy-table-wrapper' : 'table-wrapper';
        return (
          <div className={wrapperClass}>
            <table {...p}>{children}</table>
          </div>
        );
      },
      sup: ({ children, ...p }: React.HTMLAttributes<HTMLElement>) => {
        // Extract citation number if this is a citation
        const citationNumber =
          onCitationClick && children
            ? (() => {
                const text = String(children);
                const match = text.match(/\[(\d+)\]/);
                return match ? parseInt(match[1], 10) : null;
              })()
            : null;

        const handleClick = citationNumber && onCitationClick ? () => onCitationClick(citationNumber) : undefined;

        return (
          <sup
            className={citationNumber ? 'citation-superscript' : ''}
            style={{
              verticalAlign: 'super',
              fontSize: '0.75em',
              marginLeft: '0.1em',
              cursor: handleClick ? 'pointer' : 'inherit',
            }}
            onClick={handleClick}
            {...p}
          >
            {children}
          </sup>
        );
      },
    }),
    [isPolicyContext, sessionType, onCitationClick, isDarkMode]
  );

  /* ---------- wrapper element ---------- */
  const Wrapper: ElementType = isInline ? 'span' : 'article';

  return (
    <Wrapper
      className={clsx(
        'markdown-body',
        {
          'user-message': isUser,
          'policy-context': isPolicyContext,
          'policy-render': sessionType === SessionType.POLICY || isPolicyContext,
          inline: isInline,
        },
        className
      )}
    >
      <ReactMarkdown remarkPlugins={remarkPlugins} components={components}>
        {mdSource}
      </ReactMarkdown>
    </Wrapper>
  );
};

export default MarkdownRenderer;
