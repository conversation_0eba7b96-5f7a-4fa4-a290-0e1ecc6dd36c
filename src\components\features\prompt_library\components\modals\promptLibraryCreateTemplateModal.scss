/* prompt-form block */


.prompt-form {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    align-self: stretch;
    width: 710px;
    height: 256px;
    gap: 16px;
    overflow-y: auto;
    &::-webkit-scrollbar {
      width: 6px;
    }
    &::-webkit-scrollbar-track {
        border-radius: calc(infinity * 1px);
    }
    &::-webkit-scrollbar-thumb {
        border-radius: 4px;
        background-color: #0066B1;
    }
  }
  
  .prompt-form__session-select-container {
    display: flex;
    justify-content: center;
    align-items: center;
    background: #00223C;
    width: 120px;
    border-radius: 8px;
    
    .prompt-form__session-select {
      border: 2px solid #0066B1;
      padding: 8px;
      color: #FFF;
      border-radius: 8px;
      background-color: #00223C;
      font-size: 16px;
      cursor: pointer;
      width: 120px;
      outline: none;
    }

    &:focus {
      outline: none;
    }

    .prompt-form__session-select option:hover {
      background-color: #003963;
    }
  }

  .prompt-form__field {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    align-self: stretch;
    border-radius: 2px;
    width: 704px;
    color: #495055;
  }
  
  .prompt-form__field--name {
    height: 48px;
  }
  
  .prompt-form__field--content {
    height: 192px;
  }
  
  .prompt-form__field--filled {
    color: #FFFFFF;
  }
  
  .prompt-form__input-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    align-self: stretch;
    width: 704px;
    border-radius: 2px;
    background-color: #00223C;
    border: 1px solid #6D787F;
    padding: 0 12px;
  }
  
  .prompt-form__input-container--large {
    height: 192px;
  }
  
  .prompt-form__input-container--focused {
    border: 2px solid #0066B1;
  }
  
  .prompt-form__input-wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
    align-self: stretch;
    flex-shrink: 0;
    width: 680px;
    height: 48px;
    min-height: 24px;
    min-width: 24px;
    padding: 8px 0;
  }
  
  .prompt-form__input {
    font-weight: 500;
    font-size: 16px;
    outline: none;
    width: 100%;
    line-height: 25.6px;
    letter-spacing: 0.128px;
  }

  .prompt-form__textarea-wrapper {
    display: flex;
    flex-direction: row;
    align-self: stretch;
    flex-shrink: 0;
    width: 680px;
    height: 192px;
    min-height: 26px;
    padding: 8px 0px;
  }
  
  .prompt-form__textarea {
    font-weight: 500;
    font-size: 16px;
    word-wrap: anywhere;
    resize: none;
    outline: none;
    width: 100%;
    height: 192px;
    line-height: 25.6px;
    letter-spacing: 0.128px;
  }
  
  .prompt-form__icon {
    display: flex;
    align-items: center;
    width: 24px;
    height: 24px;
  }
  
  .prompt-form__edit-icon {
    width: 24px;
    height: 24px;
  }
  
  /* prompt-actions block */
  .prompt-actions {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    width: 704px;
    height: 40px;
  }
  
  .prompt-actions__button {
    display: flex;
    align-items: center;
    height: 40px;
    padding: 8px 12px;
    border-radius: 50px;
    cursor: pointer;
    outline: none;
    white-space: nowrap;
  }
  
  .prompt-actions__button--delete {
    width: 169px;
    border: 1px solid #6D787F;
  }
  
  .prompt-actions__button--save {
    width: 155px;
    border: 1px solid #6D787F;
    color: #6D787F;
  }
  
  .prompt-actions__button--save-run {
    width: 220px;
    border: 1px solid #0066B1;
    color: #FFFFFF;
  }
  
  .prompt-actions__button--run {
    width: 149px;
    border: 1px solid #6D787F;
    color: #6D787F;
  }
  
  .prompt-actions__button--primary {
    background-color: #0066B1;
    color: #FFFFFF;
  }
  
  .prompt-actions__button-content {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 24px;
    gap: 4px;
    padding: 0 4px 0 2px;
  }
  
  .prompt-actions__button-content--delete {
    width: 145px;
    color: #6D787F;
  }
  
  .prompt-actions__buttons-group {
    display: flex;
    align-items: center;
    gap: 10px;
    height: 40px;
    width: 313px;
  }
  
  .prompt-actions__buttons-group--content {
    width: 386px;
  }
  
  .prompt-actions__icon {
    width: 24px;
    height: 24px;
    flex-shrink: 0;
  }
  
  .prompt-actions__text {
    text-align: center;
    font-size: 16px;
    font-weight: 600;
    line-height: 16px;
    letter-spacing: 0.128px;
    height: 11px;
    flex-shrink: 0;
    margin: 0;
  }
