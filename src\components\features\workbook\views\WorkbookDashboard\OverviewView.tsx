import React, { useMemo } from 'react';
import { useWorkbook } from '../../WorkbookContext';
import { useWorkbookDashboard } from '../../hooks/useWorkbookDashboard';
import { useThemeStyles } from '@/hooks/useThemeStyles';
import { PromptInput } from '@/components/features/chat';
import GreetingSection from '../../components/dashboard/GreetingSection';
import InputLowerTray from '../../components/dashboard/InputLowerTray';
import FileArea from '../../components/dashboard/FileArea';

const OverviewView: React.FC = () => {
  const { currentWorkbook } = useWorkbook();
  const { classes, colors } = useThemeStyles();

  const styles = {
    container: 'flex flex-col items-center justify-center w-full h-full',
    contentArea: 'flex flex-col items-center w-full',
    inputContainer: `
      flex w-[800px] h-[160px] p-[24px] 
      flex-col justify-between items-center 
      rounded-[32px] border border-[${colors.border}] 
      ${classes.backgroundInput}
    `,
  };

  const handleSendMessage = useMemo(() => (msg: string) => console.log('Send message:', msg), []);

  const { message, handleInputChange, handleSubmit, handleDeleteFile } = useWorkbookDashboard({
    initialFiles: currentWorkbook?.files || [],
    onSendMessage: handleSendMessage,
  });

  if (!currentWorkbook) return null;

  return (
    <div className={styles.container}>
      {/* greeting section */}
      <GreetingSection textColor={classes.text} />

      {/* content area */}
      <div className={styles.contentArea}></div>

      {/* input area */}
      <div className={styles.inputContainer}>
        <PromptInput message={message} onChange={handleInputChange} onSubmit={handleSubmit} />

        <InputLowerTray textColor={classes.text} onSubmit={handleSubmit} />
      </div>

      {/* file area */}
      <FileArea files={currentWorkbook.files} onDelete={handleDeleteFile} />
    </div>
  );
};

export default React.memo(OverviewView);
