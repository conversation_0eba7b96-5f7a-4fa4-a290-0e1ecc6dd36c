import { API_BASE_PATH } from './apiConfig';
import { parseApiError } from './apiUtils';

export async function sendFeedback(sessionId: string, responseId: string, likeOrDislike: 1 | 0): Promise<void> {
  const body = {
    id: responseId,
    info: likeOrDislike,
    sessionId: sessionId,
  };

  const feedbackEndpoint = '/restful/feedback';
  const url = `${API_BASE_PATH}${feedbackEndpoint}`;

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });
    if (!response.ok) {
      let errorData: any = { message: `HTTP error! Status: ${response.status}` };
      try {
        errorData = await response.json();
      } catch (jsonError) {
        console.warn('Could not parse error response as JSON:', jsonError);
        const fallbackError = new Error(response.statusText || `HTTP error! Status: ${response.status}`);
        const parsedFallback = parseApiError(fallbackError);
        throw new Error(parsedFallback.message);
      }
      const parsedError = parseApiError(errorData);
      throw new Error(parsedError.message);
    }

    console.log('Feedback sent successfully (API level).');
    return;
  } catch (error: unknown) {
    const parsedError = parseApiError(error);
    console.error(
      'Failed to send feedback (fetch or processing error):',
      { responseId, likeOrDislike },
      parsedError.originalError || error
    );
    throw new Error(`${parsedError.message} Please try again.`);
  }
}
