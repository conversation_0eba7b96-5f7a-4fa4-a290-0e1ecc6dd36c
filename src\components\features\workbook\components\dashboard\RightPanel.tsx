import React, { useState, useMemo } from 'react';
import { LuSearch, LuFileText, /*LuDownload,*/ LuTrash2 } from 'react-icons/lu';

import { useThemeStyles } from '@hooks/useThemeStyles';
import {
  RightPanelProps,
  SearchBarProps,
  FileListProps,
  FileListItemProps,
} from '@/components/features/workbook/workbookTypes';


const DEFAULT_PLACEHOLDER = 'Search my files...';
const PANEL_TITLE = 'Workbook Files';

// SearchBar component 
const SearchBar: React.FC<SearchBarProps> = ({ placeholder, value, onChange, onFocus, onBlur }) => {
  const { classes, colors } = useThemeStyles();

  const searchBarStyles = {
    container: `
      flex 
      h-[36px] 
      p-[8px_12px] 
      justify-center 
      items-center 
      gap-[10px] 
      self-stretch 
      rounded-[999px] 
      border 
      border-[${colors.border}] 
      ${classes.backgroundInput}
      ${classes.focusRing}
    `,
    icon: `w-[16px] h-[16px] aspect-[1/1] ${classes.text}`,
    input: `
      flex-1 
      bg-transparent 
      border-none 
      outline-none 
      ${classes.text} 
      ${classes.placeholder}
      text-sm
    `
  };

  return (
    <div className={searchBarStyles.container}>
      <LuSearch className={searchBarStyles.icon} />
      <input
        type="text"
        className={searchBarStyles.input}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        onFocus={onFocus}
        onBlur={onBlur}
      />
    </div>
  );
};

// FileListItem component 
const FileListItem: React.FC<FileListItemProps> = ({ fileName, onDelete, /*onDownload*/ }) => {
  const { classes } = useThemeStyles();

  const fileItemStyles = {
    container: `flex h-[32px] p-[8px] items-center gap-[10px] self-stretch ${classes.backgroundInput} rounded-[4px] ${classes.hoverBackground} transition-colors`,
    fileIcon: "w-[16px] h-[16px] flex-shrink-0 aspect-[1/1]",
    fileName: `flex-1 overflow-hidden ${classes.text} font-roboto text-[12px] font-normal leading-[130%] tracking-[0.096px] text-ellipsis whitespace-nowrap`,
    actionButton: `w-[16px] h-[16px] cursor-pointer ${classes.text}`,
    // downloadButton: `w-[16px] h-[16px] cursor-pointer ${classes.text} hover:text-blue-400`,
    deleteButton: `w-[16px] h-[16px] cursor-pointer ${classes.text} hover:text-red-400`,
    buttonIcon: "w-full h-full aspect-[1/1]"
  };

  return (
    <div className={fileItemStyles.container}>
      <LuFileText className={fileItemStyles.fileIcon} color="#F7987D" />
      <span className={fileItemStyles.fileName}>
        {fileName}
      </span>
      {/* {onDownload && (
        <div
          className={fileItemStyles.downloadButton}
          onClick={onDownload}
          role="button"
          aria-label={`Download ${fileName}`}
        >
          <LuDownload className={fileItemStyles.buttonIcon} />
        </div>
      )} */}
      {onDelete && (
        <div
          className={fileItemStyles.deleteButton}
          onClick={onDelete}
          role="button"
          aria-label={`Delete ${fileName}`}
        >
          <LuTrash2 className={fileItemStyles.buttonIcon} />
        </div>
      )}
    </div>
  );
};

// FileList component 
const FileList: React.FC<FileListProps> = ({ files, onDelete, /*onDownload*/ }) => {
  const fileListStyles = {
    container: "flex flex-col gap-[8px] self-stretch"
  };

  return (
    <div className={fileListStyles.container}>
      {files.map((fileName, index) => (
        <FileListItem
          key={index}
          fileName={fileName}
          onDelete={onDelete ? () => onDelete(index) : undefined}
          // onDownload={onDownload ? () => onDownload(index) : undefined}
        />
      ))}
    </div>
  );
};

// Main RightPanel component
const RightPanel: React.FC<RightPanelProps> = ({ files, onDelete, /*onDownload*/ }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [placeholder, setPlaceholder] = useState(DEFAULT_PLACEHOLDER);
  const { classes } = useThemeStyles();

  const panelStyles = {
    container: "flex flex-col items-start gap-[10px] p-[36px_24px_16px_24px] h-full w-full",
    contentWrapper: "flex flex-col items-start gap-[24px] flex-1 self-stretch",
    title: `${classes.text} font-['Sofia_Pro'] text-[26px] font-bold leading-normal tracking-[0.208px]`
  };

  const filteredFiles = useMemo(() => 
    files.filter(file => file.toLowerCase().includes(searchQuery.toLowerCase())),
    [files, searchQuery]
  );

  const handleSearchFocus = () => {
    setPlaceholder('');
  };

  const handleSearchBlur = () => {
    if (searchQuery === '') {
      setPlaceholder(DEFAULT_PLACEHOLDER);
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  return (
    <div className={panelStyles.container}>
      <div className={panelStyles.contentWrapper}>
        {/* Title */}
        <h2 className={panelStyles.title}>
          {PANEL_TITLE}
        </h2>

        {/* Search Bar */}
        <SearchBar
          placeholder={placeholder}
          value={searchQuery}
          onChange={handleSearchChange}
          onFocus={handleSearchFocus}
          onBlur={handleSearchBlur}
        />

        {/* File List */}
        <FileList files={filteredFiles} onDelete={onDelete} /*onDownload={onDownload} *//>
      </div>
    </div>
  );
};

export default RightPanel;
