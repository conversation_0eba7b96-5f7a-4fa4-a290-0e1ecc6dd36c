import React, { useState } from 'react';
import { ValidatedFile } from '@/types/fileUpload';
import { MdClose, MdErrorOutline, MdCheckCircleOutline, MdCancel } from 'react-icons/md';
import { AiOutlineLoading } from 'react-icons/ai';
import { getFileIcon, displaySize } from '@/components/features/chat/components/fileUpload/utils/fileDisplayUtils';
import clsx from 'clsx';

interface SelectedFileItemProps {
  file: ValidatedFile;
  onRemove: (fileId: string) => void;
}

const SelectedFileItem: React.FC<SelectedFileItemProps> = ({ file, onRemove }) => {
  const [isRemoving, setIsRemoving] = useState(false);

  const handleRemove = async () => {
    if (isRemoving) return; // prevent duplicate clicks

    setIsRemoving(true);
    try {
      await onRemove(file.id);
    } finally {
      // delay reset state to give UI time to update
      setTimeout(() => setIsRemoving(false), 500);
    }
  };

  const hasValidationError = !!file.validationError;
  const isUploading = file.uploadStatus === 'uploading';
  const isCancelling = file.uploadStatus === 'cancelling';
  const isCancelled = file.uploadStatus === 'cancelled';
  const isDeleting = file.uploadStatus === 'deleting';

  if (file.uploadStatus === 'deleting') {
    console.log(`[SelectedFileItem] File ${file.id} is in deleting state`);
  }

  const renderStatusIcon = () => {
    if (hasValidationError)
      return <MdErrorOutline className="w-5 h-5 text-red-500 flex-shrink-0" title={file.validationError} />;
    if (isDeleting)
      return <AiOutlineLoading className="w-5 h-5 text-red-500 animate-spin flex-shrink-0" title="Deleting..." />;
    if (isCancelling)
      return <AiOutlineLoading className="w-5 h-5 text-orange-500 animate-spin flex-shrink-0" title="Cancelling..." />;
    if (isCancelled) return <MdCancel className="w-5 h-5 text-orange-500 flex-shrink-0" title="Upload cancelled" />;
    if (isUploading)
      return <AiOutlineLoading className="w-5 h-5 text-blue-500 animate-spin flex-shrink-0" title="Uploading..." />;
    if (file.uploadStatus === 'success')
      return <MdCheckCircleOutline className="w-5 h-5 text-green-500 flex-shrink-0" title="Uploaded successfully" />;
    if (file.uploadStatus === 'error')
      return (
        <MdErrorOutline className="w-5 h-5 text-red-500 flex-shrink-0" title={file.uploadError || 'Upload failed'} />
      );
    return null;
  };

  const getRemoveButtonIcon = () => {
    if (isRemoving) {
      return <AiOutlineLoading className="w-5 h-5 animate-spin" />;
    }
    if (isUploading || isCancelling || isDeleting) {
      return <MdCancel className="w-5 h-5" />;
    }
    return <MdClose className="w-5 h-5" />;
  };

  const getRemoveButtonTitle = () => {
    if (isRemoving) return 'Removing...';
    if (isDeleting) return 'Deleting file...';
    if (isUploading) return `Cancel upload for ${file.name}`;
    if (isCancelling) return 'Cancelling upload...';
    return `Remove ${file.name}`;
  };

  const getStatusText = () => {
    if (hasValidationError) return file.validationError;
    if (file.uploadStatus === 'error') return file.uploadError || 'Upload failed';
    if (isDeleting) return 'Deleting...';
    if (isCancelling) return 'Cancelling...';
    if (isCancelled) return 'Upload cancelled';
    if (isUploading) return 'Uploading...';
    return displaySize(file.size);
  };

  const block = 'selected-file-item';

  return (
    <div
      className={clsx(
        block,
        'flex items-center justify-between p-2 my-1 rounded-md border',
        {
          [`${block}--validation-error`]: hasValidationError,
          [`${block}--upload-error`]: file.uploadStatus === 'error' && !hasValidationError,
          [`${block}--success`]: file.uploadStatus === 'success',
          [`${block}--uploading`]: isUploading,
          [`${block}--cancelling`]: isCancelling,
          [`${block}--cancelled`]: isCancelled,
          [`${block}--deleting`]: isDeleting,
        },
        hasValidationError || file.uploadStatus === 'error'
          ? 'border-red-300 bg-red-50 dark:border-red-700 dark:bg-red-900/30'
          : file.uploadStatus === 'success'
            ? 'border-green-300 bg-green-50 dark:border-green-700 dark:bg-green-900/30'
            : isDeleting
              ? 'border-red-300 bg-red-50 dark:border-red-700 dark:bg-red-900/30'
              : isCancelling || isCancelled
                ? 'border-orange-300 bg-orange-50 dark:border-orange-700 dark:bg-orange-900/30'
                : 'border-gray-200 bg-white dark:border-gray-600 dark:bg-slate-700',
        isUploading || isCancelling || isDeleting ? 'opacity-75' : ''
      )}
    >
      <div className={`${block}__main-content flex items-center overflow-hidden flex-grow`}>
        <div className={`${block}__icon-container w-8 h-8 flex items-center justify-center mr-2 flex-shrink-0`}>
          {getFileIcon(file.type)}
        </div>

        <div className={`${block}__info flex-grow overflow-hidden mr-2`}>
          <p
            className={clsx(
              `${block}__name`,
              'text-sm font-medium truncate',
              hasValidationError || file.uploadStatus === 'error'
                ? 'text-red-700 dark:text-red-300'
                : isDeleting
                  ? 'text-red-700 dark:text-red-300'
                  : isCancelling || isCancelled
                    ? 'text-orange-700 dark:text-orange-300'
                    : 'text-gray-800 dark:text-gray-100'
            )}
            title={file.name || 'Unknown File'}
          >
            {file.name || 'Unknown File'}
          </p>
          <p
            className={clsx(
              `${block}__meta`,
              'text-xs',
              hasValidationError || file.uploadStatus === 'error'
                ? 'text-red-600 dark:text-red-400'
                : isDeleting
                  ? 'text-red-600 dark:text-red-400'
                  : isCancelling || isCancelled
                    ? 'text-orange-600 dark:text-orange-400'
                    : 'text-gray-500 dark:text-gray-400'
            )}
          >
            {getStatusText()}
          </p>
        </div>
      </div>

      <div className={`${block}__actions flex items-center flex-shrink-0`}>
        <div className={`${block}__status-icon-wrapper w-5 h-5 mr-2`}>{renderStatusIcon()}</div>
        <button
          onClick={handleRemove}
          disabled={isRemoving || isCancelling || isDeleting}
          className={clsx(
            `${block}__remove-button`,
            'p-1 rounded-full focus:outline-none focus:ring-1 focus:ring-gray-400 flex-shrink-0 transition-colors',
            {
              'cursor-not-allowed opacity-50': isRemoving || isCancelling || isDeleting,
              'hover:bg-gray-200 dark:hover:bg-gray-600': !isRemoving && !isCancelling && !isUploading && !isDeleting,
              'hover:bg-red-200 dark:hover:bg-red-700/50 focus:ring-red-400':
                (isUploading || isCancelled || isDeleting) && !isRemoving && !isCancelling && !isDeleting,
            }
          )}
          aria-label={getRemoveButtonTitle()}
          title={getRemoveButtonTitle()}
        >
          <div
            className={clsx(
              `${block}__remove-icon`,
              hasValidationError || file.uploadStatus === 'error'
                ? 'text-red-500 dark:text-red-400'
                : isDeleting
                  ? 'text-red-500 dark:text-red-400'
                  : isUploading || isCancelling || isCancelled
                    ? 'text-orange-500 dark:text-orange-400'
                    : 'text-gray-600 dark:text-gray-300'
            )}
          >
            {getRemoveButtonIcon()}
          </div>
        </button>
      </div>
    </div>
  );
};

export default SelectedFileItem;
