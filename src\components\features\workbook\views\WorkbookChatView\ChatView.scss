.chat-messages-container {
    height: 62vh;
    width: 100%;
    flex: 1 1 0%;
    min-height: 0;
    overflow-y: auto;
    overflow-x: hidden;
    padding-left: 1rem;
    padding-right: 1rem;
    padding-bottom: 1rem;
    overscroll-behavior: none;

    &::-webkit-scrollbar {
        width: 6px;
    }
    &::-webkit-scrollbar-track {
        border-radius: 9999px;
    }
    &::-webkit-scrollbar-thumb {
        border-radius: 4px;
        // 默认样式（浅色模式）
        background: #e5e7eb; // 比如浅灰，按需调整
        border: 2px solid transparent; // 为了有空间显示 border
    }

    // 深色模式下滚动条滑块颜色和边框
    @media (prefers-color-scheme: dark) {
        &::-webkit-scrollbar-thumb {
            background: #fff !important; // 背景白色
            border: 2px solid #0066B1 !important; // 蓝色边框
        }
    }
}
