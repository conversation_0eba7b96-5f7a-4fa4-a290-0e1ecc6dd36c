import { client } from './client';
import { API_BASE_PATH } from './apiConfig';
import { parseApiError } from './apiUtils';

export interface PolicyWebQueryPayload {
  location: string;
  user_input: string;
  session_id: string | null;
}

export interface PolicyRecord {
  Content_ID: string;
  Title: string;
  Applicability: string;
  Responsible_Area: string;
  Content_Type: string;
  Owner: string;
  Overview_Statement: string;
  Last_Approved_Date: string;
  Effective_Date: string;
  PolicyWeb_Link: string;
  // Relevance_Score?: number;
}

export interface PolicyWebResponseData {
  policies: PolicyRecord[];
  qa_response: string;
}

export interface PolicyWebApiResponse {
  response_success: boolean;
  response?: PolicyWebResponseData;
  session_id?: string;
  prompt_id?: string;
  response_class?: any;
  message?: string;
}

export const sendPolicyQuery = async (
  payload: PolicyWebQueryPayload
): Promise<PolicyWebApiResponse> => {
  const endpoint = `${API_BASE_PATH}/restful/query_policies`;

  try {
    // console.log(`Sending policy query to endpoint: ${endpoint} with payload:`, payload);
    const response = await client.post<PolicyWebApiResponse>(endpoint, payload);

    return response.data;
  } catch (error: unknown) {
    const parsedError = parseApiError(error);
    console.error(`Error sending policy query to ${endpoint}:`, parsedError.originalError || error);

    if (error && typeof (error as PolicyWebApiResponse).response_success === 'boolean') {
      console.warn(
        'Returning structured error from backend/client.ts rejection for policy query:',
        error
      );
      return error as PolicyWebApiResponse;
    }

    return {
      response_success: false,
      message: parsedError.message || 'An unexpected error occurred while querying PolicyWeb.',
    };
  }
};

export const sendPolicyWebFeedback = async (
  sessionId: string,
  responseId: string,
  likeOrDislike: 1 | 0,
  responseClass?: any
): Promise<void> => {
  const formattedId = `${responseId}-0`;

  const body = {
    id: formattedId,
    info: likeOrDislike,
    sessionId: sessionId,
    ...(responseClass && { response_class: responseClass }),
  };

  const feedbackEndpoint = '/restful/feedback';
  const url = `${API_BASE_PATH}${feedbackEndpoint}`;

  try {
    const headers = { 'Content-Type': 'application/json' };

    const response = await window.fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const contentType = response.headers.get('content-type');
    const responseText = await response.text();
    const hasContent = responseText.trim().length > 0;

    console.log('PolicyWeb feedback sent successfully (API level).');

    if (hasContent && contentType && contentType.includes('application/json')) {
      try {
        const responseData = JSON.parse(responseText);
        console.log('PolicyWeb feedback response:', responseData);
      } catch (jsonError) {
        console.log('Response has content but is not valid JSON:', responseText);
      }
    }

    return;
  } catch (error: unknown) {
    const parsedError = parseApiError(error);
    console.error(
      'Failed to send PolicyWeb feedback:',
      { responseId, likeOrDislike, sessionId },
      parsedError.originalError || error
    );
    throw new Error(`${parsedError.message} Please try again.`);
  }
};
