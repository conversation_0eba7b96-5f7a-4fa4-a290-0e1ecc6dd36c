from typing import Optional, Type, Any, Tu<PERSON>, TypeVar, Annotated
from copy import deepcopy

from pydantic import BaseModel, create_model, Field, StringConstraints, Extra
from pydantic.fields import FieldInfo

BaseModelT = TypeVar('BaseModelT', bound=BaseModel)

def partial_model(model: Type[BaseModelT], *optional_fields) -> Type[BaseModelT]:
#     """Decorator function used to modify a pydantic model's fields to all be optional.
#     Alternatively, you can  also pass the field names that should be made optional as arguments
#     to the decorator.
#     Taken from https://github.com/pydantic/pydantic/issues/3120#issuecomment-1528030416, which 
#     references an ad-hoc solution suggested by a pydantic maintainer
#     """  
    def make_field_optional(field: FieldInfo, default: Any = None) -> Tuple[Any, FieldInfo]:
        new = deepcopy(field)
        new.default = default
        new.annotation = Optional[field.annotation]  # type: ignore
        return (new.annotation, new)
    
    return create_model(
        f'Partial{model.__name__}{"".join(field.capitalize() for field in optional_fields)}',
        __base__=model,
        __module__=model.__module__,
        **{
            field_name: make_field_optional(field_info)
            if field_name in optional_fields else (field_info.annotation, field_info)
            for field_name, field_info in model.model_fields.items()
        }
    )

# if __name__ == "__main__":
#     class X(BaseModel):
#         id: str
#         name: Annotated[str, StringConstraints(strict=True, strip_whitespace=True, to_lower=True, min_length=1, max_length=16)]
#         num_thing: Annotated[int, Field(strict=True, ge=0)]
#         clear_description: bool = False

#         class Config:
#             extra = Extra.allow

#     PartialX = partial_model(X, "name")
#     PartialX = create_model('XX', __base__=PartialX, __module__=PartialX.__module__, **{"is_thing" : (bool, FieldInfo(annotation=bool, default=False))})

#     opt_x = PartialX(id='id', name='one two t', num_thing=1)
#     print(opt_x)
#     print('----------')
#     print(PartialX.model_fields)
#     print(X.model_fields)
