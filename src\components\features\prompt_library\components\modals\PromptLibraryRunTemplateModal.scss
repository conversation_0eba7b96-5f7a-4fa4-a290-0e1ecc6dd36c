.promptLibraryRunTemplateModal__backdrop {
    position: fixed;
    inset: 0;
    z-index: 30;
    background: transparent;
    background-color: #000E1A;
    opacity: 0.9;
    display: flex;
    justify-content: center;
    align-items: center;
}

.promptLibraryRunTemplateModal__popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 50;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    width: 800px;
    height: 354px;
    color: #FFFFFF;
    border-radius: 32px;
    background-color: #003963;
    outline: none;
    padding: var(--spacing-spacing-m-3, 24px) var(--spacing-spacing-l-1, 32px);
    gap: var(--spacing-spacing-m-2, 16px);
    .promptLibraryRunTemplateModal__popup__header {
        width: 736px;
        height: 48px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        align-self: stretch;
        .promptLibraryRunTemplateModal__popup__header__text {
            font-family: var(--font-sofia-pro);
            font-size: 24px;
            font-style: normal;
            font-weight: 500;
            line-height: 31.2px;
            letter-spacing: 0.192px;
            width: 574px;
            height: 31px;
            overflow-x: auto;
        }
        .promptLibraryRunTemplateModal__popup__header__close-button {
            height: 48px;
            width: 48px;
            cursor: pointer;
        }
    }
    .promptLibraryRunTemplateModal__popup__prompt-content {
        width: 736px;
        height: 186px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        align-self: stretch;
        padding: var(--spacing-spacing-m-2, 16px);
        border-radius: 16px;
        border: 1px solid #0066B1;
        .promptLibraryRunTemplateModal__popup__prompt-content__text {
          resize: none;
          outline: none;
          width: 704px;
          height: 154px;
          font-family: var(--font-roboto);
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 25.6px;
          letter-spacing: 0.128px;
        }
    }
    .promptLibraryRunTemplateModal__popup__action-container {
        width: 736px;
        height: 40px;
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        align-self: stretch;
        .promptLibraryRunTemplateModal__popup__action-container__action-button {
            width: 145px;
            height: 40px;
            display: flex;
            align-items: center;
            padding: var(--spacing-spacing-sm-3, 8px) var(--spacing-spacing-m-1, 12px);
            gap: var(--spacing-spacing-sm-3, 8px);
            border-radius: 50px;
            border: 1px solid #0066B1;
            outline: none;
            white-space: nowrap;
            cursor: pointer;
            .promptLibraryRunTemplateModal__popup__action-container__action-button__content-container {
                width: 123px;
                height: 24px;
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 0px var(--Spacing-spacing-sm-2, 4px) 0px var(--Spacing-spacing-sm-1, 2px);
                gap: var(--spacing-spacing-sm-2, 4px);
                .promptLibraryRunTemplateModal__popup__action-container__action-button__content-run-icon {
                    width: 24px;
                    height: 24px;
                    flex-shrink: 0;
                }
                .promptLibraryRunTemplateModal__popup__action-container__action-button__content-run-text {
                    width: 97px;
                    height: 13px;
                    text-align: center;
                    font-family: var(--font-roboto);
                    font-size: 16px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 16px;
                    flex-shrink: 0;
                    letter-spacing: 0.128px;
                }
            }
        }
    }
}
