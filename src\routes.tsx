import { createBrowser<PERSON>outer, Navigate, useLocation } from 'react-router-dom';
import { FaHistory } from 'react-icons/fa';

import MainLayout from './layouts/MainLayout';
import { WorkbookLayout, WorkbookListView, WorkbookDetailsView } from '@/components/features/workbook';

import { NotFoundPage } from './components/common';

import { ChatContainer } from '@/components/features/chat';
import { ChatHistoryContainer } from '@/components/features/chathistory';
import { ChatTypeRadioGroup } from './components/features/chat/components';

import {
  SidekickChatIcon,
  SidekickWorkbookIcon,
  SidekickPromptLibraryIcon,
  SidekickTextToSpeechIcon,
  SidekickTranslateIcon,
  SidekickResourcesIcon,
} from './components/common/icons';

const ChatContainerWithKey = () => {
  const location = useLocation();
  const containerKey = location.state?.fromNewChat
    ? 'new-chat-' + Date.now() 
    : location.pathname; 

  return <ChatContainer key={containerKey} />;
};

import {
  PromptLibraryLayout,
  PromptLibraryView
} from '@/components/features/prompt_library';

import { TextToSpeechView } from '@/components/features/text_to_speech';
import { TranslateView } from '@/components/features/translate';
import ResourcesView from '@/components/features/resources/components/ResourcesView';


let basePath = '/sidekick';
const currentPath = window.location.pathname;
const pathPrefix = currentPath.split(basePath)[0];
basePath = `${pathPrefix}${basePath}`;

const routes = createBrowserRouter(
  [
    {
      path: '/',
      element: <MainLayout />,
      handle: { topBarTitle: "Untitled Page" }, 
      children: [
        {
          index: true,
          element: <Navigate to="chat" replace />,
        },
        {
          path: 'chat/:sessionId?',
          element: <ChatContainerWithKey />,
          handle: { topBarTitle: 'Chat', topBarIcon: <SidekickChatIcon />, topBarContent: <ChatTypeRadioGroup initialValue='General' /> },
        },
        {
          path: 'code/:sessionId?',
          element: <ChatContainerWithKey />,
          handle: { topBarTitle: 'Chat', topBarIcon: <SidekickChatIcon />, topBarContent: <ChatTypeRadioGroup initialValue='Code' /> },
        },
        {
          path: 'med/:sessionId?',
          element: <ChatContainerWithKey />,
          handle: { topBarTitle: 'Chat', topBarIcon: <SidekickChatIcon />, topBarContent: <ChatTypeRadioGroup initialValue='Medical' /> },
        },
        {
          path: 'policyweb/:sessionId?',
          element: <ChatContainerWithKey />,
          handle: { topBarTitle: 'Chat', topBarIcon: <SidekickChatIcon />, topBarContent: <ChatTypeRadioGroup initialValue='Policy' /> },
        },
        {
          path: 'history',
          element: <ChatHistoryContainer />,
          handle: { topBarTitle: 'Chat History', topBarIcon: <FaHistory className="h-7 w-7" /> },
        },
        {
          path: 'prompt_library',
          element: <PromptLibraryLayout />, 
          handle: { topBarTitle: "Prompt Library", topBarIcon: <SidekickPromptLibraryIcon /> },
          children: [
            {
              index: true,
              element: <Navigate to='my' replace />,
            },
            {
              path: 'my',
              element: <PromptLibraryView isGlobal={false} />,
              handle: { topBarContent: <div>My Prompts</div> }
            },
            {
              path: 'public',
              element: <PromptLibraryView isGlobal={true} />,
              handle: { topBarContent: <div>Public Prompts</div> },
            }
          ]
        },
        {
          path: 'workbooks',
          element: <WorkbookLayout />,
          handle: { topBarTitle: "Workbook", topBarIcon: <SidekickWorkbookIcon /> },
          children: [
            {
              index: true,
              element: <Navigate to="my" replace />,
            },
            {
              path: 'my',
              element: <WorkbookListView isGlobal={false} />,
              handle: { topBarContent: <div>My Workbooks</div> }
            },
            {
              path: 'my/:workbookId',
              element: <WorkbookDetailsView isGlobal={false} />,
            },
            {
              path: 'public',
              element: <WorkbookListView isGlobal={true} />,
              handle: { topBarContent: <div>Public Workbooks</div> }
            },
            {
              path: 'public/:workbookId',
              element: <WorkbookDetailsView isGlobal={true} />,
            },
          ],
        },
        {
          path: 'texttospeech',
          element: <TextToSpeechView />,
          handle: { topBarTitle: "Text-to-Speech", topBarIcon: <SidekickTextToSpeechIcon /> },
        },
        {
          path:'translate',
          element:<TranslateView/>,
          handle: { topBarTitle: "Translate", topBarIcon: <SidekickTranslateIcon /> },
        },
        {
          path: 'resources',
          element: <ResourcesView />,
          handle: { topBarTitle: 'Resources', topBarIcon: <SidekickResourcesIcon /> },
        },
        {
          path: '*',
          element: <NotFoundPage />,
          handle: {
            passLayoutWidgetsAsProps: true,
          },
        },
      ],
    },
  ],
  {
    basename: basePath,
  }
);

export default routes;
