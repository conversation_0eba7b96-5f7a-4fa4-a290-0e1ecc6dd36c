import { themeClasses } from '@/config/theme';

/**
 * Utility functions for getting theme-based styles dynamically.
 */
export const getTextColor = (
  isDarkMode: boolean,
  lightColor?: string,
  darkColor?: string
): string => {
  if (lightColor && darkColor) {
    return isDarkMode ? darkColor : lightColor;
  }
  return themeClasses.text(isDarkMode);
};

export const getBackgroundColor = (
  isDarkMode: boolean,
  lightColor?: string,
  darkColor?: string
): string => {
  if (lightColor && darkColor) {
    return isDarkMode ? darkColor : lightColor;
  }
  return themeClasses.background(isDarkMode);
};

export const getSidebarBackgroundColor = (isDarkMode: boolean): string =>
  themeClasses.backgroundSidebar(isDarkMode);

export const getInputBackgroundColor = (isDarkMode: boolean): string =>
  themeClasses.backgroundInput(isDarkMode);

export const getPlaceholderColor = (isDarkMode: boolean): string =>
  themeClasses.placeholder(isDarkMode);

export const getHoverBackgroundColor = (isDarkMode: boolean): string =>
  themeClasses.hoverBackground(isDarkMode);
