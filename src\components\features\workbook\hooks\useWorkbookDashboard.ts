import { useState, useCallback, useEffect } from 'react';
import { UseWorkbookDashboardProps } from '@features/workbook/workbookTypes';

export const useWorkbookDashboard = ({
  initialFiles = [],
  onSendMessage,
  onDeleteFile,
}: UseWorkbookDashboardProps = {}) => {
  const [message, setMessage] = useState('');
  const [files, setFiles] = useState<string[]>(initialFiles);

  // update files when initialFiles changes
  useEffect(() => {
    if (initialFiles.length > 0) {
      setFiles(initialFiles);
    }
  }, [initialFiles]);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);
  }, []);

  const handleSubmit = useCallback(() => {
    if (message.trim() && onSendMessage) {
      onSendMessage(message);
      setMessage('');
    }
  }, [message, onSendMessage]);

  const handleDeleteFile = useCallback(
    (index: number) => {
      if (onDeleteFile) {
        onDeleteFile(index);
      } else {
        setFiles(prevFiles => prevFiles.filter((_, i) => i !== index));
      }
    },
    [onDeleteFile]
  );

  return {
    message,
    files,
    setFiles,
    handleInputChange,
    handleSubmit,
    handleDeleteFile,
  };
}; 
