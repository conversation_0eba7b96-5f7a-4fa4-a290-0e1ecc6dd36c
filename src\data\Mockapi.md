# Mock API Documentation

This folder contains mock API implementations for frontend development and testing before the real backend is ready.

## Mock API Files

- `mockAPI.ts` - Mock implementation of the AI conversation API
- `mockWorkbooks.ts` - Mock data for workbooks

## Conversation API

The conversation API simulates the interaction with an AI assistant in the context of a workbook.

### Types

```typescript
// User question request
interface UserQuestion {
  workbook_id: string;
  message: string;
}

// Initial conversation response
interface ConversationResponse {
  workbook_id: string;
  workbook_session_id: string;
  user_id: string;
  conversation_role: 'model' | 'user';
  conversation_turn: number;
  state: 'STATE_UNSPECIFIED' | 'IN_PROGRESS' | 'FAILED' | 'SUCCEEDED';
}

// AI answer with citations
interface AIAnswer extends ConversationResponse {
  answer: {
    raw: string;
    created_utc: string;
    citations: RetrievalCitation[];
  };
}
```

### API Endpoints

1. `askQuestion(request: UserQuestion): Promise<ConversationResponse>`
   - Simulates sending a user question to the AI
   - Returns a response with a workbook session ID and conversation turn number

2. `getAnswer(workbookId: string, turn: number): Promise<AIAnswer>`
   - Retrieves the AI's answer for a specific conversation turn
   - Returns a complete answer with text and citations

### Using the Mock API

The easiest way to use the mock API is through the `useConversation` hook:

```typescript
import useConversation from '@/hooks/useConversation';

// In your component
const { loading, error, conversation, sendMessage } = useConversation({ 
  workbookId: 'your-workbook-id' 
});

// Send a message
await sendMessage('What is machine learning?');

// Access conversation history
conversation.forEach(message => {
  console.log(message.answer.raw);
});
```

### Example Component

Check out `src/components/features/conversation/Conversation.tsx` for a complete implementation example.

### Demo Page

Visit `/conversation/1` in the browser to see the mock API in action.

## Notes

- The mock API includes 5 predefined AI responses that will be randomly selected
- Each response includes citations with source references
- A simulated network delay is included to mimic realistic loading states
- Session management is simulated to maintain conversation context 





这个是当用户发送问题给AI

POST /workbooks/personal/<workbook_id>/ask (might change name) response should look like:
 

JSON
{
    workbook_id: string,
    workbook_session_id: string,
    user_id: string,
    conversation_role: 'model',
    conversation_turn: int
    state: 'STATE_UNSPECIFIED' | 'IN_PROGRESS' | 'FAILED' | 'SUCCEEDED'
}



这个是ai的回复response

where the workbook_id and workbook_session_id are used (user sent in cookie) to something like
 
GET /workbooks/personal/<workbook_id>/ask/<conversation_turn>/answer

{
    workbook_id: string,
    workbook_session_id: string,
    user_id: string,
    conversation_role: 'model',
    conversation_turn: int
    state: 'STATE_UNSPECIFIED' | 'IN_PROGRESS' | 'FAILED' | 'SUCCEEDED'
    answer: {
        raw: string, // pre-rendered text
        created_utc: string // ISO datetime string
        citations: RetrievalCitation[]
    }
}



where RetrievalCitation looks like

class RetrievalCitation(BaseModel):
    start_index: Annotated[int, Field(strict=True, ge=0)] # index in the answer at which the citation starts
    end_index: Annotated[int, Field(strict=True, gt=0)] # index in the answer at which the citation ends (place a superscript there or something)
    citation_sources: List[RetrievalCitationSource] = Field(default_factory=list)

## Derived from https://cloud.google.com/python/docs/reference/discoveryengine/0.11.14/google.cloud.discoveryengine_v1.types.Answer.Reference.ChunkInfo
class RetrievalCitationSource(BaseModel):
    file_id: str
    name: str
    gcs_path: str
    chunk_content: str
    page_identifier: str
    relevance_score: float




if youre wondering why we have start_index as well as end_index in the RetrievalCitation, its in case we want to render the substring text as highlighted between start_index and end_index 




{
    response_text: "You asked about exotic cats, here is what I know about that particular species. It likes to live 
        in desert* blah blah blah blah blah",
    citations: [
        {
            superscript: 1,
            start_index: 20,
            end_index: 21,
            chunk_info: "some excerpt from the original document that supports this part of the answer",
            document_info: {
                page_number: whatever

            },
        },
        {

        }
    ]
}
 