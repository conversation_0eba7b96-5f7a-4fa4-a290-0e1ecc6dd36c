import React from 'react';
import { Slider, Input } from '@base-ui-components/react';
import './SidekickValueSlider.scss';

import { SidekickValueSliderProps } from '@/types/common';

const SidekickValueSlider: React.FC<SidekickValueSliderProps> = ({
  title,
  min = 0,
  max = 10,
  value = 5,
  step,
  disabled = false,
  onChange,
}) => {
  return (
    <div className="sidekick-value-slider__container">
      <span className="sidekick-value-slider__label">{title}</span>
      <Slider.Root
        value={value}
        min={min}
        max={max}
        step={step}
        onValueChange={onChange}
        disabled={disabled}
      >
        <Slider.Control
          className={`sidekick-value-slider__control ${disabled ? 'sidekick-value-slider__control--disabled' : ''}`}
        >
          <Slider.Track
            className={`sidekick-value-slider__track ${disabled ? 'sidekick-value-slider__track--disabled' : ''}`}
          >
            <Slider.Indicator
              className={`sidekick-value-slider__indicator ${disabled ? 'sidekick-value-slider__indicator--disabled' : ''}`}
            />
            <Slider.Thumb
              className={`sidekick-value-slider__thumb ${disabled ? 'sidekick-value-slider__thumb--disabled' : ''}`}
            />
          </Slider.Track>
        </Slider.Control>
      </Slider.Root>
      <Input
        className="sidekick-value-slider__label-display"
        value={value}
        type="number"
        min={min}
        max={max}
        step={step}
        disabled={false}
        onChange={e => onChange(Number(e.target.value))}
      />
    </div>
  );
};

export default SidekickValueSlider;
