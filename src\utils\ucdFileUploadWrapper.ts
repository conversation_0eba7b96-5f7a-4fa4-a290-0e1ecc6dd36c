import { ucdAttestation } from '@/api/ucdAttestationApi';

export const withUCDAttestation = async <T extends any[]>(
  files: File[],
  sessionType: string,
  currentUserEmail: string | null,
  onSuccess: (...args: T) => void | Promise<void>,
  ...args: T
): Promise<boolean> => {
  const isUCD = Boolean(currentUserEmail?.includes('@ucci.com'));

  // Check UCD attestation first
  const userAttested = await ucdAttestation(isUCD, files, sessionType);
  if (!userAttested) {
    return false; // User cancelled attestation or failed UCD check
  }

  // Execute the original function if attestation passes
  await onSuccess(...args);
  return true;
};

/**
 * Simple wrapper for file change events
 */
export const handleFileChangeWithAttestation = async (
  event: React.ChangeEvent<HTMLInputElement>,
  sessionType: string,
  currentUserEmail: string | null,
  onFilesSelected: (files: File[]) => void | Promise<void>
): Promise<boolean> => {
  const files = Array.from(event.currentTarget.files ?? []);

  if (files.length === 0) {
    return false;
  }

  return withUCDAttestation(files, sessionType, currentUserEmail, onFilesSelected, files);
};
