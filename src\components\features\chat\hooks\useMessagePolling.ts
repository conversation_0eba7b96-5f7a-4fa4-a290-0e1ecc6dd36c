import { useEffect, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { v4 as uuidv4 } from 'uuid';
import { pollChatResponse } from '@/api/chatApi';
import { addMessage, setError, selectActivePollingId, ChatMessage } from '@/store/slices/chatSlice';

const POLLING_INTERVAL_MS = 2000;

export const useMessagePolling = () => {
  const dispatch = useDispatch();
  const activePollingId = useSelector(selectActivePollingId);
  const intervalIdRef = useRef<number | null>(null);

  useEffect(() => {
    const clearPollingInterval = () => {
      if (intervalIdRef.current !== null) {
        clearInterval(intervalIdRef.current);
        intervalIdRef.current = null;
      }
    };

    if (activePollingId) {
      clearPollingInterval();

      intervalIdRef.current = setInterval(async () => {
        try {
          const pollResponse = await pollChatResponse(activePollingId);

          if (pollResponse.ready) {
            console.info(`[Polling] Response READY for ${activePollingId}. Content:`, pollResponse.response);
            clearPollingInterval();

            const messageId = (pollResponse as any).response_id;

            if (!messageId || typeof messageId !== 'string' || !messageId.includes('-')) {
              console.error('Invalid or missing response_id for feedback in poll response:', pollResponse);
            }

            const modelMessage: ChatMessage = {
              id: messageId || uuidv4(), // Use backend ID, fallback to UUID if missing
              role: 'model',
              text: pollResponse.response,
              timestamp: new Date().toISOString(),
              feedback: null,
            };
            dispatch(addMessage(modelMessage));
          } else {
            if (pollResponse.response !== null && typeof pollResponse.response === 'string') {
              console.warn(
                `[Polling] Not ready for ${activePollingId}, but received INTERIM message: "${pollResponse.response}"`
              );
            } else {
              console.info(
                `[Polling] Still WAITING for response for ${activePollingId}. Status: { ready: false, response: ${JSON.stringify(pollResponse.response)} }`
              );
            }
          }
        } catch (pollErr) {
          console.error('[Polling] Interval error:', pollErr);
          clearPollingInterval();
          dispatch(setError((pollErr as Error).message || 'Polling failed'));
        }
      }, POLLING_INTERVAL_MS) as unknown as number;
    } else {
      // If activePollingId becomes null (e.g., chat ended, error, or response received), ensure interval is cleared.
      clearPollingInterval();
    }
    return () => {
      clearPollingInterval();
    };
  }, [activePollingId, dispatch]);
};
