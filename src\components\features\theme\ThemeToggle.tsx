import React from 'react';
// import { BiSun, BiM<PERSON> } from 'react-icons/bi';
import { BiSun } from 'react-icons/bi';
import { useThemeStyles } from '@/hooks/useThemeStyles';
import { ThemeToggleProps } from '@/types/theme';

const ThemeToggle: React.FC<ThemeToggleProps> = ({ className = '' }) => {
  // Original functionality code commented out for future restoration
  /*
  const { isDarkMode, toggleTheme, classes } = useThemeStyles();

  return (
    <button
      className={`${classes.circleIconButton} ${className}`}
      onClick={toggleTheme}
      aria-label={isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}
      tabIndex={0}
    >
      {isDarkMode ? <BiSun className="h-7 w-7" /> : <BiMoon className="h-7 w-7" />}
    </button>
  );
  */

  // Temporarily disabled toggle functionality - always dark mode
  const { classes } = useThemeStyles();

  return (
    <button
      className={`${classes.circleIconButton} ${className} opacity-60 cursor-not-allowed`}
      disabled={true}
      aria-label="Dark mode enabled"
      tabIndex={-1}
    >
      <BiSun className="h-7 w-7" />
    </button>
  );
};

export default ThemeToggle;
