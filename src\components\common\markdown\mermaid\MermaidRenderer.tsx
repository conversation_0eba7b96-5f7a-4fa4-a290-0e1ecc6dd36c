import React, { useEffect, useState, useRef, useId, useCallback } from 'react';
import clsx from 'clsx';
import { useMermaidImport } from './hooks/useMermaidImport';
import { useAsyncRender } from './hooks/useAsyncRender';
import MermaidModal from './MermaidModal';

export interface MermaidRendererProps {
  code: string;
}

// Global Mermaid configuration to prevent layout disruption
let mermaidInitialized = false;

const initializeMermaid = (mermaidModule: any) => {
  if (!mermaidInitialized && mermaidModule) {
    const mermaid = mermaidModule.default || mermaidModule;

    mermaid.initialize({
      startOnLoad: false,
      theme: 'default',
      securityLevel: 'loose',
      suppressErrorRendering: true,
      logLevel: 1,
    });

    mermaidInitialized = true;
  }
};

const MermaidRenderer: React.FC<MermaidRendererProps> = ({ code }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [hasError, setHasError] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [svgContent, setSvgContent] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);

  // Dynamic imports
  const { loading: mermaidLoading, data: mermaidModule, error: mermaidError } = useMermaidImport();
  const { scheduleRender, clearQueue } = useAsyncRender();
  const isLibraryLoading = mermaidLoading || !mermaidModule;
  const mermaidId = useId();

  // Core rendering logic
  const performMermaidRender = useCallback(async () => {
    if (!code?.trim() || !containerRef.current || !mermaidModule) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setHasError(false);
      setErrorMessage('');

      // Clear previous content
      containerRef.current.innerHTML = '';

      // Initialize Mermaid with error suppression
      initializeMermaid(mermaidModule);

      const mermaid = mermaidModule.default || mermaidModule;

      // Pre-validate syntax to catch errors before rendering
      try {
        const parseResult = await mermaid.parse(code, { suppressErrors: true });
        if (!parseResult) {
          throw new Error('Invalid diagram syntax');
        }
      } catch (parseError) {
        throw new Error(`Syntax Error: ${parseError instanceof Error ? parseError.message : 'Invalid diagram syntax'}`);
      }

      // Render the diagram with unique ID
      const { svg } = await mermaid.render(mermaidId, code);

      if (containerRef.current && svg) {
        containerRef.current.innerHTML = svg;
        setSvgContent(svg);
      } else {
        throw new Error('Failed to generate diagram');
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error occurred';
      console.warn(`[Mermaid Renderer ${mermaidId}]`, errorMsg);

      setHasError(true);
      setErrorMessage(errorMsg);

      // Display controlled error message within the container
      if (containerRef.current) {
        containerRef.current.innerHTML = `
          <div class="mermaid-error-display">
            <div>
              <div class="error-title">⚠️ Diagram Error</div>
              <div class="error-message">${errorMessage}</div>
            </div>
          </div>
        `;
      }
    } finally {
      setIsLoading(false);
    }
  }, [code, mermaidId, mermaidModule]);

  const validateAndRenderDiagram = useCallback(() => {
    // Clear any pending renders to avoid conflicts
    clearQueue();

    // Schedule the render with async execution
    // Uses requestIdleCallback to prevent UI blocking when multiple diagrams render
    scheduleRender(performMermaidRender);
  }, [performMermaidRender, scheduleRender, clearQueue]);

  useEffect(() => {
    validateAndRenderDiagram();
  }, [validateAndRenderDiagram]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearQueue();
    };
  }, [clearQueue]);

  const handleOpenModal = useCallback(() => {
    if (!hasError && svgContent) {
      setIsModalOpen(true);
    }
  }, [hasError, svgContent]);

  const handleCloseModal = useCallback(() => {
    setIsModalOpen(false);
  }, []);

  return (
    <div className="mermaid-container">
      <div
        ref={containerRef}
        className={clsx('mermaid-content', {
          'mermaid-loading': isLoading || isLibraryLoading,
          'mermaid-error': hasError,
          'mermaid-clickable': !hasError && svgContent && !isLoading && !isLibraryLoading,
        })}
        onClick={handleOpenModal}
        role={!hasError && svgContent ? 'button' : undefined}
        tabIndex={!hasError && svgContent ? 0 : undefined}
        onKeyDown={e => {
          if ((e.key === 'Enter' || e.key === ' ') && !hasError && svgContent) {
            e.preventDefault();
            handleOpenModal();
          }
        }}
        aria-label={!hasError && svgContent ? 'Click to open diagram in modal' : undefined}
      />

      {(isLoading || isLibraryLoading) && (
        <div className="mermaid-loading-indicator">
          {isLibraryLoading ? 'Loading Mermaid library...' : 'Rendering diagram...'}
        </div>
      )}

      {mermaidError && (
        <div className="mermaid-error-display">
          <div>
            <div className="error-title">⚠️ Library Load Error</div>
            <div className="error-message">Failed to load Mermaid library</div>
          </div>
        </div>
      )}

      {/* Modal for successfully rendered diagrams */}
      <MermaidModal
        isOpen={isModalOpen && !hasError && !!svgContent}
        svgContent={svgContent}
        onClose={handleCloseModal}
      />
    </div>
  );
};

export default MermaidRenderer;
