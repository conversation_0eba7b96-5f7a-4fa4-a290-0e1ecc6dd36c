from typing import Dict
from datetime import timedelta

import google.auth
import google.auth.transport
from google.auth import impersonated_credentials
from google.auth.transport import requests
from google.cloud import storage
from google.cloud.exceptions import NotFound as GCSNotFound

from pydantic import BaseModel
from werkzeug.utils import secure_filename

from lib.gcs_client import CloudStorageClient, CloudStorageDomainConfig


class GCSBucketInfo(BaseModel):
    bucket_name: str
    signed_url_service_account: str


class SignedUrlResult(BaseModel):
    signed_url: str
    gcs_path: str


class SignedUrlGenerationError(Exception):
    def __init__(self, blob_path):
        self.message = f"Failed to generate signed url to upload to {blob_path}"
        super().__init__(self.message)


class CloudStorageService:

    RESTRICTED_DOMAINS = ["ucci.com"]
    DEFAULT_DOMAIN_KEY = "default"
    SIGNED_URL_VERSION = "v4"
    SIGNED_URL_EXPIRATION_MINUTES = 45

    def __init__(self, storage_client: CloudStorageClient):
        self._persistence_client = storage_client.InstanceClient()
        self._domain_mappings: Dict[str,
                                    CloudStorageDomainConfig] = storage_client.domain_mappings

    def _normalize_email(self, email: str) -> str:
        return email.strip().lower()

    def _get_domain_config(self, domain: str) -> CloudStorageDomainConfig:
        default_domain_config = self._domain_mappings.get(
            self.DEFAULT_DOMAIN_KEY)
        domain_config = self._domain_mappings.get(
            domain, default_domain_config)
        return domain_config

    def _get_domain_config_from_email(self, user_email: str) -> CloudStorageDomainConfig:
        user_email = self._normalize_email(user_email)
        domain = self.get_domain_from_email(user_email)
        return self._get_domain_config(domain)

    def get_domain_from_email(self, email: str) -> str:
        email = self._normalize_email(email)
        if "@" not in email:
            return None
        return email.split("@")[1]

    def get_domain_workbook_bucket(self, domain: str) -> storage.Bucket:
        domain_config = self._get_domain_config(domain)
        bucket_name: str = domain_config.workbook_bucket

        workbook_bucket = self._persistence_client.bucket(bucket_name)
        return workbook_bucket

    def get_workbook_bucket(self, user_email: str) -> storage.Bucket:
        """
        Returns a handle to the GCS Bucket scoped to the domain of `user_email`
        for the purpose of workbooks.

        Parameters
        ----------
        `user_email`: str

        Returns
        -------
        `storage.Bucket`
        """

        domain_config = self._get_domain_config_from_email(user_email)
        bucket_name: str = domain_config.workbook_bucket

        workbook_bucket = self._persistence_client.bucket(bucket_name)
        return workbook_bucket

    def get_temp_upload_bucket(self, user_email: str) -> storage.Bucket:
        """
        Returns a handle to the GCS Bucket scoped to the domain of `user_email`
        for the purpose of uploading files for Sidekick sessions.

        Parameters
        ----------
        `user_email`: str

        Returns
        -------
        `storage.Bucket`
        """
        domain_config = self._get_domain_config_from_email(user_email)
        bucket_name: str = domain_config.temp_upload_bucket

        temp_upload_bucket = self._persistence_client.bucket(bucket_name)
        return temp_upload_bucket

    def get_upload_service_account(self, domain: str) -> str:
        domain_config = self._get_domain_config(domain)
        return domain_config.signed_url_service_account

    def get_upload_service_account_from_email(self, user_email: str) -> str:
        domain_config = self._get_domain_config_from_email(user_email)
        return domain_config.signed_url_service_account

    def get_blob_list(
        self,
        bucket: storage.Bucket,
        filter_prefix: str
    ):
        blob_list = bucket.list_blobs(prefix=filter_prefix)
        return blob_list

    def create_upload_signed_url(
        self,
        bucket: storage.Bucket,
        blob_path: str,
        mime_type: str,
        service_account: str,
        expiration_delta: timedelta = timedelta(
            minutes=SIGNED_URL_EXPIRATION_MINUTES),
    ) -> SignedUrlResult:
        """
        `create_upload_signed_url` generates a signed URL that can be used
        to upload a file to a GCS bucket, `bucket` to `blob_path`

        Parameters
        ----------
        `bucket` : storage.Bucket
        `blob_path` : str
        `mime_type` : str
        `service_account`: str
            Name of the GCP service account that will be used to create the signed url
        `expiration_delta`: timedelta
            How long from now the resulting signed url will be valid for. Defaults to 45 minutes

        Raises
        ------
        `SignedUrlGenerationError`
            Wraps the underlying error that caused the signed url generation to fail

        Returns
        -------
        `SignedUrlResult`
        """
        try:
            credentials, project_id = google.auth.default()
            target_scopes = [
                "https://www.googleapis.com/auth/cloud-platform",
                "https://www.googleapis.com/auth/devstorage.read_write",
            ]
            if credentials.token is None:
                credentials.refresh(requests.Request())
            impersonated_creds = impersonated_credentials.Credentials(
                source_credentials=credentials,
                target_principal=service_account,
                target_scopes=target_scopes,
            )
            if impersonated_creds.token is None:
                impersonated_creds.refresh(requests.Request())

            blob = bucket.blob(blob_path)
            signed_url = blob.generate_signed_url(
                version=self.SIGNED_URL_VERSION,
                expiration=expiration_delta,
                service_account_email=service_account,
                method="PUT",
                access_token=impersonated_creds.token,
                content_type=mime_type
            )
            gcs_path = f"gs://{blob.bucket.name}/{blob.name}"
            return SignedUrlResult(
                signed_url=signed_url,
                gcs_path=gcs_path
            )
        except Exception as e:
            print(e)
            raise SignedUrlGenerationError(blob_path) from e

    def download_blob(
        self,
        bucket: storage.Bucket,
        blob_path: str,
        local_path: str,
    ) -> bool:
        try:
            blob = bucket.blob(blob_path)
            blob.download_to_filename(local_path)
            return True
        except Exception as e:
            return False

    def delete_blob(
        self,
        bucket: storage.Bucket,
        blob_path: str,
    ) -> bool:
        try:
            blob = bucket.blob(blob_path)

            generation_match_precondition = None
            blob.reload()
            generation_match_precondition = blob.generation

            blob.delete(if_generation_match=generation_match_precondition)
            return True
        except GCSNotFound as nfe:
            return True  # just consider it a no-op
        except Exception as e:
            raise  # unexpected
