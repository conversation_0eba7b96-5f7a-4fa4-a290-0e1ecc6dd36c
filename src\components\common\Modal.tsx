import React from 'react';
import { IoClose } from 'react-icons/io5';
import { useThemeStyles } from '@hooks/useThemeStyles';
import { ModalProps } from '@features/workbook/workbookTypes';

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, children }) => {
  const { classes } = useThemeStyles();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Simple overlay */}
      <div className="absolute inset-0 bg-black/20" onClick={onClose} />

      {/* Modal Content */}
      <div
        className={`
          relative 
          ${classes.backgroundInput}
          rounded-[12px] 
          w-[650px] 
          min-h-[400px] 
          shadow-[0_12px_48px_rgba(0,0,0,0.18)]
          z-10
          overflow-hidden
          transition-all
          duration-300
        `}
      >
        {/* Close Button */}
        <button
          onClick={onClose}
          className={`
            absolute 
            top-4 
            right-4 
            ${classes.text}
            hover:opacity-80
            transition-all
            p-1
            rounded-full
            ${classes.hoverBackground}
          `}
          aria-label="Close modal"
          tabIndex={0}
        >
          <IoClose className="w-6 h-6" />
        </button>

        {children}
      </div>
    </div>
  );
};

export default Modal;
