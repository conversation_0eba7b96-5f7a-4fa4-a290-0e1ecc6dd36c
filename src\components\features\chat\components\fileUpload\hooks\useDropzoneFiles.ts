import { useCallback, useMemo } from 'react';
import { useDropzone, FileRejection, Accept } from 'react-dropzone';
import { ValidatedFile } from '@/types/fileUpload';
import { showToast } from '@/components/common/ToastNotification/ToastNotification';
import { ALLOWED_FILE_TYPES } from '@/components/features/chat/components/fileUpload/utils/fileUploadUtils';
import {
  createValidatedFileFromFile,
  createValidatedFileFromRejection,
  checkModelRestriction,
  shouldShowFileUploadUI,
} from '../utils/fileUploadHelpers';

const truncateFilename = (filename: string, maxLength: number = 30): string => {
  if (filename.length <= maxLength) {
    return filename;
  }
  const start = filename.substring(0, maxLength - 15);
  const end = filename.substring(filename.length - 12);
  return `${start}...${end}`;
};

export interface UseDropzoneFilesProps {
  selectedChatType: string;
  onFilesProcessed: (files: ValidatedFile[]) => void;
  disabled?: boolean;
}

export interface UseDropzoneFilesResult {
  getRootProps: <T extends Record<string, any>>(props?: T) => T;
  getInputProps: <T extends Record<string, any>>(props?: T) => T;
  isDragActive: boolean;
  openFileDialog: () => void;
}

export const useDropzoneFiles = ({
  selectedChatType,
  onFilesProcessed,
  disabled,
}: UseDropzoneFilesProps): UseDropzoneFilesResult => {
  const onDropInternal = useCallback(
    (acceptedDropzoneFiles: File[], fileRejections: FileRejection[]) => {
      if (!checkModelRestriction(selectedChatType)) {
        showToast.info(
          'File Upload Restricted',
          'File uploads are only allowed in the General model.'
        );
        return;
      }

      const newProcessedFiles: ValidatedFile[] = acceptedDropzoneFiles.map(
        createValidatedFileFromFile
      );

      fileRejections.forEach(rejection => {
        const rejectedValidatedFile = createValidatedFileFromRejection(rejection);
        newProcessedFiles.push(rejectedValidatedFile);
        const truncatedName = truncateFilename(rejection.file.name);
        showToast.error(
          'File Rejected',
          `${truncatedName} - File type not allowed. Please upload images, videos, audio, PDF, or text files only.`
        );
      });

      onFilesProcessed(newProcessedFiles);
    },
    [selectedChatType, onFilesProcessed]
  );

  const generateDropzoneAccept = useCallback((mimeTypes: string[]): Accept => {
    const accept: Accept = {};
    mimeTypes.forEach(type => {
      accept[type] = [];
    });
    return accept;
  }, []);

  const dropzoneAcceptConfig = useMemo(
    () => generateDropzoneAccept(ALLOWED_FILE_TYPES),
    [generateDropzoneAccept]
  );

  const isDropzoneDisabled = disabled || !shouldShowFileUploadUI(selectedChatType);

  const { getRootProps, getInputProps, isDragActive, open } = useDropzone({
    onDrop: onDropInternal,
    accept: dropzoneAcceptConfig,
    noClick: true,
    noKeyboard: true,
    disabled: isDropzoneDisabled,
  });

  return {
    getRootProps,
    getInputProps,
    isDragActive,
    openFileDialog: open,
  };
};
