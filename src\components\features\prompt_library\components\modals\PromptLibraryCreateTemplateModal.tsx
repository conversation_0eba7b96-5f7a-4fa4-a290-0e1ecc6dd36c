import { SessionType, type CreatePromptTemplate } from '@/types';

import React, { useState } from 'react';
import { MdOutlineEdit, MdOutlineSubdirectoryArrowRight } from 'react-icons/md';
import { TfiBookmarkAlt } from 'react-icons/tfi';
import { IoSaveOutline } from 'react-icons/io5';

import PromptLibraryBaseModal from './PromptLibraryBaseModal';
// import { useThemeStyles } from '@/hooks/useThemeStyles';

import './promptLibraryCreateTemplateModal.scss';

interface PromptLibraryCreateTemplateModalProps {
  isOpen: boolean;
  onOpenChange: (value: boolean) => void;
  onCreateAndRunPromptTemplate: (promptTemplate: CreatePromptTemplate) => void;
  onCreatePromptTemplate: (promptTemplate: CreatePromptTemplate) => void;
}

const PromptLibraryCreateTemplateModal: React.FC<PromptLibraryCreateTemplateModalProps> = ({
  isOpen,
  onOpenChange,
  onCreateAndRunPromptTemplate,
  onCreatePromptTemplate,
}) => {
  // const { classes } = useThemeStyles();

  // Component State
  const [selectedSessionType, setSelectedSessionType] = useState<SessionType>(SessionType.CHAT);
  const [isPromptNameInputFocused, setIsPromptNameInputFocused] = useState(false);
  const [promptName, setPromptName] = useState('');
  const [isPromptContentInputFocused, setIsPromptContentInputFocused] = useState(false);
  const [promptContent, setPromptContent] = useState('');
  const [isPromptSystemInstructionInputFocused, setIsPromptSystemInstructionInputFocused] = useState(false);
  const [promptSystemInstruction, setPromptSystemInstruction] = useState('');
  const [isLoading] = React.useState(false);

  const handlePromptNameInputFocus = () => {
    setIsPromptNameInputFocused(true);
  };

  const handlePromptNameInputBlur = () => {
    setIsPromptNameInputFocused(false);
  };

  const handlePromptContentInputFocus = () => {
    setIsPromptContentInputFocused(true);
  };

  const handlePromptContentInputBlur = () => {
    setIsPromptContentInputFocused(false);
  };

  const handlePromptSystemInstructionInputFocus = () => {
    setIsPromptSystemInstructionInputFocused(true);
  };

  const handlePromptSystemInstructionInputBlur = () => {
    setIsPromptSystemInstructionInputFocused(false);
  };

  const prepareCreatePromptTemplateModel = (): CreatePromptTemplate => {
    const template: CreatePromptTemplate = {
      name: promptName,
      prompt: promptContent,
      sessionType: selectedSessionType,
      temperature: 1.0,
      systemInstructions: promptSystemInstruction,
    };
    return template;
  };

  const handleCreatePromptTemplate = async () => {
    const promptTemplate = prepareCreatePromptTemplateModel();
    await onCreatePromptTemplate(promptTemplate);
    setPromptName('');
    setPromptContent('');
    setPromptSystemInstruction('');
    setSelectedSessionType(SessionType.CHAT);
  };

  const handleCreateAndRunPromptTemplate = async () => {
    const promptTemplate = prepareCreatePromptTemplateModel();
    await onCreateAndRunPromptTemplate(promptTemplate);
    setPromptName('');
    setPromptContent('');
    setPromptSystemInstruction('');
    setSelectedSessionType(SessionType.CHAT);
  };

  return (
    <PromptLibraryBaseModal
      isOpen={isOpen}
      onClose={() => onOpenChange(false)}
      title="New Library Prompt"
      infoText="File sizes cannot exceed 10mb. Only 4 PDF files can be uploaded to a notebook."
    >
      {/* Input fields container */}
      <div className="prompt-form">
        {/* Prompt SessionType - Dropdown */}
        <div className="prompt-form__session-select-container">
          <select
            className="prompt-form__session-select"
            value={selectedSessionType}
            onChange={event => setSelectedSessionType(event.target.value as unknown as SessionType)}
          >
            <option key={SessionType.CHAT} value={SessionType.CHAT}>General</option>
            <option key={SessionType.CODE} value={SessionType.CODE}>Code</option>
            <option key={SessionType.MDLM} value={SessionType.MDLM}>Medical</option>
          </select>
        </div>

        {/* Prompt Title - Text Field Container */}
        <div className={`prompt-form__field prompt-form__field--name ${promptName ? 'prompt-form__field--filled' : ''}`}>
          {/* Input Container */}
          <div className={`prompt-form__input-container ${isPromptNameInputFocused ? 'prompt-form__input-container--focused' : ''}`}>
            {/* Content Container */}
            <div className="prompt-form__input-wrapper">
              <input
                className="prompt-form__input"
                id="prompt-name-create"
                type="text"
                placeholder="Name your prompt..."
                disabled={isLoading}
                value={promptName}
                onChange={event => setPromptName(event.target.value)}
                onFocus={handlePromptNameInputFocus}
                onBlur={handlePromptNameInputBlur}
              />
              {/* Icon Container */}
              <div className="prompt-form__icon">
                <MdOutlineEdit className="prompt-form__edit-icon" />
              </div>
            </div>
          </div>
        </div>

        {/* Prompt Content - Text Field Container */}
        <div className={`prompt-form__field prompt-form__field--content ${promptContent ? 'prompt-form__field--filled' : ''}`}>
          {/* Input Container */}
          <div className={`prompt-form__input-container prompt-form__input-container--large ${isPromptContentInputFocused ? 'prompt-form__input-container--focused' : ''}`}>
            {/* Content Container */}
            <div className="prompt-form__textarea-wrapper">
              <textarea
                className="prompt-form__textarea"
                id="prompt-content-create"
                placeholder="Enter your prompt..."
                disabled={isLoading}
                value={promptContent}
                onChange={event => setPromptContent(event.target.value)}
                onFocus={handlePromptContentInputFocus}
                onBlur={handlePromptContentInputBlur}
              >
                {promptContent}
              </textarea>
              {/* Icon Container */}
              <div className="prompt-form__icon">
                <MdOutlineEdit className="prompt-form__edit-icon" />
              </div>
            </div>
          </div>
        </div>

        {/* Prompt System Instruction - Text Field Container */}
        <div
          className={`prompt-form__field prompt-form__field--content ${promptSystemInstruction ? 'prompt-form__field--filled' : ''}`}
        >
          {/* Input Container */}
          <div className={`prompt-form__input-container prompt-form__input-container--large ${isPromptSystemInstructionInputFocused ? 'prompt-form__input-container--focused' : ''}`}>
            {/* Content Container */}
            <div className="prompt-form__textarea-wrapper">
              <textarea
                className="prompt-form__textarea"
                id="prompt-system-instruction-create"
                placeholder="Enter prompt system instruction..."
                disabled={isLoading}
                value={promptSystemInstruction}
                onChange={event => setPromptSystemInstruction(event.target.value)}
                onFocus={handlePromptSystemInstructionInputFocus}
                onBlur={handlePromptSystemInstructionInputBlur}
              >
                {promptSystemInstruction}
              </textarea>
              {/* Icon Container */}
              <div className="prompt-form__icon">
                <MdOutlineEdit className="prompt-form__edit-icon" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Interaction row container */}
      <div className="prompt-actions">
        {/* Delete Button */}
        <button className="prompt-actions__button prompt-actions__button--delete">
          {/* Delete Button Contents Container */}
          <div className="prompt-actions__button-content prompt-actions__button-content--delete">
            <span className="prompt-actions__icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
              >
                <path
                  d="M16 9V19H8V9H16ZM14.5 3H9.5L8.5 4H5V6H19V4H15.5L14.5 3ZM18 7H6V19C6 20.1 6.9 21 8 21H16C17.1 21 18 20.1 18 19V7Z"
                  fill="#6D787F"
                />
              </svg>
            </span>
            <p className="prompt-actions__text">
              Delete Prompt
            </p>
          </div>
        </button>

        {/* Save Button - Run Button Container */}
        <div className={`prompt-actions__buttons-group ${promptContent ? 'prompt-actions__buttons-group--content' : ''}`}>
          {/* Save -->-- Save & Run Button */}
          <button
            className={`prompt-actions__button ${promptContent ? 'prompt-actions__button--save-run' : 'prompt-actions__button--save'}`}
            onClick={handleCreateAndRunPromptTemplate}
          >
            {/* Save Button Contents Container */}
            <div className="prompt-actions__button-content">
              {promptContent ? (
                <MdOutlineSubdirectoryArrowRight className="prompt-actions__icon" />
              ) : (
                <TfiBookmarkAlt className="prompt-actions__icon" />
              )}
              <p className="prompt-actions__text">
                {promptContent ? 'Save and Run Prompt' : 'Save Prompt'}
              </p>
            </div>
          </button>

          {/* Run -->-- Save Button */}
          <button
            className={`prompt-actions__button ${promptContent ? 'prompt-actions__button--save prompt-actions__button--primary' : 'prompt-actions__button--run'}`}
            onClick={handleCreatePromptTemplate}
          >
            {/* Run Button Contents Container */}
            <div className="prompt-actions__button-content">
              {promptContent ? (
                <IoSaveOutline className="prompt-actions__icon" />
              ) : (
                <MdOutlineSubdirectoryArrowRight className="prompt-actions__icon" />
              )}
              <p className="prompt-actions__text">
                {promptContent ? 'Save Prompt' : 'Run Prompt'}
              </p>
            </div>
          </button>
        </div>
      </div>
    </PromptLibraryBaseModal>
  );
};

export default PromptLibraryCreateTemplateModal;
