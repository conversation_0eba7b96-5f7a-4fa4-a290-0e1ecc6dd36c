import { ValidatedFile } from '@/types/fileUpload';
import { showToast } from '@/components/common/ToastNotification/ToastNotification';

export interface UploadResult {
  fileId: string;
  result: Partial<ValidatedFile>;
}

export interface UploadBatchResult {
  successCount: number;
  failCount: number;
  cancelledCount: number;
  failedFiles: string[];
}

/**
 * Processes upload results and returns summary
 */
export const processUploadResults = (
  uploadResults: PromiseSettledResult<UploadResult>[],
  processedFiles: ValidatedFile[]
): UploadBatchResult => {
  let successCount = 0;
  let failCount = 0;
  let cancelledCount = 0;
  const failedFiles: string[] = [];

  uploadResults.forEach((promiseResult, index) => {
    if (promiseResult.status === 'fulfilled') {
      const { result } = promiseResult.value;
      const fileName = processedFiles[index]?.name || 'Unknown file';

      if (result.uploadStatus === 'success') {
        successCount++;
      } else if (result.uploadStatus === 'cancelled') {
        cancelledCount++;
      } else {
        failCount++;
        failedFiles.push(fileName);
      }
    } else {
      // Promise rejected case
      const fileName = processedFiles[index]?.name || 'Unknown file';
      failCount++;
      failedFiles.push(fileName);
    }
  });

  return { successCount, failCount, cancelledCount, failedFiles };
};

/**
 * Shows toast notifications based on upload results
 */
export const showUploadResultToasts = (batchResult: UploadBatchResult): void => {
  const { successCount, failCount, cancelledCount, failedFiles } = batchResult;
  const totalProcessed = successCount + failCount + cancelledCount;

  if (totalProcessed === 0) return;

  if (successCount === totalProcessed) {
    // All succeeded
    showToast.positive('Upload Complete', `${successCount} file${successCount > 1 ? 's' : ''} uploaded successfully.`);
  } else if (failCount === totalProcessed) {
    // All failed
    showToast.error('Upload Failed', `${failCount} file${failCount > 1 ? 's' : ''} failed to upload.`);
  } else if (cancelledCount === totalProcessed) {
    // All cancelled - don't show notification for user-initiated cancellations
    console.log(`[uploadResultHandlers] ${cancelledCount} file(s) cancelled by user`);
  } else {
    // Mixed results
    const messages = [];
    if (successCount > 0) {
      messages.push(`${successCount} uploaded`);
    }
    if (failCount > 0) {
      messages.push(`${failCount} failed: ${failedFiles.join(', ')}`);
    }
    if (cancelledCount > 0) {
      messages.push(`${cancelledCount} cancelled`);
    }

    showToast.caution('Upload Partially Complete', messages.join(', '));
  }
};

/**
 * Updates files state based on upload results
 */
export const applyUploadResults = (
  files: ValidatedFile[],
  uploadResults: PromiseSettledResult<UploadResult>[],
  processedFiles: ValidatedFile[]
): ValidatedFile[] => {
  const updatedFiles = [...files];

  uploadResults.forEach((promiseResult, index) => {
    if (promiseResult.status === 'fulfilled') {
      const { fileId, result } = promiseResult.value;
      const fileIndex = updatedFiles.findIndex(f => f.id === fileId);
      if (fileIndex !== -1) {
        updatedFiles[fileIndex] = { ...updatedFiles[fileIndex], ...result };
      }
    } else {
      // Promise rejected case
      const fileId = processedFiles[index]?.id;
      if (fileId) {
        const fileIndex = updatedFiles.findIndex(f => f.id === fileId);
        if (fileIndex !== -1) {
          updatedFiles[fileIndex] = {
            ...updatedFiles[fileIndex],
            uploadStatus: 'error' as const,
            uploadError: 'Upload promise rejected',
          };
        }
      }
    }
  });

  return updatedFiles;
};
