import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';

import './index.css';
import App from './App.tsx';
import { store } from './store/store.ts';
import { fetchMyWorkbooks, fetchGlobalWorkbooks } from './store/slices/workbookSlice.ts';
import { fetchCurrentUser } from './store/slices/authSlice.ts';
import { registerAllSupportedLanguages } from './components/common/markdown/highlightSetup.ts';

async function start() {
  registerAllSupportedLanguages();
  store.dispatch(fetchMyWorkbooks());
  store.dispatch(fetchGlobalWorkbooks());
  store.dispatch(fetchCurrentUser());

  const root = createRoot(document.getElementById('root')!);

  root.render(
    <StrictMode>
      <Provider store={store}>
        <App />
      </Provider>
    </StrictMode>
  );
}

start();
