/* eslint-disable prettier/prettier */
import React from 'react';
import { IoCopyOutline, IoCheckmarkOutline } from 'react-icons/io5';
import { HiOutlineThumbUp, HiOutlineThumbDown } from 'react-icons/hi';
import { MessageActionsProps } from '@/components/features/workbook/workbookTypes';

/**
 * MessageActions - Component that renders the action bar for AI response
 * 
 * Provides buttons for copying, liking/disliking
 */
const MessageActions: React.FC<MessageActionsProps> = ({
  isHovered,
  feedback,
  state,
  handlers
}) => {
  const { 
    copied, 
  } = state;
  
  const { 
    handleCopy, 
    handleThumbsUp, 
    handleThumbsDown, 
  } = handlers;

  // Style definitions using template strings
  const styles = {
    container: `
      absolute 
      bottom-1 
      left-0 
      flex 
      flex-wrap 
      items-center 
      gap-3 
      py-1 
      px-1 
      transition-all 
      duration-300 
      ease-in-out
      ${isHovered ? 'opacity-100' : 'opacity-0'}
    `,
    actionGroup: `
      flex 
      items-center 
      gap-3
    `,
    roundButton: `
      flex 
      items-center 
      justify-center 
      w-9 
      h-9 
      rounded-full 
      transition-colors
    `,
    roundButtonPrimary: `
      bg-[#002D4F] 
      hover:bg-[#00417A] 
      text-white 
      transition-colors
    `,
    roundButtonLike: `
      ${feedback ? 'bg-blue-600' : 'bg-[#002D4F] hover:bg-[#00417A]'} 
      text-white 
      transition-colors
    `,
    roundButtonDislike: `
      ${typeof(feedback) !== 'undefined' && feedback !== null && !feedback ? 'bg-red-600' : 'bg-[#002D4F] hover:bg-[#00417A]'} 
      text-white 
      transition-colors
    `,
    icon: `
      w-4.5 
      h-4.5 
      text-white
    `
  };

  return (
    <div className={styles.container}>
      {/* Basic actions */}
      <div className={styles.actionGroup}>
        <button 
          onClick={handleCopy}
          className={`${styles.roundButton} ${styles.roundButtonPrimary}`}
          title={copied ? "Copied!" : "Copy message"}
        >
          {copied ? (
            <IoCheckmarkOutline className={styles.icon} />
          ) : (
            <IoCopyOutline className={styles.icon} />
          )}
        </button>
        <button 
          onClick={handleThumbsUp}
          className={`${styles.roundButton} ${styles.roundButtonLike}`}
          title="Thumbs up"
        >
          <HiOutlineThumbUp className={styles.icon} />
        </button>
        <button 
          onClick={handleThumbsDown}
          className={`${styles.roundButton} ${styles.roundButtonDislike}`}
          title="Thumbs down"
        >
          <HiOutlineThumbDown className={styles.icon} />
        </button>
      </div>
    </div>
  );
};

export default MessageActions; 
