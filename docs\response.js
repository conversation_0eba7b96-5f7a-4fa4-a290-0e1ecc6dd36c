document.addEventListener("DOMContentLoaded", function () {
  console.log(window.location.href);
  console.log(email);
  const prompt = document.getElementById("prompt");
  if (prompt != null) {
    prompt.addEventListener("keydown", allowControlEnterKey);
    prompt.addEventListener("keypress", preventEnterKey);
    const chat = document.getElementById("chat");
    chat.addEventListener("submit", submitPrompt);
  }
});
const converter = new showdown.Converter({
  extensions: [startAttributeExtension],
  tables: true,
});
const ASYNC_POLLING_INTERVAL = 2000;
let intervalId;
let pollingIntervalId;
/**
 * Converts an ISO date string to a formatted date and time string.
 * @param {string} isoDate - The ISO date string to convert.
 * @returns {string} - The formatted date and time string.
 */
function convertISODate(isoDate) {
  const date = new Date(isoDate);
  const options = {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true,
  };
  return date.toLocaleDateString("en-US", options).replace(",", "");
}

/**
 * Prevents the default behavior of the Enter key in a textarea,
 * inserting a newline instead.
 * @param {Event} event - The keypress event.
 */
function preventEnterKey(event) {
  if (event.keyCode === 13) {
    event.preventDefault();
    const textarea = event.target;
    const currentValue = textarea.value;
    const cursorPosition = textarea.selectionStart;

    textarea.value =
      currentValue.substring(0, cursorPosition) +
      "\n" +
      currentValue.substring(cursorPosition);
    textarea.setSelectionRange(cursorPosition + 1, cursorPosition + 1);
  }
}

function allowControlEnterKey(event) {
  const promptArea = document.getElementById("prompt");
  if (event.ctrlKey && event.key === "Enter") {
    event.preventDefault();
    if (this.value.trim().length > 0) {
      submitPrompt(event);
    }
  }
}

/**
 * Starts the attribute extension for markdown lists.
 * @returns {Array} - An array of attribute extension objects.
 */
function startAttributeExtension() {
  const startNumbers = [];
  return [
    {
      type: "lang",
      filter: function (text) {
        const olMarkdownRegex = /^\s*(\d+)\. /gm;
        const lines = text.split("\n");
        lines.forEach((line) => {
          const match = olMarkdownRegex.exec(line);
          if (match) {
            startNumbers.push(match[1]);
          }
        });
        return text;
      },
    },
    {
      type: "output",
      filter: function (text) {
        if (startNumbers.length > 0) {
          const lines = text.split("\n");
          lines.forEach((line, index) => {
            if (line.includes("<ol>")) {
              const startNumber = startNumbers.shift();
              lines[index] = line.replace(
                "<ol>",
                `<ol start="${startNumber}">`
              );
            }
          });
          text = lines.join("\n");
        }
        return text;
      },
    },
  ];
}

// History objects
let chat_history = [];
let file_history = [];

/**
 * Handles the submission of a prompt from the user.
 * @param {Event} event - The form submission event.
 */
function submitPrompt(event) {
  event.preventDefault();
  // let csrftoken = Cookies.get('csrftoken');

  const dateTime = new Date();
  const promptTime = dateTime.toLocaleTimeString();
  const promptData = document.getElementById("prompt").value;
  const promptClient = promptData
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/\n/g, "<br>");

  createMessage("person", "prompts", promptClient);

  intervalId = setInterval(thinkingIndicator, 1000);

  document.getElementById("prompt").value = "";

  fetchResponse(promptData);
}

/**
 * Fetches the response from the server.
 * @param {string} promptData - The prompt data to send to the server.
 * @returns {Promise} - A promise that resolves with the response data from the server.
 */
async function fetchResponse(promptData) {
  let url;
  if (window.location.href.includes("code")) {
    url = codeLink;
  } else if (window.location.href.includes("med")) {
    url = medLink;
  } else {
    url = multiLink;
  }

  const temperatureContainer = document.getElementById("temperature_container");
  let temperature;
  try {
    const checkedRadioButton = temperatureContainer.querySelector(
      'input[name="temp"]:checked'
    );
    if (checkedRadioButton) {
      temperature = checkedRadioButton.value;
    } else {
      temperature = 1;
    }
  } catch (error) {
    console.log("Defaulting temperature");
    temperature = 1;
  }

  const sessionId = document.getElementById("sessionId").value;
  let firestoreSessionId = "";
  if (
    typeof sessionDocument !== "undefined" &&
    typeof sessionDocument !== "null"
  ) {
    firestoreSessionId = sessionDocument.id;
  } else {
    firestoreSessionId = document.getElementById("firestoreSessionId").value;
  }

  const max_output_tokens = 8192; //1 - 8192 int

  let bodyVariable;
  if (window.location.href.includes("code")) {
    bodyVariable = JSON.stringify({
      prompt: promptData,
      session_id: sessionId,
      firestore_session_id: firestoreSessionId,
      temperature: temperature,
      max_output_tokens: max_output_tokens,
    });
  } else {
    bodyVariable = JSON.stringify({
      prompt: promptData,
      file_history: file_history,
      session_id: sessionId,
      firestore_session_id: firestoreSessionId,
      temperature: temperature,
      max_output_tokens: max_output_tokens,
    });
  }

  const fetchOptions = {
    method: "POST",
    headers: { "Content-Type": "application/json", Accept: "application/json" },
    body: bodyVariable,
  };

  if (checkAuth()) {
    let response;
    try {
      response = await fetch(url, fetchOptions);
    } catch (error) {
      console.error("Error fetching data:", error);
      response = null;
    }

    if (response == null) {
      onError("Failure in sending prompt to server.");
    } else {
      const resHeaders = response.headers.get("Content-Type");
      if (response.ok && resHeaders.includes("application/json")) {
        response
          .json()
          .then((data) => {
            if (data.reauth) {
              console.log("Attempting to reauth");
              refreshAuthFlow(fetchResponse, promptData);
            } else {
              if (data.response_success) {
                const firestoreSessionId = data["firestore_session_id"];
                handlePollingAsyncResponse(promptData, firestoreSessionId);
              } else {
                stopThinking(intervalId);
                handleError(data.response);
              }
            }
            if (data.session_id != null || data.session_id != undefined) {
              document.getElementById("sessionId").value = data.session_id;
              console.log(`Set sessionId ${data.session_id}`);
            }
            if (
              data.firestore_session_id != null ||
              data.firestore_session_id != undefined
            ) {
              document.getElementById("firestoreSessionId").value =
                data.firestore_session_id;
              console.log(`Set firestore id ${data.firestore_session_id}`);
            }
          })
          .catch(onError);
      } else if (!response.ok && resHeaders.includes("application/json")) {
        const error_json = await response.json();
        onError(`Error: ${error_json.message}`, true);
      } else if (resHeaders.includes("text/html")) {
        let errorHTML = await response.text();
        errorHTML = escapeHtml(errorHTML);
        onError(
          `Error: An unexpected response was received:<br>${errorHTML} <br>Response status: ${response.status} ${response.statusText}`
        );
      } else {
        onError(
          `Error: An invalid response was received. <br>Response status: ${response.status} ${response.statusText}`
        );
      }
    }
  } else {
    window.location.reload(); //Force a refresh
  }
  document.getElementById("wordDocExport").disabled = false;
}

/**
 * Sets up and manages the polling interval to check for the prompt's response from the server.
 * @param {string} promptData - The prompt data that was sent to the server.
 * @param {string} firestoreSessionId - The firestore session id to poll for a response.
 */
function handlePollingAsyncResponse(promptData, firestoreSessionId) {
  const fetchOptions = {
    method: "GET",
    headers: { Accept: "application/json" },
  };

  pollingIntervalId = setInterval(async () => {
    const response = await fetch(
      `/sidekick/restful/session/${firestoreSessionId}/prompt_response`,
      fetchOptions
    );
    if (response.ok) {
      const data = await response.json();
      if (data.ready) {
        stopThinking(intervalId);
        clearInterval(pollingIntervalId);
        handleResponse(promptData, data, converter, firestoreSessionId);
      }
    } else {
      onError(
        `Error: An invalid response was received. <br>Response status: ${response.status} ${response.statusText}`
      );
    }
  }, ASYNC_POLLING_INTERVAL);
}

/**
 * Handles the response from the server.
 * @param {Object} data - The response data from the server.
 * @param {Object} converter - The showdown converter object.
 */
function handleResponse(promptData, data, converter, firestoreSessionId) {
  chat_history.push({
    parts: [{ text: promptData }],
    role: "user",
  });

  chat_history.push({
    parts: [{ text: data.response }],
    role: "model",
  });

  const resdateTime = new Date(data.response_time_utc);
  const responseTime = resdateTime.toLocaleTimeString();
  // <div class="btn-group">

  const promptId = `${firestoreSessionId}-${
    Math.ceil(chat_history.length / 2) - 1
  }`; // each prompt/response pair in chat_history counts as 2 entries
  const thumbs = createFeedbackButtons(promptId, "");
  const thumbsUp = thumbs[0];
  const thumbsDown = thumbs[1];
  // </div>

  let fixedResponse;
  if (/(```)[^ ]+/.test(data.response)) {
    const bad_format = data.response.indexOf("```") + 3;
    fixedResponse =
      data.response.substring(0, bad_format) +
      "\n" +
      data.response.substring(bad_format);
  } else {
    fixedResponse = data.response;
  }
  createMessage("robot", "responses", converter.makeHtml(fixedResponse));
  const feedback_list = document.getElementsByClassName("messages_wrapper");
  const copyButton = generateCopyButton();
  copyIter += 1; // Increment copyIter constant

  const feedback_div = document.createElement("div");
  feedback_div.className = "feedbackDIV feedback";
  feedback_div.appendChild(thumbsUp);
  feedback_div.appendChild(thumbsDown);
  feedback_div.appendChild(copyButton);
  if (!window.location.href.includes("code")) {
    if (checkIfAllowed(false)) {
      let pptButton = createPPTButton();
      let wordButton = createWordButton();
      feedback_div.appendChild(pptButton);
      feedback_div.appendChild(wordButton);
    }
  }

  feedback_list[feedback_list.length - 1].appendChild(feedback_div);
}

/**
 * Handles errors during the fetch request.
 * @param {Error} err - The error object.
 */
function onError(err, rawText = false) {
  try {
    stopThinking(intervalId);
  } catch (error) {
    console.err("Couldn't stop thinking");
  }

  const errorMessage = rawText
    ? `${err}`
    : `Sidekick ran into an error. Please refresh or try again later.<br> ${err}`;

  handleError(errorMessage);
  throw err;
}
