import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

import { Radio } from '@base-ui-components/react/radio';
import { RadioGroup } from '@base-ui-components/react/radio-group';

import useThemeStyles from '@/hooks/useThemeStyles';
import { useAppSelector, useAppDispatch } from '@/store/hooks';

import { ChatType, chatTypeToUrl } from '../../chatTypes';
import {
  setSelectedChatType,
  selectSelectedChatType,
  selectCurrentSessionId,
  selectIsLoading,
} from '@/store/slices/chatSlice';

import './ChatTypeRadioGroup.scss';

interface ChatTypeRadioGroupProps {
  initialValue: ChatType
}

const ChatTypeRadioGroup: React.FC<ChatTypeRadioGroupProps> = (props: ChatTypeRadioGroupProps) => {
  const { classes } = useThemeStyles();
  const navigate = useNavigate();
  const location = useLocation();

  const { initialValue } = props;
  const dispatch = useAppDispatch();
  const selectedChatType = useAppSelector(selectSelectedChatType);
  const currentSessionId = useAppSelector(selectCurrentSessionId);

  const isLoading = useAppSelector(selectIsLoading);
  const isDisabled = !!currentSessionId || isLoading;

  const onChatTypeSelected = (value: ChatType) => {
    if (!isDisabled) {
      dispatch(setSelectedChatType(value));

      const replacedUrl = chatTypeToUrl[value];
      if (replacedUrl && !location.pathname.includes(replacedUrl)) {
        navigate(chatTypeToUrl[value], { replace: true });
      }
    }
  };

  useEffect(() => {
    onChatTypeSelected(initialValue);
  }, [initialValue, onChatTypeSelected])

  return (
    <div className='ChatType__radio-group-container'>
      <div className='ChatType__radio-group-header'>
        Session type:
      </div>
      <RadioGroup
        aria-labelledby="apples-caption"
        defaultValue={initialValue}
        className={'ChatType__radio-group'}
        value={selectedChatType}
        onValueChange={(value) => onChatTypeSelected(value as ChatType)}
        disabled={isDisabled}
      >
        <label className={`ChatType__radio-item ChatType__radio-item--general ${classes.text}`}>
          <Radio.Root value="General" className={'ChatType__radio'} disabled={isDisabled}>
            <Radio.Indicator className={'ChatType__radio-indicator'} />
          </Radio.Root>
          General
        </label>

        <label className={`ChatType__radio-item ChatType__radio-item--code ${classes.text}`}>
          <Radio.Root value="Code" className={'ChatType__radio'} disabled={isDisabled}>
            <Radio.Indicator className={'ChatType__radio-indicator'} />
          </Radio.Root>
          Code
        </label>

        <label className={`ChatType__radio-item ChatType__radio-item--medical ${classes.text}`}>
          <Radio.Root value="Medical" className={'ChatType__radio'} disabled={isDisabled}>
            <Radio.Indicator className={'ChatType__radio-indicator'} />
          </Radio.Root>
          Medical
        </label>

        <label className={`ChatType__radio-item ChatType__radio-item--policy ${classes.text}`}>
          <Radio.Root value="Policy" className={'ChatType__radio'} disabled={isDisabled}>
            <Radio.Indicator className={'ChatType__radio-indicator'} />
          </Radio.Root>
          Policy
        </label>
      </RadioGroup>
    </div>
  );
};

export default ChatTypeRadioGroup;
