.workbook-title {
    word-break: break-word;
}

.workbook-card {
    width: 240px;
    height: 300px;
    border-radius: 12px;
    border: 1px solid var(--border-color);
    background: var(--background-color);
    overflow: hidden;
    transition: transform 0.2s ease-in-out;
    padding: 24px;
    display: flex;
    flex-direction: column;

    &:hover {
        transform: scale(calc(1 + 0.05 * (1 - var(--disable-card-hover, 0))));
    }
}

.workbook-card__content {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.workbook-card__header {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    justify-content: space-between;
    gap: 16px;
    min-height: 0;
}

.workbook-card__title {
    font-family: 'Sofia_Pro', sans-serif;
    font-size: 24px;
    font-weight: bold;
    line-height: 120%;
    letter-spacing: 0.192px;
    color: #fff;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    word-break: break-word;
}

.workbook-card__footer {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.workbook-card__footer-info {
    // This class is now just a container for the date and divider. No special styling needed.
}

.workbook-card__date {
    font-family: 'Roboto', sans-serif;
    font-size: 12px;
    font-weight: normal;
    line-height: 100%;
    letter-spacing: 0.096px;
    color: #fff;
    margin-bottom: 12px;
}

.workbook-card__divider {
    width: 100%;
    height: 1px;
    background-color: #0066b1;
}

.workbook-card__run-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    padding: 6px 14px;
    border-radius: 50px;
    border: 1px solid #0066b1;
    background-color: transparent;
    cursor: pointer;
    font-family: 'Roboto', sans-serif;
    font-size: 16px;
    color: #fff;
    transition: background-color 0.3s, color 0.3s;
    align-self: flex-start;

    &:hover {
        background-color: #0066b1;
        color: white;
    }
}

.workbook-card__manage-button {
    display: flex;
    width: 192px;
    padding: 4px 8px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    border-radius: 8px;
    border: 1px solid #0066b1;
    background: #003963;
    color: #fff;
    cursor: pointer;
    font-family: 'Roboto', sans-serif;
    transition: transform 0.2s ease, box-shadow 0.2s ease;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 102, 177, 0.3);
    }
}

.workbook-card__manage-content {
    display: flex;
    padding: 12px 24px;
    align-items: center;
    gap: 8px;
    align-self: stretch;
    border-radius: 4px 4px 0px 0px;
    opacity: 0.7;
    font-size: 16px;
}

.workbook-card__manage-popup {
    background: rgba(0, 102, 177, 0.6);
    width: 192px; /* Same as manage button */
    padding: 12px 0px 0px 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    align-self: stretch;
    border-radius: 8px;
    border: 1px solid #0066b1;
    backdrop-filter: blur(8px);
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    overflow: hidden; /* This prevents shadows from extending beyond menu boundaries */
    
    /* Animation for smooth appearance */
    opacity: 0;
    transform: translateY(8px);
    transition: opacity 0.2s ease, transform 0.2s ease;
    
    &[data-open] {
        opacity: 1;
        transform: translateY(0);
    }
}

.workbook-card__manage-item {
    display: flex;
    padding: 8px 16px 8px 16px;
    justify-content: flex-start;
    align-items: center;
    gap: 4px;
    align-self: stretch;
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    color: #fff;
    background: transparent;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: transform 0.2s ease;
    
    &:hover {
        transform: translateX(4px) scale(1.10);
    }
    
    &:focus {
        outline: none;
        transform: translateX(4px) scale(1.03);
    }
    
    &:active {
        transform: translateX(4px) scale(1.03);
    }
}//

.workbook-card__manage-icon {
    width: 12px;
    height: 12px;
    flex-shrink: 0;
}

.workbook-deleting {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    
    .workbook-deleting-spinner {
        height: 100%;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
