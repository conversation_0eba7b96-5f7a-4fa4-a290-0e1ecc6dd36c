.workbook-utility-bar-main {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 24px;
    .workbook-name {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 8px;
        .workbook-name-text {
            font-family: var(--font-sofia-pro);
            font-size: 20px;
            font-style: normal;
            font-weight: 700;
            line-height: normal;
            letter-spacing: 0.16px;

            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            overflow: hidden;
            word-break: break-word;
        }
        .workbook-name-edit {
            cursor: pointer;
        }
        .workbook-name-edit-input {

        }
        .workbook-name-edit-save {
            cursor: pointer;
        }
        .workbook-name-edit-cancel {
            cursor: pointer;
        }
    }

    .workbook-files {
        color: white;
        margin-top: 32px;
        display: flex;
        flex-direction: column;
        gap: 10px;
        max-height: calc(100vh - 300px);
        overflow-y: auto;
        overflow-x: hidden;
        &::-webkit-scrollbar {
            width: 6px;
        }
        &::-webkit-scrollbar-track {
            border-radius: calc(infinity * 1px);
        }
        &::-webkit-scrollbar-thumb {
            border-radius: 4px;
            background-color: #0066B1;
        }
    }
    .workbook-actions { 
        margin-top: auto;
    }
    .workbook-new-session {
        margin-top: 12px !important;
        .workbook-new-session-text {
            padding: 4px 24px 4px 24px;
        }
    }
    .workbook-add-file, .workbook-new-session {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        align-self: stretch;
        color: white;
        border: 2px solid #0066B1;
        border-radius: 50px;
        cursor: pointer;
        .workbook-add-file-label {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            padding: 8px 24px 8px 24px;
            border-radius: 50px;
            .workbook-add-file-text, .workbook-new-session-text {
                text-align: center;
                font-family: var(--font-roboto);
                font-size: 16px;
                font-style: normal;
                font-weight: 600;
                line-height: 100%;
                letter-spacing: 0.128px;
            }
        }
    }
}
