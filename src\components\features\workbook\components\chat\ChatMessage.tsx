import React, { useState } from 'react';
import { ChatMessageProps } from '@/types/layout';
import { useThemeStyles } from '@/hooks/useThemeStyles';
import CitationRenderer from './CitationRenderer';
import MessageActions from './MessageActions';
import { useMessageActions } from './hooks/useMessageActions';
import MarkdownRenderer from '../../../../common/markdown/MarkdownRenderer';
import { RetrievalSessionAnswer } from '@/types';

/**
 * ChatMessage - Component to display an individual chat message
 *
 * - Different styling for user and AI messages
 * - Support for displaying citations in AI messages
 * - Action bar for AI messages (copy, thumbs up, thumbs down)
 */
const ChatMessage: React.FC<ChatMessageProps> = ({ message, messageModel, isUser, handleFeedbackDown, handleFeedbackUp, citations = [], citationSources = [] }) => {
  const { classes } = useThemeStyles();
  const [isHovered, setIsHovered] = useState(false);

  // Get message action handlers and state
  const { state, handleCopy } =
    useMessageActions(message);

  // Style based on message type (user or AI)
  const containerStyle = isUser
    ? `flex justify-end mb-4 ${classes.text} w-full overflow-hidden`
    : `flex flex-col justify-start mb-4 w-full ${classes.text} relative overflow-hidden`;

  // Always show background for user messages, no background for AI messages
  const messageStyle = isUser
    ? `inline-block max-w-[80%] py-5 px-4 rounded-xl bg-[#002D4F] text-white shadow-md transition-all duration-200 ease-in-out overflow-hidden`
    : 'w-full max-w-full py-5 pb-15 overflow-hidden';

  // Text content styles
  const textContentStyle = isUser
    ? 'break-words whitespace-pre-wrap overflow-x-hidden max-w-full'
    : 'break-words  w-full overflow-x-hidden max-w-full';

  return (
    <div
      className={containerStyle}
      onMouseEnter={() => !isUser && setIsHovered(true)}
      onMouseLeave={() => !isUser && setIsHovered(false)}
    >
      <div className={messageStyle}>
        {citations && citations.length > 0 ? (
          // If message has citations, use CitationRenderer with markdown enabled
          <CitationRenderer
            message={message}
            citations={citations}
            citationSources={citationSources}
            isUser={isUser}
            textContentStyle={textContentStyle}
            renderWithMarkdown={true}
            messageId={messageModel.id}
          />
        ) : (
          // If no citations, use MarkdownRenderer directly
          <MarkdownRenderer content={message} className={textContentStyle} isUser={isUser} />
        )}

        {!isUser && (
          <MessageActions
            isHovered={isHovered}
            state={state}
            feedback={(messageModel as RetrievalSessionAnswer).feedback}
            handlers={{
              handleCopy,
              handleThumbsUp: handleFeedbackUp,
              handleThumbsDown: handleFeedbackDown,
            }}
          />
        )}
      </div>
    </div>
  );
};

export default ChatMessage;
