import React from 'react';
import { ImFileWord } from 'react-icons/im';
import { AiOutlineLoading } from 'react-icons/ai';
import clsx from 'clsx';
import { useWordExport } from './hooks/useWordExport';
import {
  EXPORT_BUTTON_STYLES,
  getExportButtonText,
  getExportIconStyle,
} from './utils/messageActionUtils';

interface WordExportButtonProps {
  messageText: string;
}

const WordExportButton: React.FC<WordExportButtonProps> = ({ messageText }) => {
  const { isLoading, error, handleWordExport } = useWordExport();

  const handleWordExportClick = () => {
    if (!isLoading) {
      handleWordExport(messageText);
    }
  };

  if (error) {
    console.error('Word Export Error:', error);
  }

  const currentIconStyle = getExportIconStyle(isLoading, 'Word');
  const currentButtonStyle = clsx(EXPORT_BUTTON_STYLES.base, {
    [EXPORT_BUTTON_STYLES.active]: !isLoading,
    [EXPORT_BUTTON_STYLES.disabled]: isLoading,
  });
  const buttonText = getExportButtonText(isLoading, 'Word');

  return (
    <button
      type="button"
      className={`${currentButtonStyle} font-roboto`}
      onClick={handleWordExportClick}
      title={buttonText}
      disabled={isLoading}
      data-testid="word-export__button"
    >
      {isLoading ? (
        <AiOutlineLoading className={currentIconStyle} />
      ) : (
        <ImFileWord className={currentIconStyle} />
      )}
      <span className="word-export__button-text">{buttonText}</span>
    </button>
  );
};

export default WordExportButton;
