import React, { createContext, useContext, useState } from 'react';
import { WorkbookDashboardData, WorkbookContextType, WorkbookProviderProps } from './workbookTypes';
import { getWorkbookDashboardById, personalWorkbooks } from '@/data/mockWorkbooks';

const WorkbookContext = createContext<WorkbookContextType | undefined>(undefined);

export const useWorkbook = () => {
  const context = useContext(WorkbookContext);
  if (context === undefined) {
    throw new Error('useWorkbook must be used within a WorkbookProvider');
  }
  return context;
};

export const WorkbookProvider: React.FC<WorkbookProviderProps> = ({
  children,
  initialWorkbooks = personalWorkbooks,
}) => {
  const workbooks = initialWorkbooks;
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentWorkbook, setCurrentWorkbook] = useState<WorkbookDashboardData | null>(null);

  const setCurrentWorkbookById = (id: string) => {
    try {
      setLoading(true);
      setError(null);
      // temporary mock data, will be replaced with API calling the future
      const workbookDashboard = getWorkbookDashboardById(id);
      if (workbookDashboard) {
        setCurrentWorkbook(workbookDashboard as WorkbookDashboardData);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load workbook');
      console.error('Error loading workbook:', err);
    } finally {
      setLoading(false);
    }
  };

  const clearCurrentWorkbook = () => {
    setCurrentWorkbook(null);
  };

  const resetWorkbookState = () => {
    setCurrentWorkbook(null);
    setLoading(false);
    setError(null);
  };

  const value = {
    workbooks,
    loading,
    error,
    currentWorkbook,
    setCurrentWorkbookById,
    clearCurrentWorkbook,
    resetWorkbookState,
  };

  return <WorkbookContext.Provider value={value}>{children}</WorkbookContext.Provider>;
}; 
