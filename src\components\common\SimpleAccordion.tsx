import type { SimpleAccordionProps } from "../../types/layout"
import React, { useEffect }from "react";

import { MdArrowDropDown } from "react-icons/md";

import "./SimpleAccordion.scss";

const SimpleAccordion: React.FC<SimpleAccordionProps> = (props: SimpleAccordionProps) => {
  const {
    title,
    children,
    disabled,
    className,
    defaultExpanded,
    headerClassName
  } = props;

  const [isExpanded, setIsExpanded] = React.useState(defaultExpanded)
  const contentRef = React.useRef(null);
  const [contentHeight, setContentHeight] = React.useState(0);


  useEffect(() => {
    if (contentRef.current) {
      setContentHeight(contentRef.current?.['scrollHeight']);
    }
  }, [children]);

  const onExpandedToggle = async () => {
    setIsExpanded(!isExpanded);
  }

  return (
    <div className={`simple-accordion__main ${className ?? ''} ${isExpanded ? 'simple-accordion__main--expanded' : ''} ${disabled ? 'simple-accordion__main--disabled' : ''}`}>
      <div className={`simple-accordion__header ${headerClassName ?? ''}`} onClick={onExpandedToggle}>
        <div className={`simple-accordion__title`}>
          {title}
        </div>
        <div className={`simple-accordion__expand-icon`}>
          <MdArrowDropDown size={24}/>
        </div>
      </div>
      <div className={`simple-accordion__content`} ref={contentRef} style={{ maxHeight: isExpanded ? contentHeight : 0 }}>
        {children}
      </div>
    </div>
  )
}
export default SimpleAccordion;
