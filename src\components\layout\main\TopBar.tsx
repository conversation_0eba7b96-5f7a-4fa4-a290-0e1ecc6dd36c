import React, { useEffect, useRef } from 'react';

import { useThemeStyles } from '@hooks/useThemeStyles';
import { useTopBarPortal } from '@/contexts/TopBarPortalContext';

import FeedbackSection from './FeedbackSection';

import './TopBar.scss';

type TopBarProps = {
  topBarTitle: string;
  topBarIcon: React.ReactNode;
  children?: React.ReactNode;
  sidebarCollapsed?: boolean;
}

export type TopBarHandle = {
  topBarTitle: string;
  topBarIcon: React.ReactNode;
  topBarContent: React.ReactNode;
}

const TopBar: React.FC<TopBarProps> = (props: TopBarProps) => {
  const { classes } = useThemeStyles();
  const contentPortalContainerRef = useRef(null);
  const { setTopBarPortalRef } = useTopBarPortal();

  useEffect(() => {
    setTopBarPortalRef(contentPortalContainerRef.current);
    return () => setTopBarPortalRef(null);
  }, [setTopBarPortalRef]);

  return (
    <div className={`TopBar__main ${classes.text}`}>
      <div className={`TopBar__left ${props.sidebarCollapsed ? 'sidebar-collapsed-adjust' : ''}`}>
        <div className={`TopBar__heading`}>
          {props.topBarIcon && (
            <div className={`TopBar__icon`}>{props.topBarIcon}</div>
          )}
          <div className={`TopBar__title`}>{props.topBarTitle ?? "Untitled"}</div>
        </div>
        {props.children && (
          <div className={`TopBar__content`}>
            {props.children}
          </div>
        )}
        <div className='TopBar__portal' ref={contentPortalContainerRef}></div>
      </div>
      <div className={`TopBar__right`}>
        <FeedbackSection />
      </div>
    </div>
  )
};

export default TopBar;
