import { ChatMessage as ChatSliceMessage } from '@/store/slices/chatSlice';

export type ChatType = 'General' | 'Code' | 'Medical' | 'Policy';
export const chatTypeToUrl: Record<ChatType, string> = {
  General: '/chat',
  Code: '/code',
  Medical: '/med',
  Policy: '/policyweb',
};

export interface MessageActionsState {
  copied: boolean;
}

export interface MessageActionsProps {
  isHovered: boolean;
  feedback?: 0 | 1 | null;
  messageText: string;
  state: MessageActionsState;
  selectedChatType: string;
  handlers: {
    handleCopy: (e: React.MouseEvent) => void;
    handleThumbsUp: (e: React.MouseEvent) => void;
    handleThumbsDown: (e: React.MouseEvent) => void;
  };
}

export interface ChatMessageProps {
  message: ChatSliceMessage;
  handleFeedbackUp: (e: React.MouseEvent) => void;
  handleFeedbackDown: (e: React.MouseEvent) => void;
}

export interface ChatMessageListProps {
  messages: ChatSliceMessage[];
  isLoading: boolean;
  handleFeedbackUp: (messageId: string, sessionId: string) => void;
  handleFeedbackDown: (messageId: string, sessionId: string) => void;
}
