import { ChatMessage } from '@/store/slices/chatSlice';
import { FileInfo } from '@/types/fileUpload';
import { BackendHistoryItem, ConversationTurn } from '@/api/chatHistoryApi';

const MAX_FILE_EXPIRATION_MINUTES = 24 * 60;

// Helper: Converts raw file history item to FileInfo
const toFileInfo = (file: any): FileInfo => {
  const createdDate = new Date(file.created_utc);
  const now = new Date();
  const timeDifference = now.getTime() - createdDate.getTime();
  const minuteDifference = timeDifference / (1000 * 60);
  const isExpired = minuteDifference >= MAX_FILE_EXPIRATION_MINUTES;

  return {
    name: file.name,
    gcs_uri: file.gcs_path,
    type: file.mime_type,
    size: file.file_size_bytes,
    isExpired,
  };
};

// Helper: Parses timestamp string to milliseconds or null
const getTimestampMs = (ts?: string | null): number | null => {
  if (!ts) return null;
  const ms = new Date(ts).getTime();
  return Number.isNaN(ms) ? null : ms;
};

export const processAndAssociateHistory = (sessionData: BackendHistoryItem): ChatMessage[] => {
  /* ------------- Narrow turns to valid timestamps ------------- */
  type TurnWithMs = ConversationTurn & {
    created_utc_ms: number;
  };

  const turnsWithMs: TurnWithMs[] = (sessionData.conversation_history ?? [])
    .map(turn => ({
      ...turn,
      created_utc_ms: getTimestampMs(turn.created_utc),
    }))
    .filter((t): t is TurnWithMs => t.created_utc_ms !== null);

  const userTurns = turnsWithMs.filter(t => t.conversation_role === 'user');
  const otherTurns = turnsWithMs.filter(t => t.conversation_role !== 'user');

  /* ------------------- Prepare files ------------------- */
  const sortedFiles = (sessionData.file_history ?? [])
    .map((file: any) => ({
      ...file,
      effective_timestamp_ms: getTimestampMs(file.updated_utc ?? file.created_utc),
    }))
    .filter(
      (file: any) =>
        file.effective_timestamp_ms !== null &&
        typeof file.name === 'string' &&
        typeof file.gcs_path === 'string' &&
        typeof file.mime_type === 'string' &&
        typeof file.file_size_bytes === 'number' // allow size === 0
    )
    .sort((a, b) => a.effective_timestamp_ms! - b.effective_timestamp_ms!);

  /* ------------------- Associate files ------------------- */
  let fp = 0; // file pointer
  let lastBoundaryMs = Number.NEGATIVE_INFINITY;

  const processedUserMsgs: ChatMessage[] = userTurns.map(turn => {
    const msgMs = turn.created_utc_ms;
    const attached: FileInfo[] = [];

    while (fp < sortedFiles.length) {
      const file = sortedFiles[fp];
      if (file.effective_timestamp_ms! <= msgMs && file.effective_timestamp_ms! > lastBoundaryMs) {
        attached.push(toFileInfo(file));
        fp++;
        continue;
      }
      if (file.effective_timestamp_ms! > msgMs) break;
      fp++; // too old or already processed
    }

    lastBoundaryMs = msgMs;

    const msg: ChatMessage = {
      id: turn.id,
      role: 'user',
      text: turn.message,
      timestamp: turn.created_utc, // original ISO string
      feedback: null,
    };
    if (attached.length) msg.fileInfo = attached;
    return msg;
  });

  /* -------- Attach any remaining files to last user msg -------- */
  if (processedUserMsgs.length && fp < sortedFiles.length) {
    const last = processedUserMsgs[processedUserMsgs.length - 1];
    last.fileInfo = [...(last.fileInfo ?? []), ...sortedFiles.slice(fp).map(toFileInfo)];
  }

  /* ---------------- Combine & sort all messages --------------- */
  const loadedMessages: ChatMessage[] = [
    ...processedUserMsgs,
    ...otherTurns.map(turn => ({
      id: turn.id,
      role: turn.conversation_role as ChatMessage['role'],
      text: turn.message,
      timestamp: turn.created_utc,
      feedback: null,
    })),
  ].sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

  return loadedMessages;
};
