.promptLibrary__main {
    width: 100%;
}

.promptLibrary__tiles {
    width: 100%;
    padding: var(--spacing-spacing-m-3, 24px) var(--spacing-spacing-l-4, 64px);
    display: flex;
    align-items: flex-start;
    align-content: flex-start;
    gap: 36px;
    flex: 1 0 0;
    align-self: stretch;
    flex-wrap: wrap;
}

.promptLibrary__no-results {
    width: 100%;
    text-align: center;
    .promptLibrary__no-results__title {
        color: #FFF;
        font-family: var(--font-sofia-pro);
        font-size: 28px;
        font-weight: 700;
        font-style: normal;
        line-height: 36.4px;
        letter-spacing: 0.224px;
    }
    
    .promptLibrary__no-results__content {
        color: #FFF;
        font-family: var(--font-sofia-pro);
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 22.4px;
        letter-spacing: 0.128px;
    }
}
