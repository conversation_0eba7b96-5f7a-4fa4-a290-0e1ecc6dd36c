import { Action, ThunkAction, configureStore } from '@reduxjs/toolkit';

import authReducer from './slices/authSlice';
import workbookReducer from './slices/workbookSlice';
import promptTemplateReducer from './slices/promptTemplateSlice';
import chatReducer from './slices/chatSlice';

export const store = configureStore({
  reducer: {
    auth: authReducer,
    workbooks: workbookReducer,
    promptTemplates: promptTemplateReducer,
    chat: chatReducer,
  },
});

export type SidekickStore = typeof store;
export type SidekickDispatch = typeof store.dispatch;
export type RootState = ReturnType<typeof store.getState>;
// Reusable type for handwritten thunks
export type SidekickThunk = ThunkAction<void, RootState, unknown, Action>;
