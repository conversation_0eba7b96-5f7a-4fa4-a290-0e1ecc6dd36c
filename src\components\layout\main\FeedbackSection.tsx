import React from 'react';
import { useThemeStyles } from '@/hooks/useThemeStyles';
import { SidekickFeedbackIcon } from '@/components/common/icons';

const FeedbackSection: React.FC = () => {
  const { classes } = useThemeStyles();
  const feedbackFormURL = 'https://forms.office.com/Pages/ResponsePage.aspx?id=cxp9xVwOS0avtwhtxn89RpvoggQIelRKot5WcreWbLtUNk9UN05EV01HNzZXRTZMS1czUDNLNTBXSiQlQCN0PWcu'

  return (
    <a 
      href={feedbackFormURL}
      target="_blank"
      className="flex justify-end items-center gap-[12px] self-stretch"
      aria-label="Give feedback" 
    >
      <span
        className={`
        font-roboto text-[14px] leading-[18px] tracking-[0.144px] whitespace-nowrap
        ${classes.text}
      `}
      >
        How do you like Sidekick?
      </span>
      <button className={`bg-white cursor-pointer ${classes.circleIconButton}`}>
        <SidekickFeedbackIcon />
      </button>
    </a>
  );
};

export default FeedbackSection;
