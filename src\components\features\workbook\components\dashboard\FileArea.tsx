import React from 'react';
import FileCard from './FileCard';
import { FileAreaProps } from '@features/workbook/workbookTypes';

const FileArea: React.FC<FileAreaProps> = ({ files, onDelete }) => {
  if (files.length === 0) return null;

  return (
    <div className="flex w-[800px] p-[12px] justify-start items-center flex-wrap gap-[12px]">
      {files.map((fileName: string, index: number) => (
        <FileCard key={index} fileName={fileName} onDelete={() => onDelete(index)} />
      ))}
    </div>
  );
};

export default FileArea; 
