import { ReactNode } from 'react';
import {
  getThemeColors,
  spacing,
  typography,
  borderRadius,
  shadows,
  animation,
} from '@/config/theme';

export type Theme = 'light' | 'dark';

export interface ThemeContextType {
  theme: Theme;
  isDarkMode: boolean;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
}

export interface ThemeProviderProps {
  children: ReactNode;
}

export interface ThemeToggleProps {
  className?: string;
}

export interface ThemeStylesReturn {
  isDarkMode: boolean;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
  colors: ReturnType<typeof getThemeColors>;
  classes: {
    // Background classes
    background: string;
    backgroundInput: string;
    backgroundSidebar: string;

    border: string;
    borderPrimary: string;
    borderSidebar: string;

    // Text classes
    text: string;
    textMuted: string;
    placeholder: string;

    // Interactive states
    hoverBackground: string;
    focusRing: string;

    // Combined utility classes for common components
    button: string;
    buttonSecondary: string;
    buttonGhost: string;
    iconButton: string;
    circleIconButton: string;
    input: string;
    card: string;
    sidebar: string;
    rightPanel: string;

    // Sidebar specific classes
    sidebarText: string;
    sidebarSubItemText: string;
    sidebarBorder: string;
    sidebarIconHover: string;
  };
  // Simplified design tokens
  tokens: {
    spacing: typeof spacing;
    typography: typeof typography;
    borderRadius: typeof borderRadius;
    shadows: typeof shadows;
    animation: typeof animation;
  };
}
