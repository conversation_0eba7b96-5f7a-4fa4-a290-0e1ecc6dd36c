import React from 'react';
import { createPortal } from 'react-dom';
// import { useTopBarPortal } from '@/contexts/TopBarPortalContext';
import { useTopBarPortal } from '@/contexts/TopBarPortalContext';

const TopBarPortal = ({ children }: { children: React.ReactNode }) => {
  const { topBarPortalRef } = useTopBarPortal();

  // Only render if the portal target exists
  if (!topBarPortalRef) return null;

  return createPortal(children, topBarPortalRef);
};

export default TopBarPortal;
