import { CreatePromptTemplate, SessionType, UpdatePromptTemplate } from '@/types';

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '@/store/hooks';

import {
  PromptTemplateInfo,
  createMyPromptTemplate,
  createPublicPromptTemplate,
  fetchMyPromptTemplates,
  fetchGlobalPromptTemplates,
  selectAllUserPromptTemplates,
  selectAllGlobalPromptTemplates,
  updatePromptTemplateById,
  removePromptTemplateById,
} from '@/store/slices/promptTemplateSlice';

import { selectCurrentUser, selectCurrentUserRoles } from '@/store/slices/authSlice';

import TopBarPortal from '@/components/layout/main/TopBarPortal';
import PromptTemplateTile from '../components/PromptTemplateTile';
import PromptLibraryEditTemplateModal from '../components/modals/PromptLibraryEditTemplateModal';
import PromptLibraryRunTemplateModal from '../components/modals/PromptLibraryRunTemplateModal';
import PromptLibraryDeleteModal from '../components/modals/PromptLibraryDeleteModal';
import PromptLibraryCreateTemplateModal from '../components/modals/PromptLibraryCreateTemplateModal';
import PromptLibrarySearchBar from '../components/PromptLibrarySearchBar/PromptLibrarySearchBar';

import { showToast } from '@/components/common/ToastNotification/ToastNotification';

import './PromptLibraryView.scss';
import AddPromptTemplateTile from '../components/AddPromptTemplateTile';
export interface PromptLibraryViewProps {
  isGlobal: boolean;
}

const PromptLibraryView: React.FC<PromptLibraryViewProps> = (props: PromptLibraryViewProps) => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const { isGlobal } = props;
  const selectAllPromptTemplates = isGlobal
    ? selectAllGlobalPromptTemplates
    : selectAllUserPromptTemplates;
  const promptTemplates = useAppSelector(selectAllPromptTemplates);

  const [activeTemplate, setActiveTemplate] = useState<PromptTemplateInfo | null>(null);

  // keyword based Search Filter State
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredPromptTemplates, setFilteredPromptTemplates] = useState<PromptTemplateInfo[]>([]);

  // Modal states
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isRunModalOpen, setIsRunModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  // state for Edit Modal fields
  const [editedTitle, setEditedTitle] = useState('');
  const [editedContent, setEditedContent] = useState('');
  const [editedPromptSystemInstruction, setEditedPromptSystemInstruction] = useState('');
  const [editedPromptSessionType, setEditedPromptSessionType] = useState<SessionType | null>(null);

  const currentUser = useAppSelector(selectCurrentUser);
  const currentUserRoles = useAppSelector(selectCurrentUserRoles);

  useEffect(() => {
    setSearchQuery('');
  }, [isGlobal]);

  useEffect(() => {
    const fetchPromptTemplates = isGlobal ? fetchGlobalPromptTemplates : fetchMyPromptTemplates;
    dispatch(fetchPromptTemplates()).unwrap();
  }, [isGlobal, dispatch]);

  // Filter templates when search query or templates change
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredPromptTemplates(promptTemplates);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = promptTemplates.filter(
        template =>
          template.promptTemplate.name.toLowerCase().includes(query) ||
          template.promptTemplate.prompt.toLowerCase().includes(query)
      );
      setFilteredPromptTemplates(filtered);
    }
  }, [searchQuery, promptTemplates]);

  // Reset state when active template changes
  useEffect(() => {
    if (activeTemplate) {
      setEditedTitle(activeTemplate.promptTemplate.name);
      setEditedContent(activeTemplate.promptTemplate.prompt);
      setEditedPromptSystemInstruction(activeTemplate.promptTemplate.systemInstructions || '');
      setEditedPromptSessionType(activeTemplate.promptTemplate.sessionType);
    }
  }, [activeTemplate]);

  // Search handler
  const handleSearch = (value: string) => {
    setSearchQuery(value);
  };

  // Modal Open handlers
  const handleOpenEditModal = (template: PromptTemplateInfo) => {
    if (!isGlobal || (isGlobal && currentUser === template.promptTemplate.author)) {
      setActiveTemplate(template);
      setIsEditModalOpen(true);
    }
  };

  const handleOpenRunModal = (template: React.SetStateAction<PromptTemplateInfo | null>) => {
    setActiveTemplate(template);
    setIsRunModalOpen(true);
  };

  const handleOpenDeleteModal = (template: React.SetStateAction<PromptTemplateInfo | null>) => {
    if (
      !isGlobal ||
      (isGlobal &&
        (currentUserRoles.includes('admin') || currentUserRoles.includes('Sidekick-CoreTeam')))
    ) {
      setActiveTemplate(template);
      setIsDeleteModalOpen(true);
    }
  };

  const handleOpenCreateModal = () => {
    if (
      !isGlobal ||
      (isGlobal &&
        (currentUserRoles.includes('admin') || currentUserRoles.includes('Sidekick-CoreTeam')))
    ) {
      setIsCreateModalOpen(true);
    }
  };

  // Modal close handlers
  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
    setActiveTemplate(null);
  };

  const handleCloseRunModal = () => {
    setIsRunModalOpen(false);
    setActiveTemplate(null);
  };

  const handleCloseDeleteModal = () => {
    setIsDeleteModalOpen(false);
    // Don't reset activeTemplate here if coming from edit modal
    if (!isEditModalOpen) {
      setActiveTemplate(null);
    }
  };

  const handleCloseCreateModal = () => {
    setIsCreateModalOpen(false);
  };

  // Action handlers
  // Case 1A: Save prompt changes
  const handleSavePrompt = async () => {
    if (!activeTemplate) {
      return;
    }

    try {
      const updateData: UpdatePromptTemplate = {
        id: activeTemplate.promptTemplate.id,
        name: editedTitle ?? null,
        prompt: editedContent ?? null,
        systemInstructions: editedPromptSystemInstruction ?? null,
        sessionType: editedPromptSessionType ?? activeTemplate.promptTemplate.sessionType,
      };
      await dispatch(
        updatePromptTemplateById({ promptTemplateUpdates: updateData, isGlobal })
      ).unwrap();
      setIsEditModalOpen(false);
      setActiveTemplate(null);
      showToast.positive('Prompt Updated!', 'Access this updated prompt in the Prompt Library!');
    } catch (error) {
      showToast.error('Prompt Update Failed!', 'Failed to update prompt template, Please retry!');
      console.error('Error updating prompt template:', error);
    }
  };

  const handleSaveAndRunPrompt = async () => {
    if (!activeTemplate) {
      return;
    }

    try {
      const updateData: UpdatePromptTemplate = {
        id: activeTemplate.promptTemplate.id,
        name: editedTitle ?? null,
        prompt: editedContent ?? null,
        systemInstructions: editedPromptSystemInstruction ?? null,
        sessionType: editedPromptSessionType ?? activeTemplate.promptTemplate.sessionType,
      };
      
      await dispatch(
        updatePromptTemplateById({ promptTemplateUpdates: updateData, isGlobal })
      ).unwrap();
      showToast.positive('Prompt Updated!', 'Access this updated prompt in the Prompt Library!');
      setIsEditModalOpen(false);

      const routePath = getRoutePathBySessionType(updateData.sessionType ?? activeTemplate.promptTemplate.sessionType);
      navigate(routePath, {
        replace: true,
        state: {
          newSession: true,
          initialProps: editedContent,
          promptTemplate: updateData,
          promptOverride: editedContent,
          promptTemplateIsGlobal: isGlobal,
        },
      });
      setActiveTemplate(null);
    } catch (error) {
      showToast.error('Prompt Update Failed!', 'Failed to update prompt template, Please retry!');
      console.error('Error updating prompt template:', error);
    }
  };

  // Case1C & 2: Delete prompt
  const handleDeletePrompt = async () => {
    if (!activeTemplate) {
      showToast.error('Prompt Deletion Failed!', 'No template selected for deletion!');
      return;
    }

    try {
      await dispatch(
        removePromptTemplateById({
          promptTemplateId: activeTemplate.promptTemplate.id!,
          isGlobal: isGlobal!,
        })
      ).unwrap();

      setIsDeleteModalOpen(false);
      setIsEditModalOpen(false);
      setActiveTemplate(null);
      showToast.positive('Prompt Deleted!', 'Prompt template deleted successfully!');
    } catch (error) {
      showToast.error('Prompt Deletion Failed!', 'Failed to delete prompt template!');
      console.error('Error deleting prompt template:', error);
    }
  };

  // Case 3: Run Prompt
  const handleRunPrompt = (promptOverride: string) => {
    if (!activeTemplate) {
      showToast.error('Prompt Run Failed!', 'No template selected!');
      return;
    }
    setIsRunModalOpen(false);

    const routePath = getRoutePathBySessionType(activeTemplate.promptTemplate.sessionType);
    navigate(routePath, {
      replace: true,
      state: {
        promptTemplate: activeTemplate?.promptTemplate,
        promptOverride: promptOverride,
        promptTemplateIsGlobal: isGlobal,
      },
    });
    setActiveTemplate(null);
  };

  // TODO: Currently as per design SessionType (code, chat, medlm) are not taken into account while creation of Prompt template
  // TODO: Need to be reviewed by team
  const handleCreateAndRunPromptTemplate = async (promptTemplate: CreatePromptTemplate) => {
    if (promptTemplate) {
      try {
        const createPromptTemplate =
          isGlobal && currentUserRoles.some(r => r === 'Sidekick-CoreTeam')
            ? createPublicPromptTemplate
            : createMyPromptTemplate;
        const createTemplateResponse = await dispatch(
          createPromptTemplate(promptTemplate)
        ).unwrap();
        const newTemplate = createTemplateResponse.promptTemplate;
        setIsCreateModalOpen(false);

        const routePath = getRoutePathBySessionType(newTemplate.sessionType);
        navigate(routePath, {
          replace: true,
          state: {
            promptTemplate: newTemplate,
            promptOverride: newTemplate.prompt,
            promptTemplateIsGlobal: isGlobal,
          },
        });
        setActiveTemplate(null);
      } catch (error) {
        showToast.error('Prompt Creation Failed!', `Failed to create Prompt "${promptTemplate.name}"`);
      }
    }
  };

  const handleCreatePromptTemplate = async (promptTemplate: CreatePromptTemplate) => {
    if (promptTemplate) {
      try {
        const createPromptTemplate =
          isGlobal && currentUserRoles.some(r => r === 'Sidekick-CoreTeam')
            ? createPublicPromptTemplate
            : createMyPromptTemplate;
        const createTemplateResponse = await dispatch(
          createPromptTemplate(promptTemplate)
        ).unwrap();
        const newTemplate = createTemplateResponse.promptTemplate;
        setIsCreateModalOpen(false);
        showToast.positive(
          'Prompt Created!',
          `Prompt "${newTemplate.name}" was successfully created!`
        );
      } catch (error) {
        console.error('Prompt Creation Failed', error);
        showToast.error(
          'Prompt Creation Failed!',
          `Failed to create Prompt "${promptTemplate.name}"`
        );
      }
    }
  };

  // TODO: Adhoc Implementation for Navigation to appropriate chat session type
  // TODO: To be reviewed by team
  const getRoutePathBySessionType = (sessionType: SessionType) => {
    switch (sessionType) {
      case SessionType.CODE:
        return '/code';
      case SessionType.MDLM:
        return '/med';
      case SessionType.CHAT:
      default:
        return '/chat';
    }
  };

  // Open delete modal from edit modal
  const handleOpenDeleteFromEdit = () => {
    setIsDeleteModalOpen(true);
  };

  return (
    <div className="promptLibrary__main">
      <TopBarPortal>
        <PromptLibrarySearchBar onSearch={handleSearch} searchQuery={searchQuery} />
      </TopBarPortal>
      <div className={`promptLibrary__tiles`}>
        {filteredPromptTemplates.length > 0 ? (
          filteredPromptTemplates.map(pt => (
            <PromptTemplateTile
              promptTemplateInfo={pt}
              isGlobal={isGlobal}
              currentUser={currentUser}
              author={pt.promptTemplate.author}
              key={pt.promptTemplate.id}
              onTileClick={() => handleOpenEditModal(pt)}
              onRunClick={() => handleOpenRunModal(pt)}
              onDeleteClick={() => handleOpenDeleteModal(pt)}
            />
          ))
        ) : (
          <>
            {searchQuery && (
              <div className='promptLibrary__no-results'>
                <h3 className="promptLibrary__no-results__title">0 results for {searchQuery}</h3>
                <p className="promptLibrary__no-results__content">Hmm... I couldn't find any prompts.</p>
              </div>
            )}
          </>
        )}
        {(!isGlobal || (isGlobal && (currentUserRoles.includes('admin') || currentUserRoles.includes('Sidekick-CoreTeam')))) && (
          <AddPromptTemplateTile onClick={() => handleOpenCreateModal()} isGlobal={isGlobal} />
        )}
      </div>

      {/* Edit Modal */}
      {activeTemplate && (
        <PromptLibraryEditTemplateModal
          isOpen={isEditModalOpen}
          onOpenChange={handleCloseEditModal}
          onSavePromptTemplate={handleSavePrompt}
          onSaveAndRunPromptTemplate={handleSaveAndRunPrompt}
          onDeletePromptTemplateFromEdit={handleOpenDeleteFromEdit}
          promptName={editedTitle}
          promptContent={editedContent}
          promptSystemInstruction={editedPromptSystemInstruction}
          promptSessionType={editedPromptSessionType || activeTemplate.promptTemplate.sessionType}
          onPromptNameChange={setEditedTitle}
          onPromptContentChange={setEditedContent}
          onPromptSystemInstructionChange={setEditedPromptSystemInstruction}
          onPromptSessionTypeChange={setEditedPromptSessionType}
        />
      )}

      {/* Run Modal */}
      {activeTemplate && (
        <PromptLibraryRunTemplateModal
          isOpen={isRunModalOpen}
          onOpenChange={handleCloseRunModal}
          onRunPromptTemplate={handleRunPrompt}
          promptName={activeTemplate.promptTemplate.name}
          promptContent={activeTemplate.promptTemplate.prompt}
        />
      )}

      {/* Delete Modal */}
      {activeTemplate && (
        <PromptLibraryDeleteModal
          isOpen={isDeleteModalOpen}
          onOpenChange={handleCloseDeleteModal}
          onDeletePromptTemplate={handleDeletePrompt}
        />
      )}

      {/* Create Modal */}
      <PromptLibraryCreateTemplateModal
        isOpen={isCreateModalOpen}
        onOpenChange={handleCloseCreateModal}
        onCreateAndRunPromptTemplate={handleCreateAndRunPromptTemplate}
        onCreatePromptTemplate={handleCreatePromptTemplate}
      />
    </div>
  );
};

export default PromptLibraryView;
