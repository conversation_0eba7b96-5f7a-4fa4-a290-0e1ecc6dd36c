import React, { useState } from 'react';
import { RouterProvider } from 'react-router-dom';
import routes from './routes';
import { ThemeProvider } from '@contexts/ThemeContext';
import { TopBarPortalProvider } from '@/contexts/TopBarPortalContext';
import { WorkbookProvider } from '@/components/features/workbook';
import { personalWorkbooks, publicWorkbooks } from '@data/mockWorkbooks';
import { SidebarContext } from '@/contexts/SidebarContext';

const App: React.FC = () => {
  const [isSidebarExpanded, setIsSidebarExpanded] = useState(true);

  const handleToggleSidebar = () => {
    setIsSidebarExpanded(prev => !prev);
  };

  // Combine personal and public workbooks, removing duplicates by ID
  const allWorkbooks = [
    ...personalWorkbooks,
    ...publicWorkbooks.filter(publicWb => !personalWorkbooks.some(myWb => myWb.id === publicWb.id)),
  ];

  return (
    <ThemeProvider>
      <TopBarPortalProvider>
        <SidebarContext.Provider value={{ toggleSidebar: handleToggleSidebar, isSidebarExpanded }}>
          <WorkbookProvider initialWorkbooks={allWorkbooks}>
            <RouterProvider router={routes} />
          </WorkbookProvider>
        </SidebarContext.Provider>
      </TopBarPortalProvider>
    </ThemeProvider>
  );
};

export default App;
