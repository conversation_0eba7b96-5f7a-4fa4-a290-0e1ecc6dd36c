.add-files-main {
    height: 45px;
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 24px;
    margin-top: 12px;
    .add-files-button {
        height: 100%;
        display: flex;
        margin-right: 24px;
        .add-files-label {
            display: flex;
            align-items: center;
            justify-content: center;
            .add-files-label-text {
                width: 65px;
            }
        }
    }

    .add-files-file-list {
        display: flex;
        align-items: center;
        flex: 4;
        overflow-x: auto;
        border-top: 8px solid #002D4F;
        padding-bottom: 4px;

        .add-files-button {
            height: 100%;
            display: flex;
            margin-right: 24px;
            .add-files-label {
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
        &::-webkit-scrollbar {
            height: 6px;
        }
        &::-webkit-scrollbar-track {
            border-radius: calc(infinity * 1px);
            background-color: #002D4F;
        }
        &::-webkit-scrollbar-thumb {
            border-radius: 4px;
        }
        .add-files-file {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            margin-right: 16px;
            .add-files-file-icon {
                display: flex;
                align-items: center;
                position: relative;
                top: 2px;
                margin-right: 4px;
            }
            .add-files-file-name {
                display: flex;
                align-items: center;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                margin-right: 16px;
            }
            .add-files-file-delete {
                display: flex;
                align-items: center;
                position: relative;
                top: 2px;
            }
        }
    }
}

.chunksize-radio-group__main {
    cursor: default;
    display: flex;
    flex-direction: column;
    align-items: start;
    gap: 0.25rem;
  }
  
  .chunksize-radio-group__title {
    font-weight: 500;
  }
  
  .chunksize-radio-group__label {
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .chunksize-radio-group__item {
    box-sizing: border-box;
    display: flex;
    width: 1rem;
    height: 1rem;
    align-items: center;
    justify-content: center;
    border-radius: 100%;
    outline: 0;
    padding: 0;
    margin: 0;
    border: 2px solid hsl(205, 100%, 35%);
  
    &[data-unchecked] {
      border: 2px solid hsl(205, 100%, 35%);
      background-color: transparent;
    }
  
    &[data-checked] {
      background-color: var(--color-gray-900);
    }
  
    &:focus-visible {
      outline: 2px solid var(--color-blue);
      outline-offset: 2px;
    }
  }
  
  .chunksize-radio-group__indicator {
    display: flex;
    align-items: center;
    justify-content: center;
  
    &[data-unchecked] {
      display: none;
    }
  
    &::before {
      content: '';
      border-radius: 100%;
      width: 0.5rem;
      height: 0.5rem;
      background-color: hsl(205, 100%, 70%);
    }
  }

.create-workbook__container {
  .create-workbook__main {
    padding: 32px;
    .create-workbook__actions {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 32px;
      .create-workbook__actions-cancel {
    
      }
      .create-workbook__actions-create {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        border: 2px solid #0066B1;
        border-radius: calc(infinity * 1px);
        padding: 8px 16px;
        cursor: pointer;
        &:hover {
          background-color: #0066B1;
        }
        &.disabled {
          opacity: 0.5;
          cursor: not-allowed;
          &:hover {
            background-color: transparent;
          }
        }
      }
    }
  }
  .create-workbook__footer {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 32px;
    border-top: 1px solid #0066B1;
    font-family: var(--font-roboto);
    font-size: 14px;
    font-weight: 400;
    .create-workbook__footer-icon {

    }
  }
}
