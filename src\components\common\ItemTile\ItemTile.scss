.itemTile__wrapper {
    display: block;
    text-decoration-line: none;
}

.itemTile {
    display: flex;
    width: 240px;
    height: 280px;
    border: 1px solid var(--border-color-border-interactive);
    border-radius: var(--border-radius-border-radius-xl);
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.15s ease-in-out;
    box-shadow: 0px 8px 16px -4px var(--shadow-shadow-interactive);
    &:hover {
        transform: scale(1.05);
    }
    .itemTile__spine {
        display: flex;
        width: 24px;
        padding: 10px;
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
        align-self: stretch;
        background: var(--elements-on-surface-onsurface-brand);
    }

    .itemTile__content {
        width: 100%;
        height: 100%;
    }
}
