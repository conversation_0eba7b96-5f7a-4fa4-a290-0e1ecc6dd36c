import React, { useState } from "react";
import { Dialog } from '@base-ui-components/react/dialog';
import { IoClose } from "react-icons/io5";

import useThemeStyles from '@/hooks/useThemeStyles';

import './PromptLibraryRunTemplateModal.scss';

interface PromptLibraryRunTemplateModalProps {
  isOpen: boolean;
  promptName: string;
  promptContent: string;
  onOpenChange: (value: boolean) => void;
  onRunPromptTemplate: (promptOverride: string) => void;
}

const PromptLibraryRunTemplateModal: React.FC<PromptLibraryRunTemplateModalProps> = ({
  isOpen,
  promptName,
  promptContent,
  onOpenChange,
  onRunPromptTemplate,
}) => {

  const { classes } = useThemeStyles();
  const [promptRunText, setPromptRunText] = useState(promptContent);

  const handlePromptRunTextUpdate = async (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    const updatedText = event.currentTarget.value;
    setPromptRunText(updatedText);
  }

  const handleRunPrompt = async () => {
    await onRunPromptTemplate(promptRunText);
  }

  return (
    <Dialog.Root open={isOpen} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Backdrop className={`promptLibraryRunTemplateModal__backdrop ${classes.text}`} />
        <Dialog.Popup className="promptLibraryRunTemplateModal__popup">
          <div className="promptLibraryRunTemplateModal__popup__header">
            <h4 className="promptLibraryRunTemplateModal__popup__header__text">{promptName}</h4>
            <IoClose
              className="promptLibraryRunTemplateModal__popup__header__close-button"
              onClick={() => onOpenChange(false)}
            />
          </div>
          <div className="promptLibraryRunTemplateModal__popup__prompt-content">
            <textarea
              className="promptLibraryRunTemplateModal__popup__prompt-content__text"
              value={promptRunText}
              onChange={handlePromptRunTextUpdate}
            >
              {promptRunText}
            </textarea>
          </div>
          <div className="promptLibraryRunTemplateModal__popup__action-container">
            <button
              className="promptLibraryRunTemplateModal__popup__action-container__action-button"
              onClick={handleRunPrompt}
            >
              <div className="promptLibraryRunTemplateModal__popup__action-container__action-button__content-container">
                <span className="promptLibraryRunTemplateModal__popup__action-container__action-button__content-run-icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                  >
                    <path
                      d="M19.5 14.5039L13.5 20.5039L12.08 19.0839L15.67 15.5039H4.5V3.50391H6.5V13.5039H15.67L12.08 9.92391L13.5 8.50391L19.5 14.5039Z"
                      fill="white"
                    />
                  </svg>
                </span>
                <p className="promptLibraryRunTemplateModal__popup__action-container__action-button__content-run-text">
                  Run Prompt
                </p>
              </div>
            </button>
          </div>
        </Dialog.Popup>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export default PromptLibraryRunTemplateModal;
