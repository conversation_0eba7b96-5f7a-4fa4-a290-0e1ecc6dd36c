import { useState, useCallback } from 'react';
import { exportToPowerPoint } from '@/api/powerPointExportApi';

interface UsePowerPointExportState {
  isLoading: boolean;
  error: string | null;
}

interface UsePowerPointExportResult extends UsePowerPointExportState {
  handlePowerPointExport: (messageContent: string, template: string) => Promise<void>;
}

export const usePowerPointExport = (): UsePowerPointExportResult => {
  const [state, setState] = useState<UsePowerPointExportState>({
    isLoading: false,
    error: null,
  });

  const handlePowerPointExport = useCallback(async (messageContent: string, template: string) => {
    setState({ isLoading: true, error: null });
    try {
      const result = await exportToPowerPoint(messageContent, template);
      if (result.download_url) {
        window.open(result.download_url, '_blank', 'noopener,noreferrer');
        setState({ isLoading: false, error: null }); // Reset on success
      } else if (result.error) {
        setState({ isLoading: false, error: result.error });
        console.error('PowerPoint export failed:', result.error);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred.';
      setState({ isLoading: false, error: errorMessage });
      console.error('Error in handlePowerPointExport:', errorMessage);
    }
  }, []);

  return {
    ...state,
    handlePowerPointExport,
  };
};
