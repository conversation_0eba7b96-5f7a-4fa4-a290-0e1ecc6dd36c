import { useState } from 'react';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  addMessage,
  setError,
  setSessionIds,
  selectSelectedPolicyLocation,
  selectCurrentSessionId,
  setChatIsLoading,
} from '@/store/slices/chatSlice';
import {
  sendPolicyQuery,
  PolicyWebApiResponse,
  PolicyWebQueryPayload,
  PolicyRecord,
} from '@/api/policyWebApi';
import { v4 as uuidv4 } from 'uuid';

// Helper to convert policy records to a Markdown table string
const formatPolicyRecordsToMarkdown = (records: PolicyRecord[]): string => {
  if (!records || records.length === 0) return '';

  const headers = [
    'Content_ID',
    'Title',
    'Applicability',
    'Responsible_Area',
    'Content_Type',
    'Owner',
    'Overview_Statement',
    'Last_Approved_Date',
    'Effective_Date',
  ];

  let markdownTable = '\n| ' + headers.map(header => header.replace(/_/g, ' ')).join(' | ') + ' |\n';
  
  markdownTable += '| ' + headers.map(() => '---').join(' | ') + ' |\n';

  records.forEach(record => {
    const row = headers
      .map(header => {
        let cellValue = String(record[header as keyof PolicyRecord] || '').replace(/\r\n|\r|\n/g, ' ');

        if (header === 'Content_ID' && record.PolicyWeb_Link) {
          const link = record.PolicyWeb_Link.trim();
          if (link && link !== '#') {
            const linkText = String(record[header as keyof PolicyRecord] || '')
              .replace(/\r\n|\r|\n/g, ' ')
              .replace(/\|/g, '\\|');
            return `[${linkText}](${link})`;
          }
        }
        if (header === 'Overview_Statement' && cellValue.trim().length > 0) {
          cellValue = cellValue.trim() + '...';
        }

        return cellValue.replace(/\|/g, '\\|');
      })
      .join(' | ');
    markdownTable += `| ${row} |
`;
  });

  return markdownTable;
};

interface UsePolicyWebChatReturn {
  submitPolicyQuery: (prompt: string) => Promise<void>;
  policyError: string | null;
}

export const usePolicyWebChat = (): UsePolicyWebChatReturn => {
  const dispatch = useAppDispatch();
  const [policyError, setPolicyError] = useState<string | null>(null);

  const selectedLocation = useAppSelector(selectSelectedPolicyLocation);
  const policySessionId = useAppSelector(selectCurrentSessionId);

  const submitPolicyQuery = async (prompt: string) => {
    if (!selectedLocation) {
      dispatch(setError('Please select a policy location before sending a query.'));
      return;
    }
    if (!prompt.trim()) {
      dispatch(setError('Please enter a query.'));
      return;
    }

    dispatch(setChatIsLoading(true));
    setPolicyError(null);

    dispatch(
      addMessage({
        id: uuidv4(),
        role: 'user',
        text: prompt,
        timestamp: new Date().toISOString(),
      })
    );

    const payload: PolicyWebQueryPayload = {
      location: selectedLocation,
      user_input: prompt,
      session_id: policySessionId,
    };

    try {
      const result: PolicyWebApiResponse = await sendPolicyQuery(payload);

      if (result.response_success && result.response) {
        let responseText = result.response.qa_response;
        if (result.response.policies && result.response.policies.length > 0) {
          const tableMarkdown = formatPolicyRecordsToMarkdown(result.response.policies);
          if (responseText.trim().length > 0) {
            responseText += '\n\n' + tableMarkdown;
          } else {
            responseText = tableMarkdown;
          }
        }

        dispatch(
          addMessage({
            id: result.prompt_id || uuidv4(),
            role: 'model',
            text: responseText,
            timestamp: new Date().toISOString(),
            isPolicyContext: true,
            // Store PolicyWeb data for feedback
            policyWebData: {
              sessionId: result.session_id,
              responseId: result.prompt_id,
              responseClass: result.response_class,
            },
          })
        );

        if (result.session_id) {
          if (result.session_id !== policySessionId) {
            dispatch(
              setSessionIds({
                sessionId: result.session_id
              })
            );
          }
        }
      } else {
        const errorMsg = result.message || 'Failed to get a successful response from PolicyWeb.';
        setPolicyError(errorMsg);
        dispatch(setError(errorMsg));
        dispatch(
          addMessage({
            id: uuidv4(),
            role: 'system',
            text: `Error: ${errorMsg}`,
            timestamp: new Date().toISOString(),
            isPolicyContext: true,
          })
        );
      }
    } catch (e: any) {
      const errorMsg = e.message || 'An unexpected error occurred while sending policy query.';
      setPolicyError(errorMsg);
      dispatch(setError(errorMsg));
      dispatch(
        addMessage({
          id: uuidv4(),
          role: 'system',
          text: `Error: ${errorMsg}`,
          timestamp: new Date().toISOString(),
        })
      );
    } finally {
      dispatch(setChatIsLoading(false));
    }
  };

  return {
    submitPolicyQuery,
    policyError,
  };
};
