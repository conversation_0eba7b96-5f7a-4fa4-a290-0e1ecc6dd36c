import { client } from '@/api/client';

import {
  TTS<PERSON>arams,
  TTSDownloadResponse,
  TTSLanguagesJSON,
} from '@/components/features/text_to_speech/textToSpeechTypes';

const { hostname, pathname } = window.location;
const baseHost = hostname.includes('localhost') ? 'http://localhost:5000' : ``;
let baseApiPath = '/sidekick';
const apiPathPrefix = pathname.split(baseApiPath)[0];
baseApiPath = `${apiPathPrefix}${baseApiPath}`;

const baseRoute = `${baseHost}${baseApiPath}/restful`;

const staticRoute = `${baseHost}${baseApiPath}/static/assets`;

export const getLanguageAndVoices = async () => {
  const response = await client.get<TTSLanguagesJSON>(`${staticRoute}/languages.json`, {
    headers: {
      Accept: 'application/json',
    },
  });
  return response.data;
};

export const playAudio = async (body: TTSParams) => {
  // const response = await client.post<Blob>(
  //   `${baseRoute}/synthesizeSpeech-speech`,
  //   body
  // );
  const headers = { 'Content-Type': 'application/json' };
  const config: RequestInit = {
    method: 'POST',
    headers: headers,
    body: JSON.stringify(body),
  };
  let data;
  try {
    const response = await window.fetch(`${baseRoute}/synthesizeSpeech-speech`, config);
    if (response.ok) {
      data = await response.blob();
      return data;
    }
    throw new Error(response.statusText);
  } catch (err: any) {
    return Promise.reject(err.message ? err.message : data);
  }
};

export const downloadAudio = async (body: TTSParams) => {
  const response = await client.post<TTSDownloadResponse>(`${baseRoute}/download-speech`, body);
  return response.data;
};
