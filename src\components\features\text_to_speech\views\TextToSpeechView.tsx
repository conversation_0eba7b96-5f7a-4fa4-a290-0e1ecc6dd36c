import React, { useState, useEffect, useRef, useMemo } from 'react';
import SidekickDropDown from '@common/SidekickDropDown';
import AdvancedSettings from '../components/AdvancedSettings';
import SidekickValueSlider from '@common/SidekickValueSlider';
import DownloadIcon from '@common/icons/DownloadIcon';
import PlayIcon from '@common/icons/PlayIcon';
import { getLanguageAndVoices, playAudio, downloadAudio } from '@/api/textToSpeechApi';
import './TextToSpeechView.scss';

import { TTSLanguagesJSON, TTSParams } from '../textToSpeechTypes';
import { showToast } from '@/components/common/ToastNotification/ToastNotification';
import { FaPauseCircle } from 'react-icons/fa';

const getLanguages = (data: TTSLanguagesJSON | null) => {
  if (data === null) {
    return [];
  }
  const languagesData = data.languages;
  const simplifiedLanguages = languagesData.map(language => ({
    name: language.name,
    value: language.code,
  }));

  return simplifiedLanguages;
};

const getVoices = (data: TTSLanguagesJSON | null, language: string) => {
  if (data === null) {
    return [];
  }
  const languagesData = data.languages;
  const selectedLanguage = languagesData.find(lang => lang.code === language);

  if (selectedLanguage && selectedLanguage.voices) {
    return selectedLanguage.voices.map(voice => ({
      name: voice,
      value: voice,
    }));
  }
  return [{ name: '', value: '' }];
};

const getEncodings = () => {
  return [
    { name: 'MP3', value: 'MP3' },
    { name: 'Linear16', value: 'Linear16' },
    { name: 'Mu-law', value: 'Mu-law' },
    { name: 'Ogg Opus', value: 'Ogg Opus' },
  ];
};

const TextToSpeechView: React.FC = () => {
  const audioRef = useRef<HTMLAudioElement | null>(null);

  const byteLimit = 5000;
  const defaults: TTSParams = {
    message: '',
    language: 'en-US',
    voice: 'en-US-Casual-K --Male',
    encoding: 'MP3',
    speed: 1,
    pitch: 0,
  };

  const [languagesData, setLanguagesData] = useState<TTSLanguagesJSON | null>(null); //from backend
  const [message, setMessage] = useState(defaults.message);
  const [language, setLanguage] = useState(defaults.language);
  const [voice, setVoice] = useState(defaults.voice);
  const [encoding, setEncoding] = useState(defaults.encoding);
  const [speed, setSpeed] = useState(defaults.speed);
  const [pitch, setPitch] = useState(defaults.pitch);
  const [downloadDisabled, setDownloadDisabled] = useState(true);
  const [playbackDisabled, setPlaybackDisabled] = useState(true);
  const [audioState, setAudioState] = useState<'reset' | 'idle' | 'playing' | 'paused'>('reset');
  const [byteCount, setByteCount] = useState(0);

  const payload: TTSParams = {
    message: message.trim(),
    language: language,
    voice: voice.split(' --')[0],
    encoding: encoding,
    speed: speed,
    pitch: pitch,
  };

  useEffect(() => {
    const getLAV = async () => {
      const LAV = await getLanguageAndVoices();
      setLanguagesData(LAV);
    };
    getLAV();
  }, []);

  const availableVoices = useMemo(() => {
    return getVoices(languagesData, language);
  }, [languagesData, language]);
  const encoder = useMemo(() => new TextEncoder(), []);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    let inputValue = e.target.value;
    const byteLength = encoder.encode(inputValue).length;

    if (byteLength <= byteLimit) {
      setMessage(inputValue);
      setByteCount(byteLength);
    } else {
      let start = 0;
      let end = inputValue.length;

      while (start < end) {
        const mid = Math.floor((start + end + 1) / 2);
        const testText = inputValue.substring(0, mid);
        const testByteLength = encoder.encode(testText).length;

        if (testByteLength <= byteLimit) {
          start = mid;
        } else {
          end = mid - 1;
        }
      }
      inputValue = inputValue.substring(0, start);
      setMessage(inputValue);
      setByteCount(encoder.encode(inputValue).length);
    }

    const disable = !Boolean(inputValue.trim().length);
    setDownloadDisabled(disable);
    setPlaybackDisabled(disable);
    setAudioState('reset');
  };

  const handleLanguageChange = (value: string) => {
    if (languagesData === null) {
      return null;
    }
    setLanguage(value);
    const newAvailableVoices = getVoices(languagesData, value);

    if (newAvailableVoices.length > 0) {
      setVoice(newAvailableVoices[0]['value']);
    }
    setAudioState('reset');
  };

  const handleVoiceChange = (value: string) => {
    setVoice(value);
    setAudioState('reset');
  };

  const handleEncodingChange = (value: string) => {
    setEncoding(value);
    setAudioState('reset');
  };

  const speedParams = {
    title: 'Speed',
    min: 0.25,
    max: 4,
    step: 0.05,
  };
  const handleSpeedChange = (value: number) => {
    if (value >= speedParams.min && value <= speedParams.max) {
      setSpeed(value);
    } else if (value > speedParams.max) {
      setSpeed(speedParams.max);
    } else {
      setSpeed(speedParams.min);
    }
    setAudioState('reset');
  };
  const pitchParams = {
    title: 'Pitch',
    min: -20,
    max: 20,
    step: 0.5,
  };
  const handlePitchChange = (value: number) => {
    if (value >= pitchParams.min && value <= pitchParams.max) {
      setPitch(value);
    } else if (value > pitchParams.max) {
      setPitch(pitchParams.max);
    } else {
      setPitch(pitchParams.min);
    }
    setAudioState('reset');
  };

  const validateIfVoiceCanPitchControl = (voice: string) => {
    const noPitchVoices = [
      'de-DE-Journey-D --Male',
      'de-DE-Journey-F --Female',
      'en-GB-Journey-D --Male',
      'en-GB-Journey-F --Female',
      'en-IN-Journey-D --Male',
      'en-IN-Journey-F --Female',
      'en-US-Journey-D --Male',
      'en-US-Journey-F --Female',
      'en-US-Journey-O --Female',
      'es-US-Journey-D --Male',
      'es-US-Journey-F --Female',
      'fr-CA-Journey-D --Male',
      'fr-CA-Journey-F --Female',
      'fr-FR-Journey-D --Male',
      'fr-FR-Journey-F --Female',
      'it-IT-Journey-D --Male',
      'it-IT-Journey-F --Female',
    ];
    if (noPitchVoices.includes(voice)) {
      if (pitch !== defaults.pitch) {
        handlePitchChange(defaults.pitch);
      }
      return true;
    } else {
      return false;
    }
  };
  const disableDropDowns = languagesData === null;

  const getFile = async (payload: TTSParams) => {
    setDownloadDisabled(true);
    try {
      showToast.info('Generating Speech...', '');
      const response = await downloadAudio(payload);
      const downloadURL = response.download_url;
      window.open(downloadURL, '_blank', 'noopener noreferrer');
      showToast.positive('Speech Generated!', '');
    } catch (err: any) {
      console.error(err);
      showToast.error('Failed generating speech', 'Please try again!');
    }
    setPlaybackDisabled(false);
    // await toast.promise(action(payload), {
    //   pending: 'Generating Speech...',
    //   success: 'Speech Generated',
    //   error: 'Error while generating speech',
    // });
  };

  const readText = async (payload: TTSParams) => {
    if (!audioRef.current) {
      audioRef.current = new Audio();
      audioRef.current.addEventListener('play', () => setAudioState('playing'));
      audioRef.current.addEventListener('pause', () => setAudioState('paused'));
      audioRef.current.addEventListener('ended', () => setAudioState('idle'));
    }

    if (audioState !== 'reset') {
      if (!audioRef.current.paused) {
        audioRef.current.pause();
        return;
      }

      if (audioRef.current.src && audioRef.current.paused) {
        audioRef.current.play();
        return;
      }
    }
    setPlaybackDisabled(true);
    showToast.info('Generating Speech...', '');
    try {
      const audioBlob = await playAudio(payload);

      showToast.positive('Speech Generated!', '');
      setPlaybackDisabled(false);
      if (audioRef.current.src) {
        URL.revokeObjectURL(audioRef.current.src);
        audioRef.current.src = '';
      }
      const audioURL = URL.createObjectURL(audioBlob);

      audioRef.current.src = audioURL;
      audioRef.current.play();
    } catch (err: any) {
      console.error(err);
      showToast.error('Failed generating speech', 'Please try again!');
      setPlaybackDisabled(false);
    }
  };

  const playbackButtonContent = {
    playing: { icon: <PlayIcon />, text: 'Pause' },
    paused: { icon: <FaPauseCircle className="text-to-speech__button-svg" />, text: 'Resume' },
    idle: { icon: <PlayIcon />, text: 'Read Aloud' },
    reset: { icon: <PlayIcon />, text: 'Read Aloud' },
  };
  const currentPlaybackButtonContent =
    playbackButtonContent[audioState] || playbackButtonContent.reset;

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      if (audioRef.current?.src) {
        URL.revokeObjectURL(audioRef.current.src);
      }
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
    };
  }, []);
  const languages = getLanguages(languagesData);
  return (
    <div className="text-to-speech__container">
      <div className="text-to-speech__input">
        <textarea
          className="text-to-speech__input__textarea"
          placeholder="Input text for speech output..."
          value={message}
          onChange={handleInputChange}
          title={`Text input cannot exceed ${byteLimit} bytes`}
        />
        <div className="text-to-speech__input__counter">
          {byteCount}/{byteLimit} bytes
        </div>
      </div>
      <div className="text-to-speech__flex">
        <div className="text-to-speech__select-flex">
          <SidekickDropDown
            value={language}
            placeholder={languages.find(lang => lang.value === language)?.name || language}
            select={languages}
            disabled={disableDropDowns}
            onChange={handleLanguageChange}
          />
        </div>
        <div className="text-to-speech__select-flex">
          <SidekickDropDown
            value={voice}
            placeholder={voice}
            select={availableVoices}
            disabled={disableDropDowns}
            onChange={handleVoiceChange}
          />
        </div>
      </div>
      <div className="text-to-speech__advanced-section">
        <div className="text-to-speech__advanced-container">
          <AdvancedSettings>
            <span className="text-to-speech__select-label">Audio Encoding</span>
            <SidekickDropDown
              value={encoding}
              placeholder={encoding}
              select={getEncodings()}
              onChange={handleEncodingChange}
            />
            <SidekickValueSlider
              title={speedParams.title}
              min={speedParams.min}
              max={speedParams.max}
              value={speed}
              step={speedParams.step}
              onChange={handleSpeedChange}
            />
            <SidekickValueSlider
              title={pitchParams.title}
              min={pitchParams.min}
              max={pitchParams.max}
              value={pitch}
              step={pitchParams.step}
              disabled={validateIfVoiceCanPitchControl(voice)}
              onChange={handlePitchChange}
            />
          </AdvancedSettings>
        </div>
        <div className="text-to-speech__button-flex">
          <button
            className="text-to-speech__button__root"
            disabled={playbackDisabled || languagesData === null}
            onClick={async () => {
              readText(payload);
            }}
          >
            <span className="text-to-speech__button__icon">
              {currentPlaybackButtonContent.icon}
            </span>
            <span className="text-to-speech__button__text">
              {currentPlaybackButtonContent.text}
            </span>
          </button>
          <button
            className="text-to-speech__button__root"
            disabled={downloadDisabled || languagesData === null}
            onClick={async () => {
              getFile(payload);
            }}
          >
            <span className="text-to-speech__button__icon">
              <DownloadIcon />
            </span>
            <span className="text-to-speech__button__text">Save Voice Output</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default TextToSpeechView;
