import React from 'react';
import { useThemeStyles } from '@/hooks/useThemeStyles';
import { PolicyOptionGroup, PolicyOption } from '../config/policyWebOptions';
import './PolicyLocationDropdown.scss';

interface PolicyLocationDropdownProps {
  options: PolicyOptionGroup[];
  selectedValue: string | null;
  onChange: (value: string) => void;
  disabled?: boolean;
  className?: string;
}

const PolicyLocationDropdown: React.FC<PolicyLocationDropdownProps> = ({
  options,
  selectedValue,
  onChange,
  disabled = false,
  className = '',
}) => {
  const { isDarkMode } = useThemeStyles();

  const handleSelectionChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    onChange(event.target.value);
  };

  const coreBlue = '#0066B1';
  const hoverBlue = '#0075CA';
  const lightText = '#ECF7FF';
  const coreBlueRGB = '0, 102, 177';

  const coreBlueRgba008 = `rgba(${coreBlueRGB},0.08)`;
  const coreBlueRgba01 = `rgba(${coreBlueRGB},0.1)`;

  const baseStyles = [
    'h-10 px-4 text-[0.9rem] font-medium',
    'transition-all duration-200 ease-out',
    'focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2',
    'relative w-full appearance-none',
    'cursor-pointer',
  ].join(' ');

  const getDropdownStyles = () => {
    if (disabled) {
      return `${baseStyles} opacity-50 cursor-not-allowed`;
    }

    const textColor = isDarkMode ? lightText : coreBlue;
    const backgroundColor = isDarkMode ? 'transparent' : 'white';

    return `${baseStyles} text-[${textColor}] bg-[${backgroundColor}]`;
  };

  const optionGroupStyles = isDarkMode ? 'text-white bg-[#002D4F]' : 'text-[#003963] bg-white';

  const optionStyles = isDarkMode
    ? 'text-white bg-[#002D4F] hover:bg-[#003963]'
    : 'text-[#003963] bg-white hover:bg-[#F5F5F5]';

  return (
    <div
      className={`policy-dropdown policy-dropdown--temperature-aligned ${className}`.trim()}
      style={
        {
          '--policy-dropdown-core-blue': coreBlue,
          '--policy-dropdown-hover-blue': hoverBlue,
          '--policy-dropdown-light-text': lightText,
          '--policy-dropdown-core-blue-rgba008': coreBlueRgba008,
          '--policy-dropdown-core-blue-rgba01': coreBlueRgba01,
        } as React.CSSProperties
      }
    >
      <select
        value={selectedValue ?? ''}
        onChange={handleSelectionChange}
        disabled={disabled}
        className={`policy-dropdown__select ${getDropdownStyles()}`}
        aria-label="Select policy location"
        role="combobox"
        aria-expanded="false"
        style={{
          paddingRight: '30px',
        }}
      >
        {(!selectedValue || selectedValue === '') && (
          <option
            value=""
            disabled
            className={`${isDarkMode ? 'text-gray-400 bg-[#002D4F]' : 'text-gray-500 bg-white'}`}
          >
            Select policy location...
          </option>
        )}
        {options.map(group => (
          <optgroup
            label={group.label}
            key={group.label}
            className={`policy-dropdown__option-group ${optionGroupStyles}`}
          >
            {group.options.map((option: PolicyOption) => (
              <option key={option.value} value={option.value} className={`policy-dropdown__option ${optionStyles}`}>
                {option.label}
              </option>
            ))}
          </optgroup>
        ))}
      </select>
    </div>
  );
};

export default PolicyLocationDropdown;
