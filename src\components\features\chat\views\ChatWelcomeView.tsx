import type { PromptTemplate } from '@/types';
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useOutletContext } from 'react-router-dom';

import GreetingSection from '../../chat/components/GreetingSection';
import PromptSuggestions from '../../chat/components/PromptSuggestions';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  selectSelectedChatType,
  selectError,
  selectIsLoading,
} from '@/store/slices/chatSlice';
import { ChatType } from '@/api/chatApi';
import { useChatInputHandler } from '../hooks/useChatInputHandler';
import ChatInputArea from '../components/ChatInputArea';
import { FileInfo } from '@/types/fileUpload';

export interface ChatWelcomeViewInterface {
  promptTemplate: PromptTemplate | null
  promptTextOverride: string | null
  promptTemplateIsGlobal: boolean
}

const ChatWelcomeView: React.FC<ChatWelcomeViewInterface> = (props: ChatWelcomeViewInterface) => {
  const { promptTemplate, promptTextOverride, promptTemplateIsGlobal } = props;
  const processedTemplateRef = useRef<PromptTemplate | null>(null);

  const [showSuggestions, setShowSuggestions] = useState(true);
  const dispatch = useAppDispatch();
  const selectedChatType = useAppSelector(selectSelectedChatType);
  const isLoading = useAppSelector(selectIsLoading);
  const error = useAppSelector(selectError);

  const context = useOutletContext<{ selectedChatType?: ChatType }>();
  const modelFromContext = context?.selectedChatType || 'General';

  const handleSuggestionClickedCallback = useCallback(() => {
    // setShowSuggestions(false);
  }, []);

  const { promptText, handlePromptTemplate, handleInputChange, handleSubmit, handleSuggestionClick, handleTemperatureChange } =
    useChatInputHandler(handleSuggestionClickedCallback);
  
  useEffect(() => {
    if (promptTemplate && !isLoading && processedTemplateRef.current !== promptTemplate) {
      processedTemplateRef.current = promptTemplate;
      handlePromptTemplate(promptTemplate, promptTextOverride, promptTemplateIsGlobal);
    }
  }, [promptTemplate, isLoading, handlePromptTemplate]);

  useEffect(() => {
    if (modelFromContext !== selectedChatType) {
      // dispatch(setSelectedChatType(modelFromContext));
      handleTemperatureChange(1.0);
    }
  }, [modelFromContext, selectedChatType, dispatch, handleTemperatureChange]);

  const onSuggestionItemClicked = useCallback(
    (suggestion: string) => {
      handleSuggestionClick(suggestion);
    },
    [handleSuggestionClick]
  );

  const handleWelcomeSubmit = useCallback(
    (attachedFiles?: FileInfo[]) => {
      handleSubmit(attachedFiles);
      setShowSuggestions(true);
    },
    [handleSubmit]
  );

  const handleWelcomeInputChange = useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      handleInputChange(e);
      // setShowSuggestions(e.target.value.trim() === '');
    },
    [handleInputChange]
  );

  return (
    <div className="chat-welcome-view flex flex-col items-center justify-center w-full h-full">
      <div className="chat-welcome-view__content-wrapper flex flex-col items-center w-full max-w-4xl">
        <GreetingSection selectedChatType={selectedChatType} />

        <ChatInputArea
          promptText={promptText}
          handleInputChange={handleWelcomeInputChange}
          handleSubmit={handleWelcomeSubmit}
          handleTemperatureChange={handleTemperatureChange}
          selectedChatType={selectedChatType}
          error={error}
          isWelcomeView={true}
        />

        {showSuggestions && (
          <div className="chat-welcome-view__suggestions-wrapper w-full max-w-[800px] mt-4">
            <PromptSuggestions onClick={onSuggestionItemClicked} selectedChatType={selectedChatType} />
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatWelcomeView;
