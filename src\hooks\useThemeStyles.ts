import { useTheme } from '@contexts/ThemeContext';
import {
  getThemeColors,
  themeClasses,
  spacing,
  typography,
  borderRadius,
  shadows,
  animation,
} from '@/config/theme';
import { ThemeStylesReturn } from '@/types/theme';

export const useThemeStyles = (): ThemeStylesReturn => {
  const { isDarkMode, toggleTheme, setTheme } = useTheme();

  // Retrieve the current theme's color object
  const colors = getThemeColors(isDarkMode);

  // Retrieve the Tailwind class names for the current theme
  const classes = {
    // Background classes
    background: themeClasses.background(isDarkMode),
    backgroundInput: themeClasses.backgroundInput(isDarkMode),
    backgroundSidebar: themeClasses.backgroundSidebar(isDarkMode),

    // Border classes
    border: themeClasses.border(isDarkMode),
    borderPrimary: themeClasses.borderPrimary(),
    borderSidebar: themeClasses.borderSidebar(),

    // Text classes
    text: themeClasses.text(isDarkMode),
    textMuted: themeClasses.textMuted(isDarkMode),
    placeholder: themeClasses.placeholder(isDarkMode),

    // Interactive states
    hoverBackground: themeClasses.hoverBackground(isDarkMode),
    focusRing: themeClasses.focusRing(isDarkMode),

    // Combined utility classes for common components
    button: themeClasses.button(isDarkMode, 'primary'),
    buttonSecondary: themeClasses.button(isDarkMode, 'secondary'),
    buttonGhost: themeClasses.button(isDarkMode, 'ghost'),
    iconButton: themeClasses.iconButton(isDarkMode),
    circleIconButton: themeClasses.circleIconButton(isDarkMode),
    input: themeClasses.input(isDarkMode),
    card: themeClasses.card(isDarkMode),
    sidebar: themeClasses.sidebar(isDarkMode),
    rightPanel: themeClasses.rightPanel(isDarkMode),

    // Sidebar specific classes
    sidebarText: themeClasses.sidebarText(),
    sidebarSubItemText: themeClasses.sidebarSubItemText(),
    sidebarBorder: themeClasses.sidebarBorder(),
    sidebarIconHover: themeClasses.sidebarIconHover(),
  };

  const tokens = {
    spacing,
    typography,
    borderRadius,
    shadows,
    animation,
  };

  return {
    isDarkMode,
    toggleTheme,
    setTheme,
    colors,
    classes,
    tokens,
  };
};

export default useThemeStyles;
