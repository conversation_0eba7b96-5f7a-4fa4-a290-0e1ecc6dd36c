import type { PromptTemplate } from '@/types';

import React, { useEffect, useState } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { toast } from 'react-toastify';

import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { fetchSessionDetails } from '@/api/chatHistoryApi';
import { selectMessages, selectCurrentSessionId, resumeChatSession, startNewChat } from '@/store/slices/chatSlice';

import { ChatWelcomeView, ChatConversationView } from '@/components/features/chat';
import { processAndAssociateHistory } from '@/components/features/chat/utils/sessionProcessing'; 

/**
 * ChatContainer component decides which view to display based on chat state.
 * Shows welcome view when no messages exist, conversation view otherwise.
 */
const ChatContainer: React.FC = () => {
  const { sessionId } = useParams<{ sessionId?: string }>();
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const messages = useAppSelector(selectMessages);
  const currentSessionId = useAppSelector(selectCurrentSessionId);

  const [isSessionLoading, setIsSessionLoading] = useState(false);
  const [sessionError, setSessionError] = useState<string | null>(null);
  const [promptTemplate, setPromptTemplate] = useState<PromptTemplate | null>(null);
  const [promptTemplateIsGlobal, setPromptTemplateIsGlobal] = useState<boolean>(false);
  const [promptTextOverride, setPromptTextOverride] = useState<string | null>(null);

  useEffect(() => {
    const newPromptTemplate = location?.state?.promptTemplate;
    const promptOverride = location?.state?.promptOverride;
    const promptIsGlobal = location?.state?.promptTemplateIsGlobal;
    
    if (newPromptTemplate && newPromptTemplate !== promptTemplate) {
      switch (newPromptTemplate.sessionType) {
        case 1:
          dispatch(startNewChat('General'));
          break;
        case 2:
          dispatch(startNewChat('Code'));
          break;
        case 3:
          dispatch(startNewChat('Medical'));
          break;
        default:
          dispatch(startNewChat('General'));
      }

      setPromptTemplate(newPromptTemplate);
      setPromptTextOverride(promptOverride);
      setPromptTemplateIsGlobal(promptIsGlobal);
      setTimeout(() => {
        setPromptTemplate(null);
        setPromptTextOverride(null);
        setPromptTemplateIsGlobal(false);
      }, 10)
      window.history.replaceState({}, '');
    }
  }, [location?.state, dispatch]);

  useEffect(() => {
    let isMounted = true;

    if (sessionId && sessionId !== currentSessionId) {
      if (isMounted) {
        setIsSessionLoading(true);
        setSessionError(null);
      }

      const loadAndResumeSession = async () => {
        try {
          const sessionData = await fetchSessionDetails(sessionId);
          if (!isMounted) return;

          const loadedMessages = processAndAssociateHistory(sessionData);

          if (!isMounted) return;
          dispatch(resumeChatSession({ sessionData, messages: loadedMessages }));
          setSessionError(null);
        } catch (err) {
          if (!isMounted) return;
          const msg = err instanceof Error ? err.message : 'An unknown error occurred.';
          setSessionError(msg);
          toast.error(`Failed to load session: ${msg}`);
          dispatch(startNewChat());
          navigate('/chat', { replace: true });
        } finally {
          if (isMounted) setIsSessionLoading(false);
        }
      };

      loadAndResumeSession();
    } else {
      if (isMounted) setIsSessionLoading(false);
    }

    return () => {
      isMounted = false;
    };
  }, [sessionId, currentSessionId, dispatch, navigate]);

  /* ---------------------- Render ---------------------- */

  if (isSessionLoading && sessionId) {
    return (
      <div className="chat-container__loading-indicator flex items-center justify-center h-full !text-white">
        Loading session history...
      </div>
    );
  }

  if (sessionError && sessionId) {
    return (
      <div className="chat-container__error-display flex flex-col items-center justify-center h-full text-red-500 dark:text-red-400 p-4">
        <h2 className="chat-container__error-title text-xl font-semibold mb-2">
          Error Loading Session
          </h2>
        <p className="chat-container__error-text text-center">{sessionError}</p>
        <button
          onClick={() => navigate('/chat', { replace: true })}
          className="chat-container__new-chat-button mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors font-roboto"
        >
          Go to New Chat
        </button>
      </div>
    );
  }
  
  return messages.length === 0 ? <ChatWelcomeView promptTemplate={promptTemplate} promptTextOverride={promptTextOverride} promptTemplateIsGlobal={promptTemplateIsGlobal} /> : <ChatConversationView />;
};

export default ChatContainer;
