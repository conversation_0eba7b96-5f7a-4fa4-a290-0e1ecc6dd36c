import { useState, useCallback } from 'react';
import { exportToWord } from '@/api/wordExportApi';

interface UseWordExportState {
  isLoading: boolean;
  error: string | null;
}

interface UseWordExportResult extends UseWordExportState {
  handleWordExport: (messageContent: string) => Promise<void>;
}

export const useWordExport = (): UseWordExportResult => {
  const [state, setState] = useState<UseWordExportState>({
    isLoading: false,
    error: null,
  });

  const handleWordExport = useCallback(async (messageContent: string) => {
    setState({ isLoading: true, error: null });
    try {
      const result = await exportToWord(messageContent);
      if (result.download_url) {
        window.open(result.download_url, '_blank', 'noopener,noreferrer');
      } else if (result.error) {
        setState({ isLoading: false, error: result.error });
        console.error('Word export failed:', result.error);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred.';
      setState({ isLoading: false, error: errorMessage });
      console.error('Error in handleWordExport:', errorMessage);
    } finally {
      // set isLoading to false after the export is complete
      setState(prevState => ({ ...prevState, isLoading: false }));
    }
  }, []);

  return {
    ...state,
    handleWordExport,
  };
};
