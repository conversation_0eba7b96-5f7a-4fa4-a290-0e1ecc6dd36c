import type {
  PromptTemplate,
  CreatePromptTemplate,
  CreatePromptTemplateResponse,
  UpdatePromptTemplate,
  DeletePromptTemplateResponse,
  SessionType,
} from '@/types/promptTemplateTypes';

import { client } from './client';

const { hostname, pathname } = window.location;
const baseHost = hostname.includes('localhost') ? 'http://localhost:5000' : ``;

let baseApiPath = '/sidekick';
const apiPathPrefix = pathname.split(baseApiPath)[0];
baseApiPath = `${apiPathPrefix}${baseApiPath}/api/prompt_templates`;

const baseRoute = `${baseHost}${baseApiPath}`;
const baseRoutes = {
  USER_TEMPLATES: `${baseRoute}/my`,
  GLOBAL_TEMPLATES: `${baseRoute}/public`,
};

export interface GetPromptTemplatesResponse {
  user: string | null;
  promptTemplates: PromptTemplate[];
}

const _getPromptTemplates = async (isGlobal: boolean = true) => {
  const baseRoute = isGlobal ? baseRoutes.GLOBAL_TEMPLATES : baseRoutes.USER_TEMPLATES;
  const response = await client.get<GetPromptTemplatesResponse>(baseRoute);
  return response.data;
};

const _getPromptTemplatesByType = async (sessionType: SessionType, isGlobal: boolean = true) => {
  const baseRoute = isGlobal ? baseRoutes.GLOBAL_TEMPLATES : baseRoutes.USER_TEMPLATES;
  const response = await client.get<GetPromptTemplatesResponse>(
    `${baseRoute}/type/${sessionType.valueOf}`
  );
  return response.data;
};

const _getPromptTemplateById = async (promptTemplateId: string, isGlobal: boolean = true) => {
  const baseRoute = isGlobal ? baseRoutes.GLOBAL_TEMPLATES : baseRoutes.USER_TEMPLATES;
  const response = await client.get<PromptTemplate>(`${baseRoute}/${promptTemplateId}`);
  return response.data;
};

export const postPromptTemplate = async (toCreate: CreatePromptTemplate) => {
  const response = await client.post<CreatePromptTemplateResponse>(
    `${baseRoutes.USER_TEMPLATES}`,
    toCreate
  );
  return response.data;
};

export const postPublicPromptTemplate = async (toCreate: CreatePromptTemplate) => {
  const response = await client.post<CreatePromptTemplateResponse>(
    `${baseRoutes.GLOBAL_TEMPLATES}`,
    toCreate
  );
  return response.data;
};

export const getUserPromptTemplates = async () => {
  return await _getPromptTemplates(false);
};

export const getUserAuthoredPromptTemplates = async () => {
  const baseRoute = `${baseRoutes.USER_TEMPLATES}/authored`;
  const response = await client.get<GetPromptTemplatesResponse>(baseRoute);
  return response.data;
};

export const getUserPromptTemplatesByType = async (sessionType: SessionType) => {
  return await _getPromptTemplatesByType(sessionType, false);
};

export const getGlobalPromptTemplates = async () => {
  return await _getPromptTemplates(true);
};

export const getUserPromptTemplateById = async (promptTemplateId: string) => {
  return await _getPromptTemplateById(promptTemplateId, false);
};

export const getGlobalPromptTemplateById = async (promptTemplateId: string) => {
  return await _getPromptTemplateById(promptTemplateId, true);
};

export const getGlobalPromptTemplatesByType = async (sessionType: SessionType) => {
  return await _getPromptTemplatesByType(sessionType, true);
};

export const patchPromptTemplateById = async (
  promptTemplateUpdates: UpdatePromptTemplate,
  isGlobal: boolean = false
) => {
  const { id } = promptTemplateUpdates;
  const updates = promptTemplateUpdates;
  const requestBody = {
    name: updates?.name ?? null,
    prompt: updates?.prompt ?? null,
    sessionType: updates?.sessionType ?? null,
    temperature: updates?.temperature ?? null,
    systemInstructions: updates?.systemInstructions ?? null,
    addedTags: updates?.addedTags ?? null,
    removedTags: updates?.addedTags ?? null,
    addedAuthorizedEntities: updates?.addedAuthorizedEntities ?? null,
    removedAuthorizedEntities: updates?.removedAuthorizedEntities ?? null,
  };

  const baseRoute = isGlobal ? baseRoutes.GLOBAL_TEMPLATES : baseRoutes.USER_TEMPLATES;
  const response = await client.patch<PromptTemplate>(`${baseRoute}/${id}`, requestBody);
  return response.data;
};

export const deletePromptTemplateById = async (
  promptTemplateId: string,
  isGlobal: boolean = false
) => {
  const baseRoute = isGlobal ? baseRoutes.GLOBAL_TEMPLATES : baseRoutes.USER_TEMPLATES;
  const response = await client.delete<DeletePromptTemplateResponse>(
    `${baseRoute}/${promptTemplateId}`
  );
  return response.data;
};
