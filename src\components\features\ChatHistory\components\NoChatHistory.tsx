import React from 'react';
import { FaRegCommentDots } from 'react-icons/fa'; // Example icon
import { useThemeStyles } from '@/hooks/useThemeStyles';

const NoChatHistory: React.FC = () => {
  const { classes } = useThemeStyles();

  return (
    <div
      className={`no-chat-history flex flex-col items-center justify-center text-center p-8 rounded-lg ${classes.textMuted}`}
    >
      <FaRegCommentDots size={48} className="no-chat-history__icon mb-4" />
      <h2 className={`no-chat-history__heading text-xl font-semibold mb-2 ${classes.text}`}>No Chat History Yet</h2>
      <p className="no-chat-history__message">
        Looks like you haven't started any chats. Begin a new conversation to see it here!
      </p>
    </div>
  );
};

export default NoChatHistory;
