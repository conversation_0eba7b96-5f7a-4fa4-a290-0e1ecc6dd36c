.TopBar__main {
    width: 100%;
    height: 70px;
    padding: var(--spacing-spacing-m-2, 16px) var(--spacing-spacing-l-1, 32px) var(--spacing-spacing-m-1, 8px) var(--spacing-spacing-l-1, 32px);
    border-bottom: 1px solid var(--border-color-border-interactive, #0066B1);

    display: flex;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    gap: var(--spacing-spacing-m-2, 16px);

    .TopBar__left {
        width: 100%;
        display: flex;
        align-items: center;
        .TopBar__heading {
            display: flex;
            align-items: center;
            gap: var(--spacing-spacing-m-1, 12px);
        }
        .TopBar__title {
            padding-right: var(--spacing-spacing-m-3, 24px);

            color: #FFF;
            font-family: var(--font-roboto);
            font-size: 18px;
            font-style: normal;
            font-weight: 500;
            line-height: normal;
            letter-spacing: 0.144px;
        }
        .TopBar__content {
            padding-left: var(--spacing-spacing-m-3, 12px);
            border-left: 1px solid var(--border-color-border-interactive, #0066B1);
        }
        .TopBar__portal {
            width: 65%;
            display: flex;
            justify-content: center;
            padding-left: var(--spacing-spacing-m-3, 12px);
        }
    }
    .TopBar__left.sidebar-collapsed-adjust {
        margin-left: 2.5rem;
    }
    .TopBar__right {
        justify-self: flex-end;
        margin-left: var(--spacing-spacing-sm-3, 8px);
    }
}
