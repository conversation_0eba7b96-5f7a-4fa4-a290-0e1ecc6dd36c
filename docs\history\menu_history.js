// {% if "History" in info and not session_history %}
// {% for c in context %}
// <p>({{c["timestamp"]}})<br> <a href="{{c['url']}}/{{c['session_id']}}">{{c["title"]}}</a></p><hr>
// {% endfor %}
// {% endif %}
const sessionTypeToResumeLink = {
  chat: "chat",
  code: "code",
  mdlm: "med"
}

fetch(menu_history_url)
  .then((response) => {
    if (response.ok) {
      return response.json();
    }
  })
  .then((data) => {
    for (let i = 0; i < data.length; i++) {
      let history_info = data[i]
      console.log(history_info)

      let initial_prompt = history_info.conversation_history[0]
      const prompt_title = initial_prompt.prompt.split(" ").slice(0, 50).join(" ")
      const link_text = initial_prompt.prompt.split(" ").slice(0, 10).join(" ")

      const session_info_url = `/sidekick/history/${history_info.id}`
      const session_resume_url = `/sidekick/${sessionTypeToResumeLink[history_info.session_type]}/${history_info.id}/resume`

      history_info["title"] = prompt_title
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;");
      history_info["link_text"] = link_text
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;");

      let history_message = `
        <a href="${session_info_url}">View history: <br>${link_text}</a>
      `;

      if (!!history_info.file_history || history_info.file_history.length == 0) {
        history_message = history_message.concat(`
          <br>
          <a href="${session_resume_url}">Resume session</a>      
        `)
      }

      history_text = formMessageText(
        "person",
        "prompts",
        history_message,
        prompt_title,
        convertISODate(history_info["created_utc"]),
        (id = "")
      );
      // document.getElementById('response').innerHTML += history_text;
      writeMessageToScreen(history_text, "", "", false);
    }
  });
