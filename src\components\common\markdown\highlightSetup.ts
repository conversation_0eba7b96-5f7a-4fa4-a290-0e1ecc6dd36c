import { PrismLight } from 'react-syntax-highlighter';

const registeredPrismNames = new Set<string>();

interface LanguageConfig {
  names: string[];
  prismName: string;
  loader: () => Promise<any>;
}

const LANGUAGES_CONFIG: LanguageConfig[] = [
  {
    names: ['javascript', 'js'],
    prismName: 'javascript',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/javascript'),
  },
  {
    names: ['jsx'],
    prismName: 'jsx',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/jsx'),
  },
  {
    names: ['typescript', 'ts'],
    prismName: 'typescript',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/typescript'),
  },
  {
    names: ['tsx'],
    prismName: 'tsx',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/tsx'),
  },
  {
    names: ['html', 'markup'],
    prismName: 'markup',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/markup'),
  },
  {
    names: ['css'],
    prismName: 'css',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/css'),
  },
  {
    names: ['scss'],
    prismName: 'scss',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/scss'),
  },
  {
    names: ['markdown', 'md'],
    prismName: 'markdown',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/markdown'),
  },
  {
    names: ['bash', 'shell', 'sh'],
    prismName: 'bash',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/bash'),
  },
  {
    names: ['powershell', 'ps1'],
    prismName: 'powershell',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/powershell'),
  },
  {
    names: ['yaml', 'yml'],
    prismName: 'yaml',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/yaml'),
  },
  {
    names: ['json'],
    prismName: 'json',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/json'),
  },
  {
    names: ['docker', 'dockerfile'],
    prismName: 'docker',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/docker'),
  },
  {
    names: ['diff'],
    prismName: 'diff',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/diff'),
  },
  {
    names: ['python', 'py'],
    prismName: 'python',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/python'),
  },
  {
    names: ['java'],
    prismName: 'java',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/java'),
  },
  {
    names: ['c'],
    prismName: 'c',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/c'),
  },
  {
    names: ['cpp'],
    prismName: 'cpp',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/cpp'),
  },
  {
    names: ['csharp', 'cs'],
    prismName: 'csharp',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/csharp'),
  },
  {
    names: ['go', 'golang'],
    prismName: 'go',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/go'),
  },
  {
    names: ['rust', 'rs'],
    prismName: 'rust',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/rust'),
  },
  {
    names: ['php'],
    prismName: 'php',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/php'),
  },
  {
    names: ['ruby', 'rb'],
    prismName: 'ruby',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/ruby'),
  },
  {
    names: ['kotlin', 'kt'],
    prismName: 'kotlin',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/kotlin'),
  },
  {
    names: ['swift'],
    prismName: 'swift',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/swift'),
  },
  {
    names: ['sql'],
    prismName: 'sql',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/sql'),
  },
  {
    names: ['toml'],
    prismName: 'toml',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/toml'),
  },
  {
    names: ['ini'],
    prismName: 'ini',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/ini'),
  },
  {
    names: ['graphql', 'gql'],
    prismName: 'graphql',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/graphql'),
  },
  {
    names: ['regex'],
    prismName: 'regex',
    loader: () => import('react-syntax-highlighter/dist/esm/languages/prism/regex'),
  },
];

/**
 * Registers all languages defined in LANGUAGES_CONFIG.
 * This is an eager loading strategy that loads everything at app startup.
 */
export async function registerAllSupportedLanguages() {
  const registrationPromises = LANGUAGES_CONFIG.map(async config => {
    try {
      const module = await config.loader();
      // Register the language module using its official Prism name.
      PrismLight.registerLanguage(config.prismName, module.default);
      registeredPrismNames.add(config.prismName); // Mark as registered.

      config.names.forEach(alias => {
        PrismLight.registerLanguage(alias, module.default);
      });
    } catch (e) {
      console.error(`Failed to register language '${config.prismName}'`, e);
    }
  });

  await Promise.all(registrationPromises);
}
