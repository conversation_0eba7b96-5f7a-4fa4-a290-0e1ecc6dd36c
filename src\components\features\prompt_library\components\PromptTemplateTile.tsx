import React from 'react';

import useThemeStyles from '@/hooks/useThemeStyles';
import { ItemTile } from '@/components/common/ItemTile/ItemTile';
import { formatSidekickDate } from '@/utils/dateUtils';

import './PromptTemplateTile.scss';
// import { PromptTemplate } from '@/types';

export interface PromptTemplateCardProps {
  promptTemplateInfo: any;
  onTileClick: () => void;
  onRunClick: (e: React.MouseEvent) => void;
  onDeleteClick: (e: React.MouseEvent) => void;
  isGlobal?: boolean;
  author?: string;
  currentUser?: string | null;
}

const PromptTemplateTile: React.FC<PromptTemplateCardProps> = (props: PromptTemplateCardProps) => {
  const { classes } = useThemeStyles();
  const { promptTemplateInfo, onTileClick, onRunClick, onDeleteClick, isGlobal, author, currentUser } = props;
  const promptTemplate = promptTemplateInfo.promptTemplate;

  const handleRunClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onRunClick(e);
  };

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDeleteClick(e);
  };

  const TileContent = (
    <div className={`promptTemplateTile ${classes.text}`}>
      <div className={`promptTemplateTile__title`}>
        <div className={`promptTemplateTile__title-header`}>{promptTemplate.name}</div>
        <div className={`promptTemplateTile__title-date`}>
          Last edited {formatSidekickDate(new Date(promptTemplate.updatedUtc))}
        </div>
      </div>
      <div className={`promptTemplateTile__prompt-container`}>
        <div className={`promptTemplateTile__prompt-text`}>{promptTemplate.prompt}</div>
      </div>
      <div className={`promptTemplateTile__actions`}>
        {(!isGlobal || (isGlobal && currentUser == author)) && (
          <div className={`promptTemplateTile__delete`} onClick={handleDeleteClick}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
          >
            <path
              d="M14.12 10.47L12 12.59L9.87 10.47L8.46 11.88L10.59 14L8.47 16.12L9.88 17.53L12 15.41L14.12 17.53L15.53 16.12L13.41 14L15.53 11.88L14.12 10.47ZM15.5 4L14.5 3H9.5L8.5 4H5V6H19V4H15.5ZM6 19C6 20.1 6.9 21 8 21H16C17.1 21 18 20.1 18 19V7H6V19ZM8 9H16V19H8V9Z"
              fill="#F7987D"
            />
          </svg>
          </div>
        )}
        <div className={`promptTemplateTile__run`}>
          <div className={`promptTemplateTile__run-button`} onClick={handleRunClick}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
            >
              <path
                d="M19.5 14.5L13.5 20.5L12.08 19.08L15.67 15.5H4.5V3.5H6.5V13.5H15.67L12.08 9.92L13.5 8.5L19.5 14.5Z"
                fill="white"
              />
            </svg>
            <div className={`promptTemplateTile__run-label`}>Run</div>
          </div>
        </div>
      </div>
    </div>
  );
  return <ItemTile onClick={onTileClick}>{TileContent}</ItemTile>;
};

export default PromptTemplateTile;
