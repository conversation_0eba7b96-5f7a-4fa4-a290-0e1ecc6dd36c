.promptTemplateTile {
    height:100%;
    display: flex;
    flex: 1 0 0;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    align-self: stretch;
    padding: var(--spacing-spacing-m-2);
    .promptTemplateTile__title {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        align-self: stretch;
        gap: var(--spacing-spacing-sm-3, 8px);
        padding-bottom: var(--spacing-spacing-m-1, 12px);
        border-bottom: 1px solid var(--border-color-border-interactive, #0066B1);
        .promptTemplateTile__title-header {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            height: 42px;
            width: 100%;
            align-self: stretch;
            overflow: hidden;
            color: #FFF;
            font-family: var(--font-sofia-pro);
            font-size: 16px;
            font-style: normal;
            font-weight: 700;
            line-height: 130%; /* 20.8px */
            letter-spacing: 0.128px;
        }
        .promptTemplateTile__title-date {
            color: var(--elements-on-surface-onsurface-tertiary, #C5E6FF);
            text-align: center;
            font-family: var(--font-roboto);
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 100%; /* 12px */
            letter-spacing: 0.096px;
        }
    }
    .promptTemplateTile__prompt-container {
        display: flex;
        padding-top: var(--spacing-spacing-m-1, 12px);
        align-items: flex-start;
        gap: 10px;
        flex: 1 0 0;
        align-self: stretch;
        .promptTemplateTile__prompt-text {
            height: 100px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 7;
            line-clamp: 7;
            color: var(--elements-on-surface-onsurface-tertiary, #C5E6FF);
            text-overflow: ellipsis;
            overflow: hidden;
            font-family: var(--font-roboto);
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 120%; /* 14.4px */
            letter-spacing: 0.096px;
        }
    }
    .promptTemplateTile__actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        align-self: stretch;
        padding-top: var(--spacing-spacing-sm-3, 8px);
        .promptTemplateTile__delete {
            width: 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-spacing-sm-3, 8px);

            border-radius: 50px;
            border: 1px solid var(--accent-accent-1-accent-1-primary, #F7987D);
        }
        .promptTemplateTile__run {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: var(--spacing-spacing-sm-3, 8px) var(--spacing-spacing-m-1, 12px);
            border-radius: 50px;
            border: 1px solid var(--border-color-border-interactive, #0066B1);
            .promptTemplateTile__run-button {
                display: flex;
                justify-content: center;
                align-items: center;
                gap: var(--spacing-spacing-sm-2, 4px);
                padding: 0px var(--spacing-spacing-sm-2, 4px) 0px var(--spacing-spacing-sm-1, 2px);
                .promptTemplateTile__run-label {
                    color: var(--text-text-invert, #FFF);
                    text-align: center;
                    font-family: var(--font-roboto);
                    font-size: 16px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 100%; /* 16px */
                    letter-spacing: 0.128px;
                }
            }
        }
    }
}
