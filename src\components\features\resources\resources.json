{"title": "Helpful links", "introduction": ["Sidekick is a Generative AI Assistance Tool that empowers users to harness the power of large language models (LLMs).", "The application is powered by two of Google's LLMs, one for standard text uses and one for code uses. They are designed to generate human-like text responses to help with a variety of text and code-based tasks."], "helpfulLinks": [{"title": "Sidekick Resource Guide", "url": "https://highmarkhealth.sharepoint.com/sites/thinkUP/SiteAssets/Forms/AllItems.aspx?id=%2Fsites%2FthinkUP%2FSiteAssets%2FSitePages%2FGenerative%2F1456610897SIDEKICK%20Resource%20Guide%20%2D4%2E1%2Epdf&parent=%2Fsites%2FthinkUP%2FSiteAssets%2FSitePages%2FGenerative"}, {"title": "GenAI Compliance Workday Learning Course", "url": "https://www.myworkday.com/highmarkhealth/learning/course/96d5bf85e84710019e48865949d60000?type=9882927d138b100019b928e75843018d"}, {"title": "GenAI Basics Workday Learning Course", "url": "https://www.myworkday.com/highmarkhealth/learning/course/6257b7bf29d510019e49463a80950000?type=9882927d138b100019b928e75843018d"}, {"title": "Prompt Engineering for General Use - Workday Learning Course", "url": "https://www.myworkday.com/highmarkhealth/learning/course/5094780d0de210006911ba12b1010002?type=9882927d138b100019b928e75843018d"}, {"title": "Prompt Engineering for Coding - Workday Learning Course", "url": "https://www.myworkday.com/highmarkhealth/learning/course/4083e46dd4d11000694007bb27070000?type=9882927d138b100019b928e75843018d"}, {"title": "Generative AI Microsite", "url": "https://highmarkhealth.sharepoint.com/sites/thinkUP/SitePages/Generative%20AI.aspx?xsdata=MDV8MDJ8fDk2YTg1NTM3N2JmOTQ4Mzk5MGIyMDhkYzRkYmE2NjEyfGM1N2QxYTczMGU1YzQ2NGJhZmI3MDg2ZGM2N2YzZDQ2fDB8MHw2Mzg0NzA3MTAxNTE1MTYzMzh8VW5rbm93bnxWR1ZoYlhOVFpXTjFjbWwwZVZObGNuWnBZMlY4ZXlKV0lqb2lNQzR3TGpBd01EQWlMQ0pRSWpvaVYybHVNeklpTENKQlRpSTZJazkwYUdWeUlpd2lWMVFpT2pFeGZRPT18MXxMMk5vWVhSekx6RTVPak0zWmpRMk1UVmhMVGhrWlRJdE5ESXdOaTA0TkRFeExUQmpaR1ptTUdJeU5EbGpaVjlpTnpJM01ERTNZeTAyWmpKaUxUUXpZVEV0WVRBek5DMDJZekpqTm1FNE16SXhZbUpBZFc1eExtZGliQzV6Y0dGalpYTXZiV1Z6YzJGblpYTXZNVGN4TVRRM05ESXhORGczTkE9PXxiMWE1ZTI2ZGIxOTM0NTM4OTBiMjA4ZGM0ZGJhNjYxMnw5YjUyN2Q2ODJjMTg0NDMzOGUxYmFkOGFhYjk4MzQ3Yg%3D%3D&sdata=c1l3VE45NEZIdGpOUnlma1pjMTJXa0g3VXZHN0VjZ05CY0RtQ0ZxZUJJST0%3D&ovuser=c57d1a73-0e5c-464b-afb7-086dc67f3d46%2Ckiersten.kochanowski%40highmarkhealth.org&OR=Teams-HL&CT=1711642079824&clickparams=eyJBcHBOYW1lIjoiVGVhbXMtRGVza3RvcCIsIkFwcFZlcnNpb24iOiI0OS8yNDAyMTUyODYxNiIsIkhhc0ZlZGVyYXRlZFVzZXIiOmZhbHNlfQ%3D%3D"}, {"title": "Sidekick Usage Dashboard", "url": "https://app.powerbi.com/groups/me/apps/fea34786-55db-4692-9c62-fc7d567641fb/reports/10c95809-7928-4a2a-b65c-90821f649ec3/346a494f8ecfc896a143?ctid=c57d1a73-0e5c-464b-afb7-086dc67f3d46&experience=power-bi"}, {"title": "SDLC Prompt Library", "url": "https://highmarkhealth.sharepoint.com/:x:/r/sites/AIenGen/_layouts/15/Doc.aspx?sourcedoc=%7B41CEAF99-2229-4FA0-B99B-E011E5872A93%7D&file=Prompt%20Library.xlsx&action=default&mobileredirect=true"}], "modelDetailsTitle": "Model details", "modelDetailsSection": [{"heading": "Chat session model details", "content": ["Powered by gemini-2.0-flash-001 by Google", "Training data through June 2024", "Temperature button values:", "Max output tokens set at 8,192", "Max input/output tokens of 1 million tokens (around 4000 pages)"]}, {"heading": "Code session model details", "content": ["Powered by gemini-2.0-flash-001 by Google", "Training data through June 2024", "Temperature button values:", "Max output tokens set at 8,192", "Max input/output tokens of 1 million tokens (around 4000 pages)"]}, {"heading": "Med session model details", "content": ["Powered by medlm-large-1.5 by Google", "Temperature button values:", "Max output tokens set at 8,192", "Max input/output tokens of 1 million tokens (around 4000 pages)"]}]}