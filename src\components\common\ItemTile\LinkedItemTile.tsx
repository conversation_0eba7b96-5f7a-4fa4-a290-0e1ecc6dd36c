import React from 'react';
import { Link } from 'react-router-dom';
import ItemTile, { ItemTileProps } from './ItemTile';

export type LinkedItemTileProps = Omit<ItemTileProps, 'onClick'> & {
  to: string;
};

export const LinkedItemTile: React.FC<LinkedItemTileProps> = (props: LinkedItemTileProps) => {
  const { to, ...itemTileProps } = props;

  return (
    <Link to={to} className="block no-underline">
      <ItemTile 
        {...itemTileProps} 
        onClick={(e) => {
          e.stopPropagation();
        }}
      />
    </Link>
  );
};

export default LinkedItemTile;
