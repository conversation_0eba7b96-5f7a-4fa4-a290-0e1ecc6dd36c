import type { Nullable } from '@/types/common';
import type {
  PromptTemplate,
  CreatePromptTemplate,
  UpdatePromptTemplate,
} from '@/types/promptTemplateTypes';

import { SessionType } from '@/types/promptTemplateTypes';

import { createSidekickSlice } from '../withTypes';

import {
  postPromptTemplate,
  postPublicPromptTemplate,
  getUserPromptTemplates,
  getGlobalPromptTemplates,
  patchPromptTemplateById,
  deletePromptTemplateById,
  GetPromptTemplatesResponse,
} from '../../api/promptTemplateApi';

export interface PromptTemplateInfo {
  promptTemplate: PromptTemplate;
  isFetching?: boolean;
  isUpdating?: boolean;
  isDeleting?: boolean;
}

export interface PromptTemplatesState {
  globalPromptTemplates: Record<string, PromptTemplateInfo>;
  userPromptTemplates: Record<string, PromptTemplateInfo>;
  userPromptTemplatesByType: Record<SessionType, PromptTemplate[]>;
  globalPromptTemplatesByType: Record<SessionType, PromptTemplate[]>;
  currentPromptTemplateId: Nullable<string>;
  currentPromptTemplateIsGlobal: Nullable<boolean>;
}

const initialState: PromptTemplatesState = {
  globalPromptTemplates: {},
  userPromptTemplates: {},
  userPromptTemplatesByType: {
    [SessionType.CHAT]: [],
    [SessionType.CODE]: [],
    [SessionType.MDLM]: [],
    [SessionType.POLICY]: []

  },
  globalPromptTemplatesByType: {
    [SessionType.CHAT]: [],
    [SessionType.CODE]: [],
    [SessionType.MDLM]: [],
    [SessionType.POLICY]: []
  },
  currentPromptTemplateId: null,
  currentPromptTemplateIsGlobal: null,
};

const promptTemplateSlice = createSidekickSlice({
  name: 'promptTemplates',
  initialState: initialState,
  reducers: create => {
    return {
      createMyPromptTemplate: create.asyncThunk(
        async (toCreate: CreatePromptTemplate) => {
          const data = await postPromptTemplate(toCreate);
          return data;
        },
        {
          fulfilled: (state, action) => {
            const { promptTemplate } = action.payload;
            state.userPromptTemplates[promptTemplate.id] = {
              promptTemplate: promptTemplate,
            };
            if (!state.userPromptTemplatesByType[promptTemplate.sessionType]) {
              state.userPromptTemplatesByType[promptTemplate.sessionType] = [];
            }
            state.userPromptTemplatesByType[promptTemplate.sessionType].push(promptTemplate);
          },
        }
      ),
      createPublicPromptTemplate: create.asyncThunk(
        async (toCreate: CreatePromptTemplate) => {
          const data = await postPublicPromptTemplate(toCreate);
          return data;
        },
        {
          fulfilled: (state, action) => {
            const { promptTemplate } = action.payload;
            state.globalPromptTemplates[promptTemplate.id] = {
              promptTemplate: promptTemplate,
            };
            if (!state.globalPromptTemplatesByType[promptTemplate.sessionType]) {
              state.globalPromptTemplatesByType[promptTemplate.sessionType] = [];
            }
            state.globalPromptTemplatesByType[promptTemplate.sessionType].push(promptTemplate);
          },
        }
      ),
      fetchMyPromptTemplates: create.asyncThunk(
        async () => {
          const data = await getUserPromptTemplates();
          return data;
        },
        {
          fulfilled: (state, action) => {
            const data: GetPromptTemplatesResponse = action.payload;
            const { promptTemplates } = data;

            state.userPromptTemplates = promptTemplates.reduce(
              (acc, curr) => {
                acc[curr.id] = {
                  promptTemplate: curr,
                  isFetching: false,
                  isDeleting: false,
                  isUpdating: false,
                };
                return acc;
              },
              {} as Record<string, PromptTemplateInfo>
            );
            state.userPromptTemplatesByType = promptTemplates.reduce(
              (acc, curr) => {
                if (acc[curr.sessionType]) {
                  acc[curr.sessionType].push(curr);
                } else {
                  acc[curr.sessionType] = [curr];
                }
                return acc;
              },
              {} as Record<SessionType, PromptTemplate[]>
            );
          },
        }
      ),
      fetchGlobalPromptTemplates: create.asyncThunk(
        async () => {
          const data = await getGlobalPromptTemplates();
          return data;
        },
        {
          fulfilled: (state, action) => {
            const data: GetPromptTemplatesResponse = action.payload;
            const { promptTemplates } = data;

            state.globalPromptTemplates = promptTemplates.reduce(
              (acc, curr) => {
                acc[curr.id] = {
                  promptTemplate: curr,
                  isFetching: false,
                  isDeleting: false,
                  isUpdating: false,
                };
                return acc;
              },
              {} as Record<string, PromptTemplateInfo>
            );
            state.globalPromptTemplatesByType = promptTemplates.reduce(
              (acc, curr) => {
                if (acc[curr.sessionType]) {
                  acc[curr.sessionType].push(curr);
                } else {
                  acc[curr.sessionType] = [curr];
                }
                return acc;
              },
              {} as Record<SessionType, PromptTemplate[]>
            );
          },
        }
      ),
      updatePromptTemplateById: create.asyncThunk(
        async ({
          promptTemplateUpdates,
          isGlobal = false,
        }: {
          promptTemplateUpdates: UpdatePromptTemplate;
          isGlobal: boolean;
        }) => {
          const updatedTemplate = await patchPromptTemplateById(promptTemplateUpdates, isGlobal);
          return updatedTemplate;
        },
        {
          pending: (state, action) => {
            const { promptTemplateUpdates, isGlobal } = action.meta.arg;
            if (isGlobal) {
              state.globalPromptTemplates[promptTemplateUpdates.id].isUpdating = true;
            } else {
              state.userPromptTemplates[promptTemplateUpdates.id].isUpdating = true;
            }
          },
          fulfilled: (state, action) => {
            const updatedTemplate = action.payload;
            const { isGlobal } = action.meta.arg;
            const promptTemplates = isGlobal
              ? state.globalPromptTemplates
              : state.userPromptTemplates;
            const prevSessionType = promptTemplates[updatedTemplate.id].promptTemplate.sessionType;

            const newSessionType = updatedTemplate.sessionType;

            promptTemplates[updatedTemplate.id].isUpdating = false;
            promptTemplates[updatedTemplate.id].promptTemplate = updatedTemplate;

            const promptTemplatesByType = isGlobal
              ? state.globalPromptTemplatesByType
              : state.userPromptTemplatesByType;

            if (prevSessionType !== newSessionType) {
              const prevIndex = promptTemplatesByType[prevSessionType].findIndex(
                p => p.id === updatedTemplate.id
              );
              promptTemplatesByType[prevSessionType].splice(prevIndex, 1);
              if (!promptTemplatesByType[newSessionType]) {
                promptTemplatesByType[newSessionType] = [];
              }
              promptTemplatesByType[newSessionType].push(updatedTemplate);
            } else {
              const promptIndex = promptTemplatesByType[newSessionType].findIndex(
                p => p.id === updatedTemplate.id
              );
              promptTemplatesByType[newSessionType].splice(promptIndex, 1, updatedTemplate);
            }
          },
          rejected: (state, action) => {
            const { promptTemplateUpdates, isGlobal } = action.meta.arg;
            const promptTemplates = isGlobal
              ? state.globalPromptTemplates
              : state.userPromptTemplates;
            promptTemplates[promptTemplateUpdates.id].isUpdating = false;
          },
        }
      ),
      removePromptTemplateById: create.asyncThunk(
        async ({
          promptTemplateId,
          isGlobal = false,
        }: {
          promptTemplateId: string;
          isGlobal: boolean;
        }) => {
          const data = await deletePromptTemplateById(promptTemplateId, isGlobal);
          return data;
        },
        {
          pending: (state, action) => {
            const { promptTemplateId, isGlobal } = action.meta.arg;
            if (isGlobal) {
              state.globalPromptTemplates[promptTemplateId].isDeleting = true;
            } else {
              state.userPromptTemplates[promptTemplateId].isDeleting = true;
            }
          },
          fulfilled: (state, action) => {
            const { id } = action.payload;
            const { isGlobal } = action.meta.arg;

            const promptTemplate = isGlobal
              ? state.globalPromptTemplates[id].promptTemplate
              : state.userPromptTemplates[id].promptTemplate;
            const promptIndex = isGlobal
              ? state.globalPromptTemplatesByType[promptTemplate.sessionType].findIndex(
                  p => p.id === id
                )
              : state.userPromptTemplatesByType[promptTemplate.sessionType].findIndex(
                  p => p.id === id
                );

            if (isGlobal) {
              state.globalPromptTemplates[id].isDeleting = false;
              state.globalPromptTemplatesByType[promptTemplate.sessionType].splice(promptIndex, 1);
              delete state.globalPromptTemplates[id];
            } else {
              state.userPromptTemplates[id].isDeleting = false;
              state.userPromptTemplatesByType[promptTemplate.sessionType].splice(promptIndex, 1);
              delete state.userPromptTemplates[id];
            }
          },
          rejected: (state, action) => {
            const { promptTemplateId, isGlobal } = action.meta.arg;
            const promptTemplates = isGlobal
              ? state.globalPromptTemplates
              : state.userPromptTemplates;
            promptTemplates[promptTemplateId].isDeleting = false;
          },
        }
      ),
    };
  },
  selectors: {
    selectAllUserPromptTemplates: promptTemplatesState => {
      return Object.values(promptTemplatesState.userPromptTemplates);
    },
    selectAllGlobalPromptTemplates: promptTemplatesState => {
      return Object.values(promptTemplatesState.globalPromptTemplates);
    },
    selectUserPromptTemplateById: (promptTemplatesState, promptTemplateId: string) => {
      return promptTemplatesState.userPromptTemplates[promptTemplateId];
    },
    selectGlobalPromptTemplateById: (promptTemplatesState, promptTemplateId: string) => {
      return promptTemplatesState.globalPromptTemplates[promptTemplateId];
    },
    selectUserPromptTemplatesByType: (promptTemplatesState, sessionType: SessionType) => {
      return promptTemplatesState.userPromptTemplatesByType[sessionType];
    },
    selectGlobalPromptTemplatesByType: (promptTemplatesState, sessionType: SessionType) => {
      return promptTemplatesState.globalPromptTemplatesByType[sessionType];
    },
  },
});

export const {
  createMyPromptTemplate,
  createPublicPromptTemplate,
  fetchMyPromptTemplates,
  fetchGlobalPromptTemplates,
  updatePromptTemplateById,
  removePromptTemplateById,
} = promptTemplateSlice.actions;

export const {
  selectAllUserPromptTemplates,
  selectAllGlobalPromptTemplates,
  selectUserPromptTemplateById,
  selectGlobalPromptTemplateById,
  selectUserPromptTemplatesByType,
  selectGlobalPromptTemplatesByType,
} = promptTemplateSlice.selectors;

export default promptTemplateSlice.reducer;
