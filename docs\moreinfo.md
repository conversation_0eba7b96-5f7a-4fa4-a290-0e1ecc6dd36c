fetch /
  views```
import json
import traceback
from datetime import datetime, timedelta, timezone
from typing import List

from flask import Blueprint, jsonify, make_response, request, session, url_for
from werkzeug.utils import secure_filename

from lib.firestore_client import FirestoreClient
from models.PromptTemplate import ModelName, PromptTemplateDocument
from models.RequestSessionDocument import RequestSessionDocument
from website.authorization import authorization_required
from website.fetch_workflow.utils import helpers, policyweb_helpers
from website.google_cloud_init import GCS
from website.logger_framework import make_logger
from website.utils import db_utils, upload_helper

workflow = "fetch_workflow"

logger_info, logger_error = make_logger(workflow, __file__)

fetch = Blueprint("fetch_process", __name__)


@fetch.route("/feedback", methods=["POST"])
def feedback():
    try:
        feedback_request: dict = request.json
        session_id, prompt_index = feedback_request.get("id").split("-")
        prompt_index = int(prompt_index)

        info = feedback_request.get("info", None)
        response_feedback = None
        if info is not None:
            response_feedback = info == 1

        if "plcy" not in feedback_request["sessionId"]:
            session_document = RequestSessionDocument.get_by_id(session_id)
            session_document.handle_response_feedback(prompt_index, response_feedback)
            prompt_response_as_bq_dict = session_document.prompt_to_bq_response_dict(
                prompt_index
            )
            db_utils.add_BQ_session_prompt_response(prompt_response_as_bq_dict)
        else:
            policyweb_helpers.handle_policyweb_feedback(feedback_request)
        return ""
    except Exception as e:
        logger_error.error(
            f"{workflow} : feedback : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, check logs for more info"}


# File Upload
@fetch.route("/session/upload_files", methods=["PUT"])
@fetch.route("/fileupload", methods=["PUT"])
def uploaded():
    try:
        req_body = request.get_json()

        signed_urls, status_code = helpers.create_upload_url(request, session)
        response_body = None

        persist_to_session = req_body.get("persist_to_session", False)
        if persist_to_session and (status_code >= 200 and status_code < 300):
            response_body = helpers.upsert_session_from_upload(
                req_body, session, signed_urls
            )
        else:
            response_body = {"signed_urls": signed_urls}

        return make_response(jsonify(response_body), status_code)

    except Exception as e:
        logger_error.error(
            f"{workflow} : multimodalbot : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, check logs for more info"}


@fetch.route("/session/<session_id>", methods=["GET"])
def get_session_by_id(session_id):
    try:  # TODO: add user_email validation
        session_document = RequestSessionDocument.get_by_id(session_id)
        if not session_document:
            return make_response(
                {"error": f"No Session with id: {session_id} could be found."}, 404
            )
        else:
            response_body = session_document.to_dict(date_format_iso=False)
            return make_response(jsonify(response_body), 200)
    except Exception as e:
        logger_error.error(
            f"{workflow} : api/v1/session/{session_id}/prompt_response : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, check logs for more info"}


@fetch.route("/history_json", methods=["GET"])
# @authorization_required
def menu_history_json():
    try:
        user_email = session.get("email")
        ten_days_ago = datetime.now(timezone.utc) - timedelta(days=10)
        sessions_for_user = RequestSessionDocument.get_by_user_email(
            user_email, since_date_utc=ten_days_ago
        )
        filtered_and_sorted_sessions = sorted(
            [
                session
                for session in sessions_for_user
                if session.conversation_history
                and session.conversation_history[-1].response
            ],
            key=lambda session: max(
                prompt.response_time_utc for prompt in session.conversation_history
            ),
            reverse=True,
        )
        json_sessions = [
            session_document.to_dict(date_format_iso=True, include_document_id=True)
            for session_document in filtered_and_sorted_sessions
        ]
        return jsonify(json_sessions)

    except Exception as e:
        logger_error.error(
            f"{workflow} : menu_history_json : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, check logs for more info"}


@fetch.route("/history_json/<session_id>", methods=["GET"])
# @authorization_required
def history_json(session_id):
    try:
        session_document = RequestSessionDocument.get_by_id(session_id)
        return jsonify(
            session_document.to_dict(date_format_iso=True, include_document_id=True)
        )

    except Exception as e:
        logger_error.error(
            f"{workflow} : history_json : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, check logs for more info"}


@fetch.route("/session/<session_id>/prompt_response", methods=["GET"])
def get_latest_response_for_session(session_id):
    try:  # TODO: need to add some validation here to make sure the requesting user matches the session
        session_document = RequestSessionDocument.get_by_id(session_id)
        if not session_document:
            return make_response(
                {"error": f"No Session with id: {session_id} could be found."}, 404
            )

        if session_document.conversation_history:
            latest_prompt = session_document.conversation_history[-1]
            response_id = f"{session_document.id}-{len(session_document.conversation_history) - 1}"
            response_body = {
                "ready": bool(latest_prompt.response),
                "response": latest_prompt.response,
                "response_time_utc": (
                    latest_prompt.response_time_utc.isoformat()
                    if latest_prompt.response_time_utc
                    else None
                ),
            }
            if latest_prompt.response:
                response_body["response_id"] = response_id

            return make_response(jsonify(response_body), 200)
        else:
            return make_response(
                jsonify(
                    {
                        "error": f"Session with id: {session_id} exists but has no active prompts.",
                    }
                ),
                422,
            )  # Unprocessable Content - should only happen if someone calls this endpoint in a vacuum

    except Exception as e:
        logger_error.error(
            f"{workflow} : api/v1/session/{session_id}/prompt_response : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, check logs for more info"}


# Policyweb
@fetch.route("/query_policies", methods=["POST"])
def pw_query_policies():
    # try:
    data = request.json
    user = session["email"]
    logger_info.info(
        f"{workflow} : pw_query_policies : Returning API call for USER: {user}"
    )
    return policyweb_helpers.handle_policyweb_session(data, user)


@fetch.route("/word_export", methods=["POST"])
def word_export():
    try:

        data = request.get_json()
        if not data or "input" not in data:
            logger_error.error(
                f"{workflow} : generate_presentation : Missing 'input' in request"
            )
            return jsonify({"error": "Missing 'input' in request"}), 400

        response = data["input"]

        email = session["email"]
        gcs_object_name = helpers.create_word_doc(response, email)

        gcs_util = GCS()
        download_success, download_url = gcs_util.generate_download_signed_url_v4(
            gcs_object_name, email
        )

        if not download_success:
            logger_error.error(
                f"{workflow} : word_export : Failed to generate download URL"
            )
            return jsonify({"error": "Failed to generate download URL"}), 500

        logger_info.info(
            f"{workflow} : word_export : Successfully generated Word document."
        )

        return jsonify(
            {
                "success": True,
                "download_url": download_url,
                "message": "Word doc generated successfully.",
            }
        )

    except Exception as e:
        logger_error.error(
            f"{workflow} : word_export : {str(e)} : {traceback.format_exc()}"
        )
        return jsonify({"error": "Error generating Word document"}), 500


@fetch.route("/file_attestation", methods=["POST"])
def file_attestation():
    files = request.get_json().get("files_mimes", None)
    session_type = request.get_json().get("session_type", None)
    attestation_text = request.get_json().get("attestation_text", None)
    user = session["email"]
    if files is None:
        logger_error.error(
            f"{workflow} : file_attestation : {user} attested with no files"
        )
        return {"err", "Bad Request"}, 400

    filenames = []
    attestation_date = datetime.now(timezone.utc).isoformat()

    for file in files:
        filenames.append(secure_filename(file["file_name"]))

    user_dict = {
        "user": user,
        "filenames": filenames,
        "attestation_date": attestation_date,
        "attestation_text": attestation_text,
        "session_type": session_type,
    }

    db_utils.add_file_attestation(user_dict)
    return ""


@fetch.route("/prompt_templates/<session_type>", methods=["GET"])
def get_prompt_templates(session_type):
    """Fetch all prompt templates for the session type, CHAT, CODE, MDLM"""
    templates = PromptTemplateDocument.get_by_created_by_and_session(
        created_by="sidekick",
        session_type=session_type,
        client=FirestoreClient().InstanceClient("@global"),
    )
    json_templates = []
    for template in templates:
        json_templates.append(
            template.to_dict(date_format_iso=True, include_document_id=True)
        )
    return json_templates


@fetch.route("/prompt_templates/user", methods=["GET"])
def get_prompt_template_user():
    """Fetch all prompt templates from the user"""
    user = session["email"]
    templates = PromptTemplateDocument.get_by_created_by(
        created_by=user, client=FirestoreClient().InstanceClient(user)
    )
    json_templates = []
    for template in templates:
        json_templates.append(
            template.to_dict(date_format_iso=True, include_document_id=True)
        )
    return json_templates


@fetch.route("/prompt_templates/<session_type>/user", methods=["GET"])
def get_prompt_template_user_session(session_type):
    """Fetch prompt templates from the user by session"""
    user = session["email"]
    # user = "sidekick"  # temporary just to see
    templates = PromptTemplateDocument.get_by_created_by_and_session(
        created_by=user,
        session_type=session_type,
        client=FirestoreClient().InstanceClient(user),
    )
    json_templates = []
    for template in templates:
        json_templates.append(
            template.to_dict(date_format_iso=True, include_document_id=True)
        )
    return json_templates


@fetch.route("/prompt_templates/user", methods=["PUT"])
def add_user_prompt_template():
    """User based add prompt template."""
    data: dict = request.get_json()
    prompt = data.get("prompt", None)
    prompt_name = data.get("title", None)
    if not prompt_name:
        prompt_name = prompt[:50] if prompt else "New Prompt"
    session_type = data.get("session_type", "chat")
    temperature = data.get("temperature", None)
    user = session["email"]
    if session_type == "mdlm":
        model = ModelName.MEDLM.value
    else:
        model = ModelName.FLASH.value
    ptd = PromptTemplateDocument(
        name=prompt_name,
        created_by=user,
        prompt=prompt,
        model=model,
        session_type=session_type,
        temperature=temperature,
        client=FirestoreClient().InstanceClient(user),
        # TODO: for unittests just mock this ^ function to return mockfirestore
    )

    ptd_id = ptd.add()
    logger_info.info(
        f"{workflow} : add_user_prompt_template : {user} created prompt template with id of {ptd_id}"
    )
    return ptd.to_dict(date_format_iso=True, include_document_id=True)


@fetch.route("/prompt_templates/<template_id>/user", methods=["POST"])
def update_user_prompt_template(template_id):
    """Update user prompt template based with valid POST body fields"""
    user = session["email"]
    data: dict = request.get_json()
    new_name = data.get("name", None)
    new_prompt_text = data.get("prompt", None)
    new_system_instructions = data.get("system_instructions", None)
    new_temperature = data.get("temperature", None)

    template_document = PromptTemplateDocument.get_by_id(
        id=template_id, client=FirestoreClient().InstanceClient(user)
    )

    if not template_document:
        return make_response(
            {"error": f"No Session with id: {template_id} could be found."}, 404
        )

    updated_values = []
    if new_name is not None:
        template_document.name = new_name
        updated_values.append(f"name = {new_name}")
    if new_prompt_text is not None:
        template_document.prompt = new_prompt_text
        updated_values.append(f"prompt = {new_prompt_text}")
    if new_system_instructions is not None:
        template_document.system_instructions = new_system_instructions
        updated_values.append(f"system_instructions = {new_system_instructions}")
    if new_temperature is not None:
        template_document.temperature = new_temperature
        updated_values.append(f"temperature = {new_temperature}")

    try:
        template_document.update()
        logger_info.info(
            f"{workflow} : update_user_prompt_template : {user} updated prompt template {template_id} with new values: {', '.join(updated_values)}"
        )
        return template_document.to_dict(date_format_iso=True, include_document_id=True)
    except Exception as e:
        logger_error.error(
            f"{workflow} : update_user_prompt_template : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, check logs for more info"}, 500


@fetch.route("/prompt_templates/<template_id>/user", methods=["DELETE"])
def delete_user_prompt_template(template_id):
    """Delete user prompt template with id <template_id>"""
    user = session["email"]
    try:
        PromptTemplateDocument.delete_by_id(
            template_id, client=FirestoreClient().InstanceClient(user)
        )
        return make_response(jsonify({"deleted_entities": [template_id]}), 200)
    except ValueError as ve:
        return make_response(
            {"error": f"No Session with id: {template_id} could be found."}, 404
        )
    except Exception as e:
        logger_error.error(
            f"{workflow} : delete_user_prompt_template : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, check logs for more info"}, 500

```;

Views /
  views.py```
import os, traceback, html, json

from flask import Blueprint, make_response, render_template, request, session

from models.RequestSessionDocument import RequestSessionDocument
from website.authorization import authorization_required
from website.logger_framework import make_logger
from website.views_workflow.utils import helpers

workflow = "views_workflow"

logger_info, logger_error = make_logger(workflow, __file__)

views_print = Blueprint("views_process", __name__)


@views_print.route("/", methods=["GET"])
@views_print.route("/chat", methods=["GET"])
def multimodal_main():
    """Similar to the ones found in views_workflow/views.py"""
    try:
        return make_response(
            render_template("views/multimodal.html", user_email=session["email"])
        )

    except Exception as e:
        logger_error.error(
            f"{workflow} : multimodalbot : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, frontend render failure"}

@views_print.route("/chat/<session_id>/resume", methods=["GET"])
def multimodal_resume(session_id):
    try:
        user_email = session["email"]
        session_document = RequestSessionDocument.get_by_id(session_id)
        if not session_document or session_document.user_email.lower() != user_email.lower():
            return make_response(
                render_template("views/multimodal.html", user_email=session["email"])
            )
    
        for turn in session_document.conversation_history:
            turn.prompt = html.escape(turn.prompt)
            turn.response = html.escape(turn.response)
        document_json = json.dumps(session_document.to_dict(date_format_iso=True, include_document_id=True))

        return make_response(
            render_template("views/multimodal.html", user_email=session["email"], session_document=document_json)
        )
    except Exception as e:
        logger_error.error(
            f"{workflow} : multimodalbot : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, frontend render failure"}


@views_print.route("/code", methods=["GET", "POST"])
# @authorization_required
def code_main():
    try:
        return code_chat_frontend_response(
            request,
            {
                "session_name": "code_session",
                "template_name_or_list": "views/code.html",
            },
        )

    except Exception as e:
        logger_error.error(
            f"{workflow} : code_main : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, frontend render failure"}

@views_print.route("/code/<session_id>/resume", methods=["GET"])
def code_resume(session_id):
    try:
        user_email = session["email"]
        code_session_data = {
            "session_name": "code_session",
            "template_name_or_list": "views/code.html",
        }

        session_document = RequestSessionDocument.get_by_id(session_id)
        if not session_document or session_document.user_email.lower() != user_email.lower():
            return code_chat_frontend_response(request, code_session_data)
    
        for turn in session_document.conversation_history:
            turn.prompt = html.escape(turn.prompt)
            turn.response = html.escape(turn.response)
        document_json = json.dumps(session_document.to_dict(date_format_iso=True, include_document_id=True))

        code_session_data["session_document"] = document_json
        return code_chat_frontend_response(request, code_session_data)
    except Exception as e:
        logger_error.error(
            f"{workflow} : code_main : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, frontend render failure"}

@views_print.route("/med", methods=["GET", "POST"])
# @authorization_required
def medlm_main():
    try:
        return code_chat_frontend_response(
            request,
            {
                "session_name": "mdlm_session",
                "template_name_or_list": "views/medlm.html",
            },
        )
    except Exception as e:
        logger_error.error(
            f"{workflow} : medlm_main : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, frontend render failure"}

@views_print.route("/med/<session_id>/resume", methods=["GET", "POST"])
# @authorization_required
def medlm_resume(session_id):
    try:
        user_email = session["email"]
        medlm_session_data = {
            "session_name": "mdlm_session",
            "template_name_or_list": "views/medlm.html",
        }

        session_document = RequestSessionDocument.get_by_id(session_id)
        if not session_document or session_document.user_email.lower() != user_email.lower():
            return code_chat_frontend_response(request, medlm_session_data)
    
        for turn in session_document.conversation_history:
            turn.prompt = html.escape(turn.prompt)
            turn.response = html.escape(turn.response)
        document_json = json.dumps(session_document.to_dict(date_format_iso=True, include_document_id=True))

        medlm_session_data["session_document"] = document_json
        return code_chat_frontend_response(request, medlm_session_data)
    except Exception as e:
        logger_error.error(
            f"{workflow} : medlm_main : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, frontend render failure"}

@views_print.route("/translate")
def translate():
    try:
        return render_template("views/translate.html", user_email=session["email"])

    except Exception as e:
        logger_error.error(
            f"{workflow} : resources : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, frontend render failure"}

@views_print.route("/texttospeech")
def textToSpeech():
    try:
        return render_template("views/text_to_speech.html", user_email=session["email"])

    except Exception as e:
        logger_error.error(
            f"{workflow} : resources : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, check logs for more info"}
    

@views_print.route("/policyweb", methods=["GET", "POST"])
# @authorization_required
def policyweb():
    try:
        return code_chat_frontend_response(
            request,
            {
                "session_name": "policy_session",
                "template_name_or_list": "views/policyweb.html",
                "form": helpers.ChatForm(),
            },
        )

    except Exception as e:
        logger_error.error(
            f"{workflow} : code_main : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, frontend render failure"}


@views_print.route("/resources")
# @authorization_required
def resources():
    try:
        return render_template("views/resources.html")

    except Exception as e:
        logger_error.error(
            f"{workflow} : resources : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, frontend render failure"}


@views_print.route("/history", methods=["GET", "POST"])
# @authorization_required
def serve_menu_history():
    try:
        return history_frontend_response(
            {"template_name_or_list": "views/menu_history.html"}
        )

    except Exception as e:
        logger_error.error(
            f"{workflow} : serve_menu_history : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, frontend render failure"}


@views_print.route("/history/<id>", methods=["GET", "POST"])
# @authorization_required
def serve_chat_history(id):
    try:
        # session_list = show_single_session(id)
        # json_list = jsonify(session_list)
        return history_frontend_response(
            {"id": id, "template_name_or_list": "views/history.html"}
        )

    except Exception as e:
        logger_error.error(
            f"{workflow} : serve_chat_history : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, frontend render failure"}


def history_frontend_response(data):
    """Common function for history response"""
    try:
        response = make_response(
            render_template(
                **data,
                deployment=os.environ.get("DEPLOYMENT", ""),
                user_email=session["email"],
            )
        )

        return response

    except Exception as e:
        logger_error.error(
            f"{workflow} : history_frontend_response : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, frontend render failure"}


def code_chat_frontend_response(request, data: dict):
    """Common function to chat and code chat response"""
    try:
        response = make_response(
            render_template(
                data["template_name_or_list"],
                deployment=os.environ.get("DEPLOYMENT", ""),
                user_email=session["email"],
                form=data.get("form", None),
                session_document=data.get("session_document", None)
            )
        )

        if request.method == "GET":
            session.pop(data["session_name"], None)

        return response

    except Exception as e:
        logger_error.error(
            f"{workflow} : code_chat_frontend_response : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, frontend render failure"}

```;
1;
