import React from 'react';

const SidekickFeedbackIcon: React.FC = () => {
    return (
        <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_1687_9082)">
                <path d="M5.61865 7.03418C3.61984 7.03426 1.9996 8.65451 1.99951 10.6533V15.4795C1.99959 17.4783 3.61983 19.0986 5.61865 19.0986H16.4771C18.4759 19.0986 20.0961 17.4783 20.0962 15.4795V10.6533C20.0961 8.65447 18.4759 7.03421 16.4771 7.03418H5.61865ZM14.6675 14.4238C14.6675 15.3838 14.2857 16.3046 13.6069 16.9834C12.9283 17.6619 12.008 18.0429 11.0483 18.043C10.0885 18.043 9.16753 17.6621 8.48877 16.9834C7.81 16.3046 7.42822 15.3838 7.42822 14.4238H14.6675ZM16.0249 8.54297C17.2741 8.54302 18.2865 9.55545 18.2866 10.8047C18.2866 12.054 17.2742 13.0673 16.0249 13.0674C14.7756 13.0674 13.7622 12.054 13.7622 10.8047C13.7623 9.55542 14.7756 8.54297 16.0249 8.54297ZM6.0708 8.54297C7.32008 8.54297 8.33341 9.55542 8.3335 10.8047C8.3335 12.054 7.32014 13.0674 6.0708 13.0674C4.82165 13.0672 3.80908 12.0539 3.80908 10.8047C3.80917 9.55556 4.8217 8.54319 6.0708 8.54297Z" fill="#003963"/>
                <circle cx="10.7879" cy="2.15315" r="1.95296" fill="#003963"/>
                <path d="M10.7882 8.01227L9.81177 5.57108H11.7647L10.7882 3.12988" stroke="#003963" strokeWidth="0.732358"/>
            </g>
            <defs>
                <clipPath id="clip0_1687_9082">
                    <rect width="21.6" height="21.6" fill="white" transform="translate(0.199951 0.200195)"/>
                </clipPath>
            </defs>
        </svg>
    );
}

export default SidekickFeedbackIcon;
