import React from 'react';
import { LuFileText, LuTrash2 } from 'react-icons/lu';
import { useThemeStyles } from '@hooks/useThemeStyles';
import { FileCardProps } from '@features/workbook/workbookTypes';

const FileCard: React.FC<FileCardProps> = ({ fileName, onDelete }) => {
  const { classes } = useThemeStyles();

  const styles = {
    container: `flex h-[32px] p-[8px] items-center gap-[10px] rounded-[4px] ${classes.backgroundInput} min-w-[200px] max-w-[250px] transition-colors ${classes.hoverBackground}`,
    fileIcon: "w-[16px] h-[16px] flex-shrink-0 aspect-[1/1]",
    fileName: `flex-1 overflow-hidden ${classes.text} font-roboto text-[12px] font-normal leading-[130%] tracking-[0.096px] text-ellipsis whitespace-nowrap truncate`,
    deleteButton: `w-[16px] h-[16px] cursor-pointer ${classes.text} hover:text-red-400`,
    deleteIcon: "w-full h-full aspect-[1/1]"
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete();
  };

  return (
    <div className={styles.container}>
      <LuFileText className={styles.fileIcon} color="#F7987D" />
      <span className={styles.fileName}>
        {fileName}
      </span>
      <div
        className={styles.deleteButton}
        onClick={handleDelete}
        role="button"
        aria-label={`Delete ${fileName}`}
      >
        <LuTrash2 className={styles.deleteIcon} />
      </div>
    </div>
  );
};

export default FileCard;
