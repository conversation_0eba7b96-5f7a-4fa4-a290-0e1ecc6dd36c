import { useEffect, useRef, useCallback } from 'react';

// Smart scroll positioning constants
const SCROLL_POSITIONING = {
  // Container height thresholds (in pixels)
  LARGE_CONTAINER_HEIGHT: 800,
  MEDIUM_CONTAINER_HEIGHT: 600,

  // Message height ratio thresholds for center positioning
  LARGE_CONTAINER_MESSAGE_RATIO: 0.3, // 30% - In large containers, center if message < 30% of container height
  MEDIUM_CONTAINER_MESSAGE_RATIO: 0.4, // 40% - In medium containers, center if message < 40% of container height
} as const;

// A generic interface to accommodate different message types from chat and workbook
interface MessageLike {
  role?: 'user' | 'model' | 'system'; // chat
  conversationRole?: 'user' | 'model'; // workbook
}

interface UseAutoScrollProps {
  messages: MessageLike[];
  isLoading: boolean;
  messagesContainerRef: React.RefObject<HTMLDivElement | null>;
  scrollTargetRef: React.RefObject<HTMLDivElement | null>;
}

export const useAutoScroll = ({ messages, isLoading, messagesContainerRef, scrollTargetRef }: UseAutoScrollProps) => {
  const previousLoadingRef = useRef<boolean>(false);
  const previousMessageCountRef = useRef<number>(0);
  const isInitializedRef = useRef<boolean>(false);
  const scrollTimeoutRef = useRef<number | null>(null);

  // Debounced scroll function to prevent multiple rapid scrolls
  const debouncedScrollToLastMessage = useCallback(() => {
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    scrollTimeoutRef.current = setTimeout(() => {
      requestAnimationFrame(() => {
        const container = messagesContainerRef.current;
        if (!container) return;

        // Check if container is scrollable
        if (container.scrollHeight <= container.clientHeight) {
          return; // No need to scroll if content fits in container
        }

        // Find the last message element in the container
        const messageElements = container.querySelectorAll('[data-message-id]');
        const lastMessageElement = messageElements[messageElements.length - 1] as HTMLElement;

        if (lastMessageElement) {
          const containerHeight = container.clientHeight;
          const messageHeight = lastMessageElement.offsetHeight;

          let scrollBlock: ScrollLogicalPosition = 'start';

          // Smart scroll positioning based on message and container size
          if (containerHeight >= SCROLL_POSITIONING.LARGE_CONTAINER_HEIGHT) {
            // Large container: center small messages for better UX
            if (messageHeight < containerHeight * SCROLL_POSITIONING.LARGE_CONTAINER_MESSAGE_RATIO) {
              scrollBlock = 'center';
            }
          } else if (containerHeight >= SCROLL_POSITIONING.MEDIUM_CONTAINER_HEIGHT) {
            // Medium container: center slightly larger messages due to limited space
            if (messageHeight < containerHeight * SCROLL_POSITIONING.MEDIUM_CONTAINER_MESSAGE_RATIO) {
              scrollBlock = 'center';
            }
          }

          lastMessageElement.scrollIntoView({
            behavior: 'smooth',
            block: scrollBlock,
          });
        } else if (scrollTargetRef.current) {
          // Fallback to scroll target if message element not found
          scrollTargetRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'end',
          });
        }
      });
    }, 50); // 50ms debounce
  }, [messagesContainerRef, scrollTargetRef]);

  // Manual scroll function for external use
  const scrollToLastMessage = useCallback(() => {
    debouncedScrollToLastMessage();
  }, [debouncedScrollToLastMessage]);

  // Handle message changes and loading state changes
  useEffect(() => {
    const currentMessageCount = messages.length;
    const previousLoading = previousLoadingRef.current;
    const previousMessageCount = previousMessageCountRef.current;

    // Skip if no messages
    if (currentMessageCount === 0) {
      previousLoadingRef.current = isLoading;
      previousMessageCountRef.current = currentMessageCount;
      return;
    }

    const isAIResponseCompleted = previousLoading && !isLoading;
    const hasNewMessage = currentMessageCount > previousMessageCount;
    const lastMessage = messages[messages.length - 1];
    const isLastMessageFromAI =
      lastMessage && (lastMessage.role === 'model' || lastMessage.conversationRole === 'model');

    // Determine if we should scroll
    let shouldScroll = false;

    if (!isInitializedRef.current && currentMessageCount > 0) {
      // Initial load - scroll to show latest messages (for session restoration)
      shouldScroll = true;
      isInitializedRef.current = true;
    } else if (isAIResponseCompleted && currentMessageCount > 0) {
      // AI response completed - always scroll to show the AI's response
      shouldScroll = true;
    } else if (hasNewMessage && isLastMessageFromAI) {
      // New AI message added (for streaming or non-streaming AI responses) - scroll
      shouldScroll = true;
    }
    // Note: User messages (when isLastMessageFromAI is false) will NOT trigger auto-scroll

    if (shouldScroll) {
      debouncedScrollToLastMessage();
    }

    previousLoadingRef.current = isLoading;
    previousMessageCountRef.current = currentMessageCount;
  }, [messages, isLoading, debouncedScrollToLastMessage]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  return {
    scrollToLastMessage,
  };
};
