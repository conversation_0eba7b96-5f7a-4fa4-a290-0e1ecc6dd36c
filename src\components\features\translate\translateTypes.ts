export type TranslateDefaults = {
  message: string;
  inputLanguage: string;
  inputLanguageName: string;
  outputLanguage: string;
  outputLanguageName: string;
};
export type TranslateParams = {
  message: string;
  inputLanguage: string;
  outputLanguage: string;
};

export type TranslateTextPayload = {
  //Backend is `text`, `source_language`, `target_language`
  text: string;
  source_language: string;
  target_language: string;
};
export type TranslateUploadFile = {
  file_name: string;
  file_size: number;
  mime_type: string;
};

export type TranslateUploadPayload = {
  files_mimes: TranslateUploadFile[];
};

export type TranslateFile = {
  fileName: string;
  gcs_uri: string;
  mime_type: string;
  file_size: number;
};

export type TranslateFileParams = {
  translateFiles:TranslateFile[]
  target_language: string;
};

export type TranslateFilePayload = {
  fileName: string;
  gcs_uri: string;
  mime_type: string;
  file_size: number;
  target_language:string;
}[]

export type TranslateLanguagesJSON = {
  language_code: string;
  language_name: string;
}[];

export type DetectResponse = {
  language_code: string;
};

export type TranslateTextResponse = string[];

export type TranslateFileResponse = {
  download_url: string;
  message: string;
  success: boolean;
};

export type SignedURLInfo = {
  file_size: number;
  gs_uri: string;
  mime_type: string;
  upload_time_utc: string;
  url: string;
};

export type FileUploadResponse = {
  signed_urls: SignedURLInfo[];
};
