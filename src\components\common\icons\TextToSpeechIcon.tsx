import React from 'react';
const SidekickTextToSpeechIcon: React.FC = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="28"
      height="28"
      viewBox="0 0 28 28"
      fill="currentcolor"
    >
      <path d="M19.8333 23.7884C19.495 23.7884 19.18 23.7184 18.9467 23.6134C18.1183 23.1818 17.535 22.5868 16.9517 20.8368C16.3567 19.0168 15.2367 18.1651 14.1633 17.3368C13.2417 16.6251 12.285 15.8901 11.4567 14.3851C10.8383 13.2651 10.5 12.0401 10.5 10.9551C10.5 7.68842 13.0667 5.12175 16.3333 5.12175C19.6 5.12175 22.1667 7.68842 22.1667 10.9551H24.5C24.5 6.37008 20.9183 2.78842 16.3333 2.78842C11.7483 2.78842 8.16667 6.37008 8.16667 10.9551C8.16667 12.4251 8.61 14.0468 9.415 15.5051C10.4767 17.4301 11.725 18.3984 12.74 19.1801C13.685 19.9034 14.3617 20.4284 14.735 21.5718C15.435 23.6951 16.3333 24.8851 17.92 25.7134C18.515 25.9818 19.1683 26.1218 19.8333 26.1218C22.4117 26.1218 24.5 24.0334 24.5 21.4551H22.1667C22.1667 22.7384 21.1167 23.7884 19.8333 23.7884ZM8.91333 3.53508L7.25667 1.87842C4.935 4.20008 3.5 7.40842 3.5 10.9551C3.5 14.5018 4.935 17.7101 7.25667 20.0318L8.90167 18.3868C7.01167 16.4851 5.83333 13.8601 5.83333 10.9551C5.83333 8.05008 7.01167 5.42509 8.91333 3.53508ZM13.4167 10.9551C13.4167 12.5651 14.7233 13.8718 16.3333 13.8718C17.9433 13.8718 19.25 12.5651 19.25 10.9551C19.25 9.34508 17.9433 8.03842 16.3333 8.03842C14.7233 8.03842 13.4167 9.34508 13.4167 10.9551Z" />
    </svg>
  );
};

export default SidekickTextToSpeechIcon;
