import { client } from './client';
import { FileInfo } from '@/types/fileUpload';

import { API_BASE_PATH, POLLING_BASE_URL } from './apiConfig';
import { parseApiError } from './apiUtils';

export interface ChatInitiateResponse {
  response_success: boolean;
  session_id?: string;
  firestore_session_id?: string;
  response?: string;
}

export interface PollChatResponse {
  ready: boolean;
  response: string;
}

export type ChatType = 'General' | 'Code' | 'Medical' | 'Policy';

const MAX_OUTPUT_TOKENS = 8192;

interface ChatRequestBody {
  prompt: string;
  session_id: string | null;
  firestore_session_id: string | null;
  prompt_template_id?: string;
  prompt_template_is_global?: boolean;
  max_output_tokens: number;
  temperature: number;
  prompt_files?: FileInfo[];
}

export const sendChatMessage = async (
  prompt: string,
  model: ChatType,
  sessionId: string | null,
  firestoreSessionId: string | null,
  temperature: number,
  promptTemplateId?: string,
  promptTemplateIsGlobal?: boolean,
  prompt_files_payload?: FileInfo[]
): Promise<ChatInitiateResponse> => {
  let endpoint = '';
  switch (model) {
    case 'Code':
      endpoint = `${API_BASE_PATH}/codechatbot`;
      break;
    case 'Medical':
      endpoint = `${API_BASE_PATH}/medbot`;
      break;
    case 'General':
    default:
      endpoint = `${API_BASE_PATH}/bots_multimodal`;
      break;
  }

  const requestBody: ChatRequestBody = {
    prompt: prompt,
    session_id: sessionId,
    firestore_session_id: firestoreSessionId,
    max_output_tokens: MAX_OUTPUT_TOKENS,
    temperature: temperature,
  };

  if (model === 'General' && prompt_files_payload && prompt_files_payload.length > 0) {
    requestBody.prompt_files = prompt_files_payload;
  } else if (model !== 'General' && prompt_files_payload && prompt_files_payload.length > 0) {
    console.warn(
      `Prompt files payload provided for ${model} model, but backend might not support it or expect 'prompt_files' key with this structure.`
    );
  }

  if (promptTemplateId) {
    requestBody.prompt_template_id = promptTemplateId;
    requestBody.prompt_template_is_global = promptTemplateIsGlobal ?? false;
  }

  try {
    const response = await client.post<ChatInitiateResponse>(endpoint, requestBody);
    return response.data;
  } catch (error: unknown) {
    const parsedError = parseApiError(error);
    console.error(`Error sending chat message to ${endpoint}:`, parsedError.originalError || error);

    if (
      error &&
      typeof (error as ChatInitiateResponse).response_success === 'boolean' &&
      typeof (error as ChatInitiateResponse).response === 'string'
    ) {
      console.warn('Returning structured error from backend/client.ts rejection:', error);
      return error as ChatInitiateResponse;
    }

    return { response_success: false, response: parsedError.message };
  }
};

/**
 * Polls for the chat response using the firestoreSessionId.
 *
 */
export const pollChatResponse = async (firestoreSessionId: string): Promise<PollChatResponse> => {
  const endpoint = `${POLLING_BASE_URL}/${firestoreSessionId}/prompt_response`;
  try {
    const response = await client.get<PollChatResponse>(endpoint);
    const data = response.data;

    if (
      !data ||
      typeof data.ready !== 'boolean' ||
      (data.ready && typeof data.response !== 'string') ||
      (!data.ready && typeof data.response !== 'string' && data.response !== null)
    ) {
      console.error('Invalid poll response structure:', data);
      return { ready: false, response: 'Invalid poll response format received from server.' };
    }
    return data;
  } catch (error: unknown) {
    const parsedError = parseApiError(error);
    console.error(
      'Error polling chat response:',
      { firestoreSessionId },
      parsedError.originalError || error
    );

    return { ready: false, response: parsedError.message };
  }
};
