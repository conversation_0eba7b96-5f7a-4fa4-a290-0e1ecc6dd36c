import { createSidekickSlice } from '../withTypes';
import { getCurrentUser, GetUserResponse } from '@/api/userApi';

export interface AuthState {
  user: string | null;
  userRoles: string[];
  accessToken: string | null;
  refreshToken: string | null;
}

const initialState: AuthState = {
  user: null,
  userRoles: [],
  accessToken: null,
  refreshToken: null,
};

const authSlice = createSidekickSlice({
  name: 'auth',
  initialState: initialState,
  reducers: create => {
    return {
      fetchCurrentUser: create.asyncThunk(
        async () => {
          const data = await getCurrentUser();
          return data;
        },
        {
          fulfilled: (state, action) => {
            const data: GetUserResponse = action.payload;
            const { user, roles } = data;

            state.user = user;
            state.userRoles = roles;
          },
        }
      ),
    };
  },
  selectors: {
    selectCurrentUser: usersState => {
      return usersState.user;
    },
    selectCurrentUserRoles: usersState => {
      return usersState.userRoles;
    },
  },
});

export const { fetchCurrentUser } = authSlice.actions;
export const { selectCurrentUser, selectCurrentUserRoles } = authSlice.selectors;

export default authSlice.reducer;
