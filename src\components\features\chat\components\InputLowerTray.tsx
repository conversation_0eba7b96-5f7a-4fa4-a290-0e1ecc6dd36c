import React, { useEffect, useState } from 'react';
import { MdAttachFile } from 'react-icons/md';
import { FaArrowRight } from 'react-icons/fa';
import { AiOutlineLoading } from 'react-icons/ai';
import { useThemeStyles } from '@/hooks/useThemeStyles';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  selectSelectedChatType,
  selectPolicyLocationOptions,
  selectSelectedPolicyLocation,
  setSelectedPolicyLocation,
  selectSelectedTemperature,
  setSelectedTemperature,
} from '@/store/slices/chatSlice';
import PolicyLocationDropdown from '../../policy_web/components/PolicyLocationDropdown';
import { shouldShowFileUploadUI } from './fileUpload/utils/fileUploadHelpers';
const temperatureOptions = [
  { label: 'Less Creative', value: 0.2 },
  { label: 'Classic', value: 1.0 },
  { label: 'More Creative', value: 1.8 },
];

interface InputLowerTrayProps {
  onSubmit: () => void;
  onAttachFileClick?: () => void;
  isSendingMessage?: boolean;
  disableSubmit?: boolean;
}

interface RippleState {
  id: number;
  key: number | string;
  top: number;
  left: number;
  size: number;
  active: boolean;
}

/**
 * InputLowerTray component provides UI elements for chat input,
 * including temperature selection, file attachment, and message submission.
 */
const InputLowerTray: React.FC<InputLowerTrayProps> = ({
  onSubmit,
  onAttachFileClick,
  isSendingMessage = false,
  disableSubmit = false,
}) => {
  const { classes, isDarkMode } = useThemeStyles();
  const block = 'input-lower-tray';

  const dispatch = useAppDispatch();
  const selectedChatType = useAppSelector(selectSelectedChatType);
  const policyLocationOptions = useAppSelector(selectPolicyLocationOptions);
  const selectedPolicyLocation = useAppSelector(selectSelectedPolicyLocation);
  const selectedTemperature = useAppSelector(selectSelectedTemperature);

  const [localTemperature, setLocalTemperature] = useState(selectedTemperature);
  const [ripples, setRipples] = useState<RippleState[]>([]);

  useEffect(() => {
    if (selectedChatType !== 'Policy') {
      setLocalTemperature(selectedTemperature);
    }
  }, [selectedTemperature, selectedChatType]);

  // Effect to manage the 'active' state of ripples for animation.
  // It triggers a re-render to start the animation once a ripple is added.
  useEffect(() => {
    const pending = ripples.filter(r => !r.active);
    if (pending.length) {
      requestAnimationFrame(() => {
        setRipples(rippleList =>
          rippleList.map(r => (pending.some(p => p.id === r.id) ? { ...r, active: true } : r))
        );
      });
    }
  }, [ripples]);

  const coreBlue = '#0066B1';
  const hoverBlue = '#0075CA';
  const lightText = '#ECF7FF';
  const coreBlueRGB = '0, 102, 177';

  // Derived color strings for CSS variables
  const coreBlueRgba008 = `rgba(${coreBlueRGB},0.08)`;
  const coreBlueRgba01 = `rgba(${coreBlueRGB},0.1)`;

  const buttonBase = [
    'flex items-center justify-center',
    'h-10 px-4 text-[0.9rem] font-medium normal-case min-w-[85px]',
    'border transition-transform transition-colors transition-opacity',
    'duration-200 ease-out',
    'focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2',
    'hover:scale-105 active:scale-90 active:opacity-80',
    'relative overflow-hidden',
  ].join(' ');

  const getRounded = (i: number, len: number) =>
    len === 1 ? 'rounded-lg' : i === 0 ? 'rounded-l-lg' : i === len - 1 ? 'rounded-r-lg' : '';

  const getStateClasses = (selected: boolean) =>
    selected
      ? `bg-[var(--input-lower-tray-core-blue)] text-white border-[var(--input-lower-tray-core-blue)] hover:bg-[var(--input-lower-tray-hover-blue)] hover:border-[var(--input-lower-tray-hover-blue)]`
      : `${isDarkMode ? `text-[var(--input-lower-tray-light-text)]` : `text-[var(--input-lower-tray-core-blue)]`} border-[var(--input-lower-tray-core-blue)] hover:bg-[var(--input-lower-tray-core-blue-rgba008)]`;

  // Creates a ripple effect at the click location on a button.
  const createRipple = (e: React.MouseEvent<HTMLButtonElement>, key: number | string) => {
    const { left, top, width, height } = e.currentTarget.getBoundingClientRect();
    const size = Math.max(width, height) * 2;
    const x = e.clientX - left - size / 2;
    const y = e.clientY - top - size / 2;
    const ripple: RippleState = { id: Date.now(), key, top: y, left: x, size, active: false };
    setRipples(prev => [...prev, ripple]);

    setTimeout(() => setRipples(prev => prev.filter(r => r.id !== ripple.id)), 400);
  };

  const onTempClick = (value: number) => {
    if (selectedChatType !== 'Policy') {
      setLocalTemperature(value);
      dispatch(setSelectedTemperature(value));
    }
  };

  const handlePolicyLocationChange = (value: string) => {
    dispatch(setSelectedPolicyLocation(value));
  };

  const showAttach = shouldShowFileUploadUI(selectedChatType);

  return (
    <div
      className="input-lower-tray flex w-full items-center justify-between"
      style={
        {
          '--input-lower-tray-core-blue': coreBlue,
          '--input-lower-tray-hover-blue': hoverBlue,
          '--input-lower-tray-light-text': lightText,
          '--input-lower-tray-core-blue-rgba008': coreBlueRgba008,
          '--input-lower-tray-core-blue-rgba01': coreBlueRgba01,
        } as React.CSSProperties
      }
    >
      {selectedChatType === 'Policy' ? (
        <div className="input-lower-tray__policy-dropdown-container flex-shrink-0 w-auto min-w-[200px] max-w-[320px]">
          <PolicyLocationDropdown
            options={policyLocationOptions}
            selectedValue={selectedPolicyLocation}
            onChange={handlePolicyLocationChange}
            disabled={isSendingMessage}
            className=""
          />
        </div>
      ) : (
        <div
          role="group"
          aria-label="Temperature"
          className="input-lower-tray__temperature-group flex"
        >
          {temperatureOptions.map((opt, i, arr) => {
            const selected = localTemperature === opt.value;
            const bemButtonClass = 'input-lower-tray__temperature-button';
            const bemSelectedModifier = selected
              ? 'input-lower-tray__temperature-button--selected'
              : '';
            const classesArr = [
              bemButtonClass,
              bemSelectedModifier,
              buttonBase,
              getRounded(i, arr.length),
              getStateClasses(selected),
              i > 0 ? '-ml-px focus-visible:z-10' : '',
            ]
              .filter(Boolean)
              .join(' ');
            const rippleColor = selected
              ? 'rgba(255,255,255,0.15)'
              : isDarkMode
                ? 'rgba(255,255,255,0.1)'
                : 'var(--input-lower-tray-core-blue-rgba01)';

            return (
              <button
                key={opt.value}
                type="button"
                className={`${classesArr} font-roboto`}
                onClick={e => {
                  onTempClick(opt.value);
                  createRipple(e, opt.value);
                }}
              >
                <span className="input-lower-tray__temperature-label relative z-10 pointer-events-none font-roboto">
                  {opt.label}
                </span>
                {/* Render active ripples for this button */}
                {ripples
                  .filter(r => r.key === opt.value) // Show only ripples associated with this button.
                  .map(r => (
                    <span
                      key={r.id}
                      className="input-lower-tray__ripple absolute rounded-full pointer-events-none transform-gpu"
                      style={{
                        top: r.top,
                        left: r.left,
                        width: r.size,
                        height: r.size,
                        backgroundColor: rippleColor,
                        transform: r.active ? 'scale(1)' : 'scale(0)',
                        opacity: r.active ? 0 : 0.6,
                        transition: 'transform 400ms ease-out, opacity 400ms ease-out',
                      }}
                    />
                  ))}
              </button>
            );
          })}
        </div>
      )}
      {/* Action buttons: Attach file and Send message */}
      <div className={`${block}__actions flex items-center gap-2`}>
        {showAttach && (
          <button
            type="button"
            className={`${block}__action-button ${block}__action-button--attach ${classes.circleIconButton} active:scale-95 active:opacity-90 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-[var(--input-lower-tray-core-blue)] font-roboto`}
            aria-label="Attach file"
            onClick={onAttachFileClick}
            title="Click to attach files"
            disabled={isSendingMessage || (selectedChatType as string) === 'Policy'}
          >
            <MdAttachFile className={`${block}__attach-icon h-5 w-5`} />
          </button>
        )}
        <button
          type="button"
          onClick={onSubmit}
          className={`${block}__action-button ${block}__action-button--send ${classes.circleIconButton} active:scale-95 active:opacity-90 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-[var(--input-lower-tray-core-blue)] font-roboto disabled:border-[var(--text-text-disable)] disabled:text-[var(--text-text-disable)] disabled:cursor-not-allowed`}
          aria-label="Send message"
          disabled={isSendingMessage || disableSubmit }
        >
          {isSendingMessage ? (
            <AiOutlineLoading
              className={`${block}__send-icon ${block}__send-icon--loading h-5 w-5 animate-spin`}
            />
          ) : (
            <FaArrowRight className={`${block}__send-icon h-5 w-5`} />
          )}
        </button>
      </div>
    </div>
  );
};

export default InputLowerTray;
