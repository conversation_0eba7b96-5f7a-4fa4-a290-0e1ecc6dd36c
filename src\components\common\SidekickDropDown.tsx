import React from 'react';
import { Select } from '@base-ui-components/react/select';

import './SidekickDropDown.scss';
import { ArrowDropDown } from '@/components/common/icons';
import { SidekickDropDownProps } from '@/types/common';

const SidekickDropDown: React.FC<SidekickDropDownProps> = ({
  value,
  placeholder,
  select = [],
  disabled = false,
  iconSVG = <ArrowDropDown />,
  onChange,
}) => {
  const generateSelects = (select: { name: string; value: string }[]) => {
    const selects = select.map(v => {
      return (
        <Select.Item key={v.value} value={v.value} className="sidekick-dropdown__item">
          <Select.ItemText>{v.name}</Select.ItemText>
        </Select.Item>
      );
    });
    return selects;
  };

  return (
    <Select.Root value={value} onValueChange={onChange}>
      <Select.Trigger className="sidekick-dropdown__trigger" disabled={disabled}>
        <Select.Value placeholder={placeholder} />
        <Select.Icon className="sidekick-dropdown__icon">{iconSVG}</Select.Icon>
      </Select.Trigger>

      <Select.Portal>
        <Select.Positioner className="sidekick-dropdown__positioner" sideOffset={8}>
          <Select.Popup className="sidekick-dropdown__popup">{generateSelects(select)}</Select.Popup>
        </Select.Positioner>
      </Select.Portal>
    </Select.Root>
  );
};

export default SidekickDropDown;
