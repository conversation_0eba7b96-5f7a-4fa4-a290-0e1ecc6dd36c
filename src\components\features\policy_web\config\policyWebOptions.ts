export interface PolicyOption {
  value: string;
  label: string;
}

export interface PolicyOptionGroup {
  label: string;
  options: PolicyOption[];
}

export const policyLocationOptions: PolicyOptionGroup[] = [
  {
    label: 'Select an Organization',
    options: [{ value: 'All', label: 'Select an Organization (All)' }],
  },
  {
    label: 'Highmark Health',
    options: [
      { value: 'All Highmark', label: 'All Highmark' },
      { value: 'Highmark Health Plans', label: 'Highmark Health Plans' },
      { value: 'enGen', label: 'enGen' },
      { value: 'HM Insurance Group', label: 'HM Insurance Group' },
      { value: 'HM Home & Community Services', label: 'HM Home & Community Services' },
      { value: 'Highmark Wholecare', label: 'Highmark Wholecare' },
      { value: 'Thryve Digital', label: 'Thryve Digital' },
      { value: 'United Concordia Dental', label: 'United Concordia Dental' },
    ],
  },
  {
    label: 'Allegheny Health Network',
    options: [
      { value: 'All AHN', label: 'All AHN' },
      { value: 'Allegheny Health Network', label: 'Allegheny Health Network' },
      { value: 'Allegheny Clinic', label: 'Allegheny Clinic' },
      { value: 'Allegheny General Hospital', label: 'Allegheny General Hospital' },
      { value: 'Allegheny Valley Hospital', label: 'Allegheny Valley Hospital' },
      { value: 'Canonsburg Hospital', label: 'Canonsburg Hospital' },
      { value: 'Ambulatory Surgery Centers', label: 'Ambulatory Surgery Centers' },
      { value: 'Forbes Hospital', label: 'Forbes Hospital' },
      { value: 'Grove City Hospital', label: 'Grove City Hospital' },
      { value: 'Jefferson Hospital', label: 'Jefferson Hospital' },
      { value: 'Rural Health Clinic', label: 'Rural Health Clinic' },
      { value: 'Saint Vincent Hospital', label: 'Saint Vincent Hospital' },
      { value: 'Saint Vincent Endoscopy Center', label: 'Saint Vincent Endoscopy Center' },
      { value: 'Saint Vincent Surgery Center', label: 'Saint Vincent Surgery Center' },
      { value: 'Westfield Memorial Hospital', label: 'Westfield Memorial Hospital' },
      { value: 'West Penn Hospital', label: 'West Penn Hospital' },
      { value: 'Wexford Hospital', label: 'Wexford Hospital' },
    ],
  },
];

export const flatPolicyLocationOptions: PolicyOption[] = policyLocationOptions.flatMap(group => group.options);

export const defaultPolicyLocationValue = 'All';
