.policy-dropdown {
    position: relative;
    display: flex;
    align-items: center;
    height: 40px;
    min-width: 200px;
  
    // dropdown arrow
    &::after {
      content: '';
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
      width: 0;
      height: 0;
      border-left: 5px solid transparent;
      border-right: 5px solid transparent;
      border-top: 6px solid var(--policy-dropdown-core-blue);
      pointer-events: none;
      z-index: 1;
      transition: border-top-color 200ms ease-out;
  
      .dark & {
        border-top-color: var(--policy-dropdown-light-text);
      }
    }
  
    &:hover::after {
      border-top-color: var(--policy-dropdown-hover-blue);
      
      .dark & {
        border-top-color: var(--policy-dropdown-light-text);
      }
    }
  
    &__select {
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      border: 2px solid var(--policy-dropdown-core-blue);
      border-radius: 12px;
      padding-right: 40px;
      width: 100%;
      transition: all 200ms ease-out;
  
      &:focus {
        border-color: var(--policy-dropdown-hover-blue);
        box-shadow: 0 0 0 3px var(--policy-dropdown-core-blue-rgba008);
        outline: none;
      }
  
      &:hover:not(:disabled) {
        border-color: var(--policy-dropdown-hover-blue);
        background-color: var(--policy-dropdown-core-blue-rgba008);
        transform: translateY(-1px);
      }
  
      &:active:not(:disabled) {
        transform: translateY(0);
        box-shadow: 0 0 0 2px var(--policy-dropdown-core-blue);
      }
  
      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background-color: #f3f4f6;
        border-color: #d1d5db;
        color: #9ca3af;
  
        &:hover {
          transform: none;
          background-color: #f3f4f6;
          border-color: #d1d5db;
        }
      }
  
      .dark & {
        background-color: transparent;
        color: var(--policy-dropdown-light-text);
  
        &:hover:not(:disabled) {
          background-color: var(--policy-dropdown-core-blue-rgba008);
          border-color: var(--policy-dropdown-hover-blue);
        }
  
        &:disabled {
          background-color: #374151;
          border-color: #4b5563;
          color: #6b7280;
  
          &:hover {
            background-color: #374151;
            border-color: #4b5563;
          }
        }
      }
    }
  
    // style for option group
    &__option-group {
      font-weight: 600;
      padding: 8px 12px 4px;
      background-color: var(--policy-dropdown-core-blue);
      color: white;
      border-bottom: 1px solid var(--policy-dropdown-core-blue-rgba01);
  
      .dark & {
        background-color: #002d4f;
        color: var(--policy-dropdown-light-text);
      }
    }
  
    &__option {
      padding: 8px 12px;
      cursor: pointer;
      transition: background-color 150ms ease-out;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
  
      &:hover {
        background-color: var(--policy-dropdown-core-blue-rgba008);
      }
  
      &:selected {
        background-color: var(--policy-dropdown-core-blue);
        color: white;
      }
  
      .dark & {
        background-color: #002d4f;
        color: var(--policy-dropdown-light-text);
  
        &:hover {
          background-color: #003963;
        }
  
        &:selected {
          background-color: var(--policy-dropdown-core-blue);
          color: white;
        }
      }
    }
  }
  
  // for temperature button aligned
  .policy-dropdown--temperature-aligned {
    height: 40px;
    min-width: 200px;
    flex: 1;
    max-width: 320px;
  
    .policy-dropdown__select {
      height: 100%;
      width: 100%;
    }
  }
