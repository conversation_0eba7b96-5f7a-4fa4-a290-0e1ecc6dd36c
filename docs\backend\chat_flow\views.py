import traceback

from flask import Blueprint, jsonify, make_response, request, session

from website.authorization import authorization_required
from website.chat_workflow.utils import helpers
from website.logger_framework import make_logger

workflow = "chat_workflow"

logger_info, logger_error = make_logger(workflow, __file__)

chat = Blueprint("chat_process", __name__)


@chat.route("/bots_multimodal", methods=["POST"])
def multichatbot():
    """POST API for multimodal.

    Returns:
        Render for frontend
    """
    try:
        response = helpers.async_bots(request, session, "chat")
        return make_response(jsonify(response[0]), response[1])

    except Exception as e:
        logger_error.error(
            f"{workflow} : multichatbot : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, check logs for more info"}


@chat.route("/codechatbot", methods=["POST"])
# @authorization_required
def codechatbot():
    try:
        response = helpers.async_bots(request, session, "code")
        return make_response(jsonify(response[0]), response[1])

    except Exception as e:
        logger_error.error(
            f"{workflow} : codechatbot : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, check logs for more info"}


@chat.route("/medbot", methods=["POST"])
# @authorization_required
def medchatbot():
    try:
        response = helpers.async_bots(request, session, "mdlm")
        return make_response(jsonify(response[0]), response[1])

    except Exception as e:
        logger_error.error(
            f"{workflow} : medchatbot : {str(e)} : {traceback.format_exc()}"
        )
        return {"msg": "error, check logs for more info"}


# # TODO: convert frontend to use this?
# @chat.route("/prompt_model/<model_id>", methods=["POST"])
# def handle_model_prompt(model_id):
#     allowed = ["chat", "code", "mdlm"]
#     if model_id in allowed:
#         response = helpers.async_bots(request, session, model_id)
#         return make_response(jsonify(response[0]), response[1])
#     else:
#         return {"msg": f"invalid model_id, valid model_ids: {allowed}"}
