import { useCallback, useState, useRef } from 'react';
import { requestSignedUrlsForUpload, uploadFileToSignedUrl } from '@/api/fileUploadApi';
import { ValidatedFile } from '@/types/fileUpload';
import { cleanFileContent } from '@/components/features/chat/components/fileUpload/utils/fileUploadUtils';
import { useAppDispatch } from '@/store/hooks';
import { setSessionIds } from '@/store/slices/chatSlice';

interface FileMimeInfo {
  file_name: string;
  mime_type: string;
  file_size: number;
}

export interface SignedUrlEntry {
  gs_uri: string;
  mime_type: string;
  file_size: number;
  url: string;
}

interface RequestSignedURLsPayload {
  files_mimes: FileMimeInfo[];
  persist_to_session?: boolean;
  session_id?: string | null;
  session_type: string;
}

interface RequestSignedURLsResponse {
  signed_urls: SignedUrlEntry[];
  firestore_session_id?: string;
}

export interface GCSUploadManagerResult {
  fetchBatchSignedUrls: (payload: RequestSignedURLsPayload) => Promise<RequestSignedURLsResponse>;
  uploadFileToGCSWithSignedUrl: (
    fileToUpload: ValidatedFile,
    signedUrlInfo: SignedUrlEntry
  ) => Promise<Partial<ValidatedFile>>;
  cancelUpload: (fileId: string) => boolean;
  isUploading: (fileId: string) => boolean;
  isCancellationRequested: (fileId: string) => boolean;
  cancelAllUploads: () => void;
}

export interface UploadProgress {
  fileId: string;
  status: 'uploading' | 'success' | 'error' | 'cancelled';
  gcs_uri?: string;
  error?: string;
}

export const useGCSUploadManager = (): GCSUploadManagerResult => {
  const dispatch = useAppDispatch();
  const [uploadingFiles, setUploadingFiles] = useState<Record<string, AbortController>>({});
  const cancellationRequestsRef = useRef<Set<string>>(new Set());

  const cancelUpload = useCallback(
    (fileId: string): boolean => {
      try {
        cancellationRequestsRef.current.add(fileId);

        if (uploadingFiles[fileId]) {
          uploadingFiles[fileId].abort();
          setUploadingFiles(prev => {
            const newUploadingFiles = { ...prev };
            delete newUploadingFiles[fileId];
            return newUploadingFiles;
          });
          console.log(`[useGCSUploadManager] Cancelled active upload for file ID: ${fileId}`);
          return true;
        }

        console.log(`[useGCSUploadManager] Marked file for cancellation: ${fileId}`);
        return true;
      } catch (error) {
        console.error(`[useGCSUploadManager] Failed to cancel upload for file ${fileId}:`, error);
        return false;
      }
    },
    [uploadingFiles]
  );

  const isUploading = useCallback(
    (fileId: string): boolean => {
      return !!uploadingFiles[fileId];
    },
    [uploadingFiles]
  );

  const isCancellationRequested = useCallback((fileId: string): boolean => {
    return cancellationRequestsRef.current.has(fileId);
  }, []);

  const cancelAllUploads = useCallback(() => {
    Object.keys(uploadingFiles).forEach(fileId => {
      cancelUpload(fileId);
    });
  }, [uploadingFiles, cancelUpload]);

  const fetchBatchSignedUrls = useCallback(
    async (payload: RequestSignedURLsPayload): Promise<RequestSignedURLsResponse> => {
      const apiPayload = {
        ...payload,
        // Default to false to prevent creating new sessions during file upload
        persist_to_session: false,
        session_id: payload.session_id === undefined ? null : payload.session_id,
      };

      const response = await requestSignedUrlsForUpload(apiPayload);
      if (
        response.firestore_session_id &&
        apiPayload.session_id !== response.firestore_session_id
      ) {
        if (!apiPayload.session_id || apiPayload.session_id !== response.firestore_session_id) {
          dispatch(setSessionIds({ firestoreId: response.firestore_session_id }));
        }
      }
      return response;
    },
    [dispatch]
  );

  const uploadFileToGCSWithSignedUrl = useCallback(
    async (
      validatedFile: ValidatedFile,
      signedUrlInfo: SignedUrlEntry
    ): Promise<Partial<ValidatedFile>> => {
      if (cancellationRequestsRef.current.has(validatedFile.id)) {
        console.log(
          `[useGCSUploadManager] File ${validatedFile.id} was already marked for cancellation`
        );
        cancellationRequestsRef.current.delete(validatedFile.id);
        return { uploadStatus: 'cancelled', uploadError: 'Upload cancelled by user.' };
      }

      const abortController = new AbortController();
      setUploadingFiles(prev => ({ ...prev, [validatedFile.id]: abortController }));

      try {
        if (cancellationRequestsRef.current.has(validatedFile.id)) {
          abortController.abort();
          cancellationRequestsRef.current.delete(validatedFile.id);
          return { uploadStatus: 'cancelled', uploadError: 'Upload cancelled by user.' };
        }

        if (
          validatedFile.originalFile.size !== signedUrlInfo.file_size ||
          validatedFile.originalFile.type !== signedUrlInfo.mime_type
        ) {
          console.warn(
            `[GCSUploadManager] Potential metadata mismatch for file ${validatedFile.name}. ` +
              `Client: ${validatedFile.originalFile.size} bytes, ${validatedFile.originalFile.type}. ` +
              `Server: ${signedUrlInfo.file_size} bytes, ${signedUrlInfo.mime_type}. ` +
              `Proceeding with upload using server-provided mime_type for Content-Type header.`
          );
        }

        const fileToUpload = await cleanFileContent(validatedFile.originalFile);
        await uploadFileToSignedUrl(
          signedUrlInfo.url,
          fileToUpload,
          signedUrlInfo.mime_type,
          abortController.signal
        );

        return { uploadStatus: 'success', gcs_uri: signedUrlInfo.gs_uri };
      } catch (uploadError: any) {
        const errorMessage = (uploadError as Error).message || 'Upload to storage failed.';
        if (errorMessage.includes('cancelled') || (uploadError as Error).name === 'AbortError') {
          return { uploadStatus: 'cancelled', uploadError: 'Upload cancelled by user.' };
        }
        return { uploadStatus: 'error', uploadError: errorMessage };
      } finally {
        cancellationRequestsRef.current.delete(validatedFile.id);
        setUploadingFiles(prev => {
          const newUploadingFiles = { ...prev };
          delete newUploadingFiles[validatedFile.id];
          return newUploadingFiles;
        });
      }
    },
    []
  );

  return {
    fetchBatchSignedUrls,
    uploadFileToGCSWithSignedUrl,
    cancelUpload,
    isUploading,
    isCancellationRequested,
    cancelAllUploads,
  };
};

