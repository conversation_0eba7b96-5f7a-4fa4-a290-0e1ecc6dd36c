// A tiny wrapper around fetch(), borrowed from
// https://kentcdodds.com/blog/replace-axios-with-a-simple-custom-fetch-wrapper

import { refreshAuthFlow, checkAuth } from './auth';

interface ClientResponse<T> {
  status: number;
  data: T;
  headers: Headers;
  url: string;
}

import { toast } from 'react-toastify';

export async function client<T>(
  endpoint: string,
  { body, method, ...customConfig }: Partial<RequestInit> = {}
): Promise<ClientResponse<T>> {
  // doing this first
  const { hostname } = window.location;
  if (
    !(
      hostname.includes('localhost') ||
      hostname.includes('127.0.0.1') ||
      hostname.includes('0.0.0.0')
    )
  ) {
    const authTest = checkAuth();
    // access_token && refresh_token = everything works as normal;
    // access_token && !refresh_token = everything works as normal;
    // !access_token && refresh_token = refresh process happens;
    // !access_token && !refresh_token == refresh window
    if (authTest === 1) {
      toast('Reauthenticating...');
      await refreshAuthFlow();
      toast('Reauthentication completed!');
    } else if (authTest === 2) {
      // Locally causes an infinite loading loop as we dont have an auth process to generate a refresh_token
      window.open('/sidekick', '_self');
    }
  }

  const headers = { 'Content-Type': 'application/json' };

  const config: RequestInit = {
    method: method ? method : 'GET',
    ...customConfig,
    headers: {
      ...headers,
      ...customConfig.headers,
    },
  };

  if (body) {
    config.body = JSON.stringify(body);
  }

  let data;
  try {
    const response = await window.fetch(endpoint, config);
    data = await response.json();
    if (response.ok) {
      // if (data.reauth) {
      //   //TODO
      //   // If no auth, how to restart the fetch
      //   await refreshAuthFlow();

      // }
      // Return a result object similar to Axios
      return {
        status: response.status,
        data,
        headers: response.headers,
        url: response.url,
      };
    }
    throw new Error(response.statusText);
  } catch (err: any) {
    return Promise.reject(err.message ? err.message : data);
  }
}

client.get = function <T>(endpoint: string, customConfig: Partial<RequestInit> = {}) {
  return client<T>(endpoint, { ...customConfig, method: 'GET' });
};

client.post = function <T>(endpoint: string, body: any, customConfig: Partial<RequestInit> = {}) {
  return client<T>(endpoint, { ...customConfig, method: 'POST', body });
};

client.put = function <T>(endpoint: string, body: any, customConfig: Partial<RequestInit> = {}) {
  return client<T>(endpoint, { ...customConfig, method: 'PUT', body });
};

client.patch = function <T>(endpoint: string, body: any, customConfig: Partial<RequestInit> = {}) {
  return client<T>(endpoint, { ...customConfig, method: 'PATCH', body });
};

client.delete = function <T>(endpoint: string, customConfig: Partial<RequestInit> = {}) {
  return client<T>(endpoint, { ...customConfig, method: 'DELETE' });
};

