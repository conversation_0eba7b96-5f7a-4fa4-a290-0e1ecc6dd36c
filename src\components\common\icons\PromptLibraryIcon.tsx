import React from 'react';

const SidekickPromptLibraryIcon: React.FC = () => {
    return (
        <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M24.5003 4.66732C23.2053 4.25898 21.782 4.08398 20.417 4.08398C18.142 4.08398 15.692 4.55065 14.0003 5.83398C12.3087 4.55065 9.85866 4.08398 7.58366 4.08398C5.30866 4.08398 2.85866 4.55065 1.16699 5.83398V22.9257C1.16699 23.2173 1.45866 23.509 1.75033 23.509C1.86699 23.509 1.92533 23.4507 2.04199 23.4507C3.61699 22.6923 5.89199 22.1673 7.58366 22.1673C9.85866 22.1673 12.3087 22.634 14.0003 23.9173C15.5753 22.9257 18.4337 22.1673 20.417 22.1673C22.342 22.1673 24.3253 22.5173 25.9587 23.3923C26.0753 23.4507 26.1337 23.4507 26.2503 23.4507C26.542 23.4507 26.8337 23.159 26.8337 22.8673V5.83398C26.1337 5.30898 25.3753 4.95898 24.5003 4.66732ZM24.5003 20.4173C23.217 20.009 21.817 19.834 20.417 19.834C18.4337 19.834 15.5753 20.5923 14.0003 21.584V8.16732C15.5753 7.17565 18.4337 6.41732 20.417 6.41732C21.817 6.41732 23.217 6.59232 24.5003 7.00065V20.4173Z" fill="white"/>
            <path d="M20.417 11.084C21.4437 11.084 22.4353 11.189 23.3337 11.3873V9.61399C22.412 9.43898 21.4203 9.33399 20.417 9.33399C18.4337 9.33399 16.637 9.67232 15.167 10.3023V12.239C16.4853 11.4923 18.317 11.084 20.417 11.084Z" fill="white"/>
            <path d="M15.167 13.4057V15.3423C16.4853 14.5957 18.317 14.1873 20.417 14.1873C21.4437 14.1873 22.4353 14.2923 23.3337 14.4907V12.7173C22.412 12.5423 21.4203 12.4373 20.417 12.4373C18.4337 12.4373 16.637 12.7873 15.167 13.4057Z" fill="white"/>
            <path d="M20.417 15.5523C18.4337 15.5523 16.637 15.8907 15.167 16.5207V18.4573C16.4853 17.7107 18.317 17.3023 20.417 17.3023C21.4437 17.3023 22.4353 17.4073 23.3337 17.6057V15.8323C22.412 15.6457 21.4203 15.5523 20.417 15.5523Z" fill="white"/>
        </svg>
    )
};

export default SidekickPromptLibraryIcon;
