document.addEventListener("DOMContentLoaded", function () {
  if (!checkIfAllowed(false)) {
    //Prevent AHN, UCD, Wholecare from seeing Multimodal popup
    const uploadButton = document.getElementById("upload-button-id");
    const downloadButton = document.getElementById("download-from-url");
    downloadButton.remove();
    uploadButton.remove();
  }
  const uploadArea = document.getElementById("translate-drop-area-1");
  uploadArea.addEventListener("click", allowUpload);
});

function allowUpload() {
  //   if (showLegalAttestation()) {
  uploadFile();
  //   }
}

let file_form;
async function uploadFile() {
  const fileInput = document.createElement("input");
  const uploadButton = document.getElementById("upload-button-id");
  const translateButton = document.getElementById("translate-button-id");
  const uploadSpinner = document.getElementById("translateUploadStatus");
  const uploadField = document.getElementById("translate-drop-area-1");
  const fileList1 = document.getElementById("file-list-1");
  const targetText = document.getElementById("target-text");

  fileInput.type = "file";

  fileInput.accept = ".ppt, .pptx, .pdf, .doc, .docx, .xls, .xlsx";
  fileInput.onchange = async () => {
    const file = fileInput.files[0];
    const formData = new FormData();
    const translateFormData = new FormData();
    if (!validateTranslateFile(file)) {
      targetText.innerHTML = `Uploaded file is not supported, please make sure your file is under 1GB and is one of the supported file types: ${fileInput.accept}`;
      // console.log("Hello world");
      return;
    }
    formData.append("file", file);
    // Resets global var in uploadHandlers.js
    fileAndMime = [];

    for (const [key, value] of formData.entries()) {
      if (value instanceof File) {
        const mimeType = value.type;
        const fileName = value.name;
        const fileSize = value.size;
        translateFormData.append("fileName", fileName);
        fileAndMime.push({
          file_name: fileName,
          mime_type: mimeType,
          file_size: fileSize,
        });
      }
    }
    let bodyVariable = JSON.stringify({
      files_mimes: fileAndMime,
    });
    if (showLegalAttestation()) {
      let error_flag = false;
      try {
        fileList1.innerHTML = "";
        uploadSpinner.style.display = "block";
        // uploadButton.innerHTML = '<div class="spinner"></div>';

        uploadField.disabled = true;
        const result_1 = await fetch(fileLink, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: bodyVariable,
        })
          .then((response) => {
            if (response.ok === true) {
              return response.json();
            } else {
              console.log("Upload Failed! Response != OK");
              let error_text = `Upload failed! Error: ${response.status} ${response.statusText}`;
              targetText.innerHTML = error_text;
              error_flag = true;
              // uploadStatusAlert.textContent = "Upload failed!";
              // uploadStatusAlert.className = "alert-failure";
              // cancelButton.disabled = false;
              // submitPrompt.disabled = false;
              throw new Error(error_text);
              return false;
            }
          })
          .then((data) => {
            let file_lists = handleFileResponse(data);
            return file_lists;
          });
        // console.log(result_1);
        if (result_1 != false) {
          const result_2 = await uploadWrapper(formData, result_1[0]);
          // console.log(result_1[1][0]["gcs_path"]);
          let gcs_uri = result_1[1][0]["gcs_path"];
          let mime_type = result_1[1][0]["mime_type"];
          let file_size = result_1[1][0]["file_size"];

          translateFormData.append("gcs_uri", gcs_uri);
          translateFormData.append("mime_type", mime_type);
          translateFormData.append("file_size", file_size);
          file_form = translateFormData;
        }
        // formData.append("gcs_uri", result_2);
      } catch (error) {
        console.error("Upload failed:", error);
      } finally {
        if (error_flag != true) {
          console.log("Upload success");

          translateButton.disabled = false;
          // Show file to user
          const listItem = document.createElement("li");
          listItem.textContent = file.name;
          fileList1.appendChild(listItem);
          targetText.innerHTML =
            "File is ready for translation! Click Translate to proceed";
        } else {
          console.log("Upload failed");
        }
        uploadField.disabled = false;
        uploadSpinner.style.display = "none";
      }
    }
  };
  fileInput.click();
}

let downloadURL;
function handleTranslateDownload(data) {
  const success = data.success;
  const message = data.message;
  downloadURL = data.download_url;
  const downloadButton = document.getElementById("download-from-url");
  const targetText = document.getElementById("target-text");

  downloadButton.removeEventListener("click", openLink);

  if (!success) {
    console.log("Success == False");
    downloadButton.disabled = true;
    targetText.innerHTML = data.message; //only writes error msg if its from JSON.
    // Write failure message to screen. Keep download button locked.
  } else {
    downloadButton.disabled = false;
    targetText.innerHTML =
      "File is ready for download! Click Download for your translated document";
  }
  downloadButton.addEventListener("click", openLink);
}
function openLink(down) {
  window.open(downloadURL, "_blank", "noopener noreferrer");
}

let text_or_document = true;
function displayTranslateUpload() {
  const uploadButton = document.getElementById("upload-button-id");
  const divInput = document.getElementById("translation-input");
  const textInput = document.getElementById("source-text");
  const uploadField = document.getElementById("translate-drop-area-1");
  const translateButton = document.getElementById("translate-button-id");
  const targetText = document.getElementById("target-text");
  if (text_or_document) {
    // Upload
    targetText.innerHTML = "Upload file for translation";
    uploadButton.innerHTML = "Text";
    translateButton.disabled = true;
    textInput.style.display = "none";
    uploadField.style.display = "block";
    text_or_document = false;
  } else {
    //Text
    targetText.innerHTML = "";
    uploadButton.innerHTML = "Document";
    translateButton.disabled = false;
    textInput.style.display = "block";
    uploadField.style.display = "none";
    text_or_document = true;
  }
}

async function translateFile(formData) {
  const translateButton = document.getElementById("translate-button-id");
  const targetLanguageSelect = document.getElementById(
    "target-language-select"
  );
  const targetText = document.getElementById("target-text");
  const targetLanguage = targetLanguageSelect.value || "en"; // Default to English if no selection

  formData.set("target_language", targetLanguage);
  translateButton.innerHTML = '<div class="spinner"></div>';

  const response = await fetch("/sidekick/translate/file", {
    method: "POST",
    body: formData,
  });
  const resHeaders = response.headers.get("Content-Type");
  if (response.ok && resHeaders.includes("application/json")) {
    const data = await response.json();
    // alert(data.message);
    handleTranslateDownload(data);
  } else if (!response.ok && resHeaders.includes("application/json")) {
    const errorData = await response.json();
    // alert(errorData.error);
  } else if (response.ok && resHeaders.includes("text/html")) {
    let errorHTML = await response.text();
    errorHTML = escapeHtml(errorHTML);
    let shownError = `Error: An unexpected response was received:<br>${errorHTML} <br>Response status: ${response.status} ${response.statusText}`;
    targetText.innerHTML = shownError;
    console.error(shownError);
  }
  // uploadButton.disabled = false;
  translateButton.innerHTML = "Translate";
  translateButton.disabled = true; //Prevent file write error for translating twice on same file same language

  // uploadButton.innerHTML = "Upload Files";
  // uploadButton.disabled = true;
  // cancelButton.disabled = false;
  // submitPrompt.disabled = false;
  // uploadStatusAlert.style.display = "none";
  // showUploaded();
  // clearFiles();
}

function translateHandler() {
  if (text_or_document) {
    translateText();
  } else {
    // uploadFile();
    translateFile(file_form);
  }
}

function validateTranslateFile(file) {
  // Define allowed file types
  const allowedFileTypes = [
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/pdf",
    "application/vnd.ms-powerpoint",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  ]; // Example file types

  // Define maximum file size in bytes
  const maxFileSize = 1025 * 1024 * 1024; // 1074790400 bytes versus actual 1GB in bytes of 1073741824

  // Check if file type is allowed
  if (allowedFileTypes.includes(file.type)) {
    // Check if file size is within the limit
    if (file.size <= maxFileSize) {
      return true;
    }
  }
  return false;
}
