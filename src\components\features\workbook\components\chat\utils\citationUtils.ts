import { RetrievalCitation } from '../../../workbookTypes';

export interface ProcessedCitation {
  originalIndex: number;
  citationNumber: number;
  citation: RetrievalCitation;
}

export interface CitationProcessingResult {
  processedContent: string;
  citationMap: Map<number, ProcessedCitation>;
}

/**
 * Processes content with citations to insert citation markers in markdown format
 * @param content - Original message content
 * @param citations - Array of citation objects
 * @returns Processed content with citation markers and citation mapping
 */
export function processCitationsIntoMarkdown(
  content: string,
  citations: RetrievalCitation[] = []
): CitationProcessingResult {
  if (!citations || citations.length === 0) {
    return {
      processedContent: content,
      citationMap: new Map(),
    };
  }

  // Sort citations by start_index to process in order
  const sortedCitations = [...citations].sort((a, b) => a.start_index - b.start_index);

  const citationMap = new Map<number, ProcessedCitation>();
  let processedContent = '';
  let lastIndex = 0;

  sortedCitations.forEach((citation, idx) => {
    const citationNumber = idx + 1;

    // Add text before citation
    if (citation.start_index > lastIndex) {
      processedContent += content.substring(lastIndex, citation.start_index);
    }

    // Add cited text with citation marker
    const citedText = content.substring(citation.start_index, citation.end_index);
    // Remove trailing whitespace from cited text and add citation marker
    const cleanedCitedText = citedText.replace(/\s+$/, '');
    processedContent += `${cleanedCitedText}^[${citationNumber}]^`;

    // Store citation mapping
    citationMap.set(citationNumber, {
      originalIndex: idx,
      citationNumber,
      citation,
    });

    lastIndex = citation.end_index;
  });

  // Add remaining text after last citation
  if (lastIndex < content.length) {
    processedContent += content.substring(lastIndex);
  }

  return {
    processedContent,
    citationMap,
  };
}

/**
 * Extracts citation number from superscript text
 * @param superscriptText - Text content of superscript element
 * @returns Citation number or null if not a citation
 */
export function extractCitationNumber(superscriptText: string): number | null {
  const match = superscriptText.match(/\[(\d+)\]/);
  return match ? parseInt(match[1], 10) : null;
}
