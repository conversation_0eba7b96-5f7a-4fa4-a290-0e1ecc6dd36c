import { Workbook, WorkbookDashboardData } from '@/components/features/workbook/workbookTypes';

/**
 * Mock data for workbooks with clear public/personal differentiation
 */

// Common workbooks that appear in both personal and public collections
const sharedWorkbooks: Partial<Workbook>[] = [
  {
    id: '1',
    title: 'Machine Learning Basics',
    lastEdited: new Date('2025-01-01'),
  },
  {
    id: '2',
    title: 'React Hooks Deep Dive',
    lastEdited: new Date('2024-12-15'),
  },
  {
    id: '3',
    title: 'TypeScript Advanced Types',
    lastEdited: new Date('2024-11-20'),
  },
];

// Workbooks that only appear in the public collection
const publicOnlyWorkbooks: Partial<Workbook>[] = [
  {
    id: '4',
    title: 'Data Visualization with D3',
    lastEdited: new Date('2024-10-05'),
  },
  {
    id: '5',
    title: 'Web Performance Optimization',
    lastEdited: new Date('2024-09-15'),
  },
  {
    id: '6',
    title: 'CSS Grid Mastery',
    lastEdited: new Date('2024-08-22'),
  },
];

// Assemble the collections with proper flags
export const personalWorkbooks: Workbook[] = sharedWorkbooks.map(
  wb =>
    ({
      ...wb,
      isPersonal: true,
      isPublic: false,
    }) as Workbook
);

export const publicWorkbooks: Workbook[] = [
  ...sharedWorkbooks.map(wb => ({
    ...wb,
    isPersonal: false,
    isPublic: true,
  })),
  ...publicOnlyWorkbooks.map(wb => ({
    ...wb,
    isPersonal: false,
    isPublic: true,
  })),
] as Workbook[];

// Get files for a workbook based on its ID
export const getWorkbookFiles = (workbookId: string): string[] => {
  // Create common base files
  const baseFileName = workbookId.replace(/-/g, '_');
  const defaultFiles = [`${baseFileName}.pdf`, `${baseFileName}_part2.pdf`];

  // Additional files specific to each workbook
  const additionalFiles: Record<string, string[]> = {
    '1': ['machine_learning_basics.pdf', 'dataset_exploration.pdf', 'model_training.pdf'],
    '2': ['useState_examples.pdf', 'useEffect_lifecycle.pdf', 'custom_hooks.pdf'],
    '3': ['generic_types.pdf', 'utility_types.pdf', 'type_inference.pdf'],
    '4': ['chart_basics.pdf', 'interactive_visualizations.pdf', 'responsive_design.pdf'],
    '5': ['loading_strategies.pdf', 'code_splitting.pdf', 'image_optimization.pdf'],
    '6': ['grid_layouts.pdf', 'responsive_patterns.pdf', 'grid_animations.pdf'],
  };

  return [...defaultFiles, ...(additionalFiles[workbookId] || [])];
};

// Mock workbook files for dashboard view - using the file generation function
export const mockWorkbookDashboards: Record<string, WorkbookDashboardData> = {
  '1': {
    id: '1',
    title: 'Machine Learning Basics',
    lastEdited: new Date('2025-01-01'),
    files: getWorkbookFiles('1'),
    isPersonal: true,
    isPublic: true,
  },
  '2': {
    id: '2',
    title: 'React Hooks Deep Dive',
    lastEdited: new Date('2024-12-15'),
    files: getWorkbookFiles('2'),
    isPersonal: true,
    isPublic: true,
  },
  '3': {
    id: '3',
    title: 'TypeScript Advanced Types',
    lastEdited: new Date('2024-11-20'),
    files: getWorkbookFiles('3'),
    isPersonal: true,
    isPublic: true,
  },
  '4': {
    id: '4',
    title: 'Data Visualization with D3',
    lastEdited: new Date('2024-10-05'),
    files: getWorkbookFiles('4'),
    isPersonal: false,
    isPublic: true,
  },
  '5': {
    id: '5',
    title: 'Web Performance Optimization',
    lastEdited: new Date('2024-09-15'),
    files: getWorkbookFiles('5'),
    isPersonal: false,
    isPublic: true,
  },
  '6': {
    id: '6',
    title: 'CSS Grid Mastery',
    lastEdited: new Date('2024-08-22'),
    files: getWorkbookFiles('6'),
    isPersonal: false,
    isPublic: true,
  },
};

// Helper function to get workbook dashboard by ID
export const getWorkbookDashboardById = (id: string): WorkbookDashboardData => {
  return (
    mockWorkbookDashboards[id] || {
      id,
      title: `Workbook ${id}`,
      lastEdited: new Date(),
      files: getWorkbookFiles(id),
      isPersonal: false,
      isPublic: true,
    }
  );
};
