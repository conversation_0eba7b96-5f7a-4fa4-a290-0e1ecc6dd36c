document.addEventListener("DOMContentLoaded", function () {
  // Load languages and populate the language list
  setupSelectLanguage();

  const languageSelect = document.getElementById("target-language-select");
  languageSelect.addEventListener("change", fileLanguageChange);
});

function fileLanguageChange(event) {
  const translateButton = document.getElementById("translate-button-id");
  translateButton.disabled = false;
}

async function translateText() {
  const sourceText = document.getElementById("source-text").value;
  if (sourceText != "") {
    const sourceLanguage = document.getElementById(
      "source-language-select"
    ).value;
    const targetLanguage =
      document.getElementById("target-language-select").value || "en"; // Default to English

    // Check if source and target languages are the same
    if (sourceLanguage && sourceLanguage === targetLanguage) {
      document.getElementById("target-text").value = sourceText;
      return;
    }

    let detectedLanguage = sourceLanguage; // Use the selected source language

    // Detect language if source language is not selected
    if (!sourceLanguage) {
      const detectResponse = await fetch("/sidekick/detect/language", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ text: sourceText }),
      });

      if (detectResponse.ok) {
        const detectData = await detectResponse.json();
        detectedLanguage = detectData.language_code;

        // Update source language select
        const sourceSelect = document.getElementById("source-language-select");
        sourceSelect.value = detectedLanguage;
      } else {
        const errorData = await detectResponse.json();
        alert(errorData.error);
        return;
      }
    }

    // Translate text to target language
    const translateResponse = await fetch("/sidekick/translate/text", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        text: sourceText,
        target_language: targetLanguage,
        source_language: detectedLanguage,
      }),
    });

    if (translateResponse.ok) {
      const translateData = await translateResponse.json();
      document.getElementById("target-text").value = translateData[0];
    } else {
      const errorData = await translateResponse.json();
      alert(errorData.error);
    }
  }
}

window.translateText = translateText;

// Add exchange button functionality to swap source and target languages and text areas
document
  .querySelector(".exchange-button")
  .addEventListener("click", function () {
    const sourceSelect = document.getElementById("source-language-select");
    const targetSelect = document.getElementById("target-language-select");

    const tempLang = sourceSelect.value;
    sourceSelect.value = targetSelect.value;
    targetSelect.value = tempLang;

    const sourceTextarea = document.querySelector(".source-language textarea");
    const targetTextarea = document.querySelector(".target-language textarea");
    const tempText = sourceTextarea.value;
    sourceTextarea.value = targetTextarea.value;
    targetTextarea.value = tempText;
  });

function copySourceText() {
  const sourceText = document.getElementById("source-text");
  sourceText.select();
  sourceText.setSelectionRange(0, 99999); // For mobile devices
  document.execCommand("copy");
}

// Function to copy target text
function copyTargetText() {
  const targetText = document.getElementById("target-text");
  targetText.select();
  targetText.setSelectionRange(0, 99999);
  document.execCommand("copy");
}

window.copySourceText = copySourceText;
window.copyTargetText = copyTargetText;

async function setupSelectLanguage() {
  const languageResponse = await fetch("/static/language.json");
  const languages = await languageResponse.json();

  const sourceLanguageSelect = document.getElementById(
    "source-language-select"
  );
  const targetLanguageSelect = document.getElementById(
    "target-language-select"
  );

  languages.forEach((language) => {
    const option = document.createElement("option");
    option.value = language.language_code;
    option.textContent = language.language_name;
    sourceLanguageSelect.appendChild(option.cloneNode(true));
    targetLanguageSelect.appendChild(option);

    // Set English as default for target language
    if (language.language_code === "en") {
      targetLanguageSelect.value = "en";
    }
  });

  sourceLanguageSelect.value = "";
}
