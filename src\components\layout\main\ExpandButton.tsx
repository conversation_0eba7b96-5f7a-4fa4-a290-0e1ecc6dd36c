import React from 'react';
import { useThemeStyles } from '@/hooks/useThemeStyles';
import { ExpandButtonProps } from '@/types/mainLayout';

const ExpandButton: React.FC<ExpandButtonProps> = ({ onToggle }) => {
  const { classes } = useThemeStyles();

  return (
    <div className={`expand-button absolute top-4 left-4 z-10 cursor-pointer`}>
      <button
        className={`expand-button__button ${classes.circleIconButton} cursor-pointer`}
        onClick={onToggle}
        aria-label="Expand sidebar"
        tabIndex={0}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className={`expand-button__icon h-5 w-5`}
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M4 5a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zm0 5a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zm1 5a1 1 0 100 2h10a1 1 0 100-2H5z"
            clipRule="evenodd"
          />
        </svg>
      </button>
    </div>
  );
};

export default ExpandButton;
