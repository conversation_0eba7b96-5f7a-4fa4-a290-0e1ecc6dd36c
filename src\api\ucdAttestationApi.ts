import { client } from '@/api/client';
import { showToast } from '@/components/common/ToastNotification/ToastNotification';
import { API_BASE_PATH } from '@/api/apiConfig';
import { getGenericCookie, setGenericCookie } from '@/utils/cookieUtils';

const baseRoute = `${API_BASE_PATH}/restful`;

type UCDAttestationPayload = {
  files_mimes: {
    file_name: string;
    mime_type: string;
    file_size: number;
  }[];
  session_type: string;
  attestation_text: string;
};

export const ucdAttestation = async (isUCD: boolean, files: File[], sessionType: string) => {
  const priorAttestation = getGenericCookie('ucdAttestation');
  if (!isUCD) {
    return true;
  }
  if (files.length === 0) {
    return false;
  }
  const payload: UCDAttestationPayload = {
    files_mimes: [],
    session_type: sessionType,
    attestation_text:
      'We are prohibited from uploading any Active-Duty Dental Program (ADDP) and TRICARE Dental Program (TDP) data into Sidekick.\n\nBy clicking OK, I attest that my upload does not contain ADDP or TDP data and understand that uploading such data may result in disciplinary action, including termination of employment.',
  };
  if (!priorAttestation) {
    const attestation = confirm(payload.attestation_text);
    if (!attestation) {
      showToast.caution('File(s) not attested for!', '');
      return false;
    }
  }
  for (const file of files) {
    payload.files_mimes.push({
      file_name: file.name,
      mime_type: file.type,
      file_size: file.size,
    });
  }
  setGenericCookie('ucdAttestation', 'true', 30 * 24); // 30 days in hours
  await client.post<null>(`${baseRoute}/file_attestation`, payload);
  return true;
};
