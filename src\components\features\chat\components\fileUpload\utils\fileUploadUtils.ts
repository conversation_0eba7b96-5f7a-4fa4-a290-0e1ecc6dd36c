import { FileUploadValidationResult } from '@/types/fileUpload';

export const ALLOWED_FILE_TYPES: string[] = [
  'image/jpeg',
  'image/png',
  'video/mp4',
  'video/webm',
  'video/x-matroska',
  'video/quicktime',
  'application/pdf',
  'text/plain',
  'audio/mpeg',
  'audio/wav',
  'audio/webm',
  'audio/ogg',
  'audio/aac',
  'audio/x-flac',
];

const MAX_FILE_SIZE_BYTES = 1024 * 1024 * 1024; // 1 GiB (Gibibyte)
const MAX_PDF_FILE_SIZE_BYTES = 50 * 1024 * 1024; // 50 MiB (Mebibyte), adjusted from 51MB for clarity
const MAX_TXT_FILE_SIZE_BYTES = 8 * 1024 * 1024; // 8 MiB (Mebibyte)

const INVALID_UTF8_CHARS_REGEX = /[\uFFFD\u0000]/g;

export const validateFile = (file: File): FileUploadValidationResult => {
  if (!ALLOWED_FILE_TYPES.includes(file.type)) {
    return {
      isValid: false,
      error: `File type not allowed. Please upload images, videos, audio, PDF or text files only`,
    };
  }

  if (file.type === 'application/pdf' && file.size > MAX_PDF_FILE_SIZE_BYTES) {
    return {
      isValid: false,
      error: `PDF file is too large. Maximum size is ${MAX_PDF_FILE_SIZE_BYTES / (1024 * 1024)}MB. File size: ${(file.size / (1024 * 1024)).toFixed(2)}MB.`,
    };
  }

  if (file.type === 'text/plain' && file.size > MAX_TXT_FILE_SIZE_BYTES) {
    return {
      isValid: false,
      error: `Text file is too large. Maximum size is ${MAX_TXT_FILE_SIZE_BYTES / (1024 * 1024)}MB. File size: ${(file.size / (1024 * 1024)).toFixed(2)}MB.`,
    };
  }

  if (file.size > MAX_FILE_SIZE_BYTES) {
    return {
      isValid: false,
      error: `File is too large. Maximum size is ${MAX_FILE_SIZE_BYTES / (1024 * 1024 * 1024)}GB. File size: ${(file.size / (1024 * 1024 * 1024)).toFixed(2)}GB.`,
    };
  }

  return { isValid: true };
};

export const cleanFileContent = async (file: File): Promise<File | Blob> => {
  if (file.type === 'text/plain') {
    try {
      const originalText = await file.text();
      const cleanedText = originalText.replace(INVALID_UTF8_CHARS_REGEX, ' ');
      return new File([cleanedText], file.name, { type: 'text/plain' });
    } catch (error) {
      console.error(`Error processing text file ${file.name}:`, error);
      return file;
    }
  }
  return file;
};
