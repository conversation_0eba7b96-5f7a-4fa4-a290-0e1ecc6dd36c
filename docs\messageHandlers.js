/**
 * Starts the "thinking" animation and displays a message indicating that <PERSON><PERSON> is processing the user's input.
 *
 * @param {string} text - The text to display in the "thinking" message. Defaults to "Thinking".
 */
function startThinking(text = "Thinking") {
  let dateTime = new Date();
  let timestamp = dateTime.toLocaleTimeString();
  let thinking = formMessageText(
    "robot",
    "responses",
    text,
    "Sidekick",
    timestamp,
    "",
    "thinking"
  );
  writeMessageToScreen(thinking, "thinking_DIV");

  document.getElementById("submitPrompt").disabled = true;
}

/**
 * Stops the "thinking" animation and removes the "thinking" message from the screen.
 *
 * @param {number} intervalId - The ID of the interval that was used to animate the "thinking" indicator.
 */
function stopThinking(intervalId) {
  clearInterval(intervalId);
  const element = document.getElementById("thinking_DIV");
  element.parentNode.removeChild(element);
  document.getElementById("submitPrompt").disabled = false;
}

/**
 * Adds a "." to the "thinking" message, creating a simple animation effect.
 */
function thinkingIndicator() {
  const thinking = document.getElementById("thinking");
  if (thinking.innerHTML.split("Thinking")[1].length >= 3) {
    thinking.innerHTML = "Thinking";
  }
  thinking.innerHTML += ".";
}

/**
 * Handles errors that occur during communication with the server.
 *
 * @param {string} message - The error message to display.
 */
function handleError(message) {
  let dateTime = new Date();
  let timestamp = dateTime.toLocaleTimeString();
  let errorMessage = formMessageText(
    "robot",
    "responses",
    message,
    "Sidekick",
    timestamp,
    "",
    "error"
  );
  writeMessageToScreen(errorMessage, "", "fetch_error");
  document.getElementById("submitPrompt").disabled = true;
}

/**
 * Stops the "thinking" animation and displays a message indicating that an error has occurred.
 *
 * @param {number} intervalId - The ID of the interval that was used to animate the "thinking" indicator.
 */
function lostConnection(intervalId) {
  clearInterval(intervalId);
  document.getElementById("thinking").innerHTML =
    "Sidekick ran into an error. Please refresh or try again later.";
}

/**
 * Writes a message to the screen, creating a new div element for the message.
 *
 * @param {string} message - The message to display.
 * @param {string} id - The ID to assign to the new div element. Defaults to an empty string.
 * @param {string} cls - The class to assign to the new div element. Defaults to an empty string.
 */
function writeMessageToScreen(message, id = "", cls = "", shouldScroll = true) {
  let responseHTML = document.getElementById("response");

  let divWrapper = document.createElement("DIV");
  divWrapper.classList.add("messages_wrapper");
  let divAppend = document.createElement("DIV");
  divAppend.classList.add("messages_list");
  if (cls.length) {
    divAppend.classList.add(cls);
  }
  divWrapper.id = id;

  divAppend.innerHTML = message;
  divWrapper.appendChild(divAppend);
  // responseHTML.appendChild(divAppend);
  responseHTML.appendChild(divWrapper);
  if (shouldScroll) {
    divWrapper.scrollIntoView(true);
  }
}

/**
 * Creates a message element and appends it to the screen.
 *
 * @param {string} user - The type of user sending the message ("person" or "robot").
 * @param {string} cls - The class to assign to the message element.
 * @param {string} message - The message text.
 * @param {boolean} think - Whether to start the "thinking" animation after displaying the message. Defaults to true.
 */
function createMessage(user, cls, message, think = true, date = null) {
  let dateTime = date ? date : new Date();
  let timestamp = dateTime.toLocaleTimeString();
  let message_area = document.getElementById("response_border");

  if (user == "person") {
    messageText = formMessageText(user, cls, message, "You", timestamp);
  } else {
    messageText = formMessageText(
      user,
      cls,
      message,
      "Sidekick",
      timestamp,
      "",
      (id = undefined)
    );
  }

  writeMessageToScreen(messageText);

  if (user == "person" && think) {
    startThinking();
  }
}

/**
 * Formats the message text for display.
 *
 * @param {string} user - The type of user sending the message ("person" or "robot").
 * @param {string} cls - The class to assign to the message element.
 * @param {string} message - The message text.
 * @param {string} name - The name of the user sending the message.
 * @param {string} timestamp - The timestamp of the message.
 * @param {string} titleMessage - A message to show in the title after the timestamp.
 * @param {string} id - An optional ID to assign to the message element. Used in Thinking animation.
 * @returns {string} The formatted message text.
 */
function formMessageText(
  user,
  cls,
  message,
  name,
  timestamp,
  titleMessage = "",
  id = ""
) {
  let message_p;
  let copyButton = "";
  if (id !== undefined) {
    message_p = `<p id='${id}'>${message}</p>`;
  } else {
    message_p = `<p>${message}</p>`;
  }

  message_p = `<div class="line line-${user}">${message_p}</div>`;
  messageText = `<span class="${cls}"><span class="user"><span class="dot dot-${user}"></span><span class="promptname">${name}</span> (${timestamp})<span class=titleMessage>${titleMessage}</span></span>${message_p}</span>`;
  return messageText;
}

/**
 * Checks if the user's email domain is allowed for file upload.
 *
 * @returns {boolean} True if the domain is allowed, false otherwise.
 */
function checkIfAllowed(output_only = true) {
  const disallowedDomains = ["highmarkwholecare.com"];
  let test_email = email.toLowerCase();
  for (const domain of disallowedDomains) {
    let check_email = test_email.endsWith(`@${domain}`);
    if (check_email) {
      if (output_only) {
        const uploadButton = document.getElementById("uploadFile");
        const cancelButton = document.getElementById("cancelFile");
        const sendFileButton = document.getElementById("sendFileButton");
        const infoText = document.getElementById("infoText");
        uploadButton.style.display = "none";
        cancelButton.style.display = "none";
        sendFileButton.style.display = "none";
        infoText.innerHTML = "Your Chat Conversation with Sidekick:";
        document.title = "Sidekick Chat";
      }
      return false;
    }
  }
  return true;
}

function escapeHtml(unsafe) {
  return unsafe
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;");
}
