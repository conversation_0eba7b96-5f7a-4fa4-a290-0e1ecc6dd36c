export const colors = {
  primary: {
    main: '#0066B1',
    light: '#ECF7FF',
    dark: '#003963',
    hover: '#0075ca',
    active: '#005694',
    fileIcon: '#F7987D',
  },
  background: {
    light: '#ECF7FF',
    dark: '#00223C',
    input: {
      light: '#F5F5F5',
      dark: '#002D4F',
    },
    sidebar: {
      light: '#003963',
      dark: 'rgba(0,57,99,1)',
    },
  },
  text: {
    light: '#003963',
    dark: '#ECF7FF',
    muted: {
      light: '#6B7280',
      dark: '#9CA3AF',
    },
    sidebar: {
      main: '#ECF7FF',
      subItem: '#C5E6FF',
      icon: {
        default: '#6B7280',
        hover: 'rgba(255,255,255,0.5)',
      },
    },
  },
  border: {
    main: '#0066B1',
    sidebar: 'rgba(255,255,255,0.25)',
    light: '#E5E7EB',
    dark: '#374151',
  },
  state: {
    hover: {
      light: '#F5F5F5',
      dark: '#002D4F',
    },
    active: {
      light: '#E5E7EB',
      dark: '#001F36',
    },
    focus: {
      light: 'rgba(0,102,177,0.2)',
      dark: 'rgba(236,247,255,0.2)',
    },
  },
  white: '#FFFFFF',
};

export const spacing = {
  md: '16px',
};

export const typography = {
  fontFamily: {
    main: 'Roboto, system-ui, sans-serif',
  },
};

export const borderRadius = {
  md: '8px',
  full: '9999px',
};

export const shadows = {
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
};

export const animation = {
  transition: {
    normal: 'all 300ms ease',
  },
};

/**
 * Gets theme color values based on dark/light mode
 */
export const getThemeColors = (isDarkMode: boolean) => ({
  background: isDarkMode ? colors.background.dark : colors.background.light,
  backgroundInput: isDarkMode ? colors.background.input.dark : colors.background.input.light,
  backgroundSidebar: isDarkMode ? colors.background.sidebar.dark : colors.background.sidebar.light,

  text: isDarkMode ? colors.text.dark : colors.text.light,
  textMuted: isDarkMode ? colors.text.muted.dark : colors.text.muted.light,
  textSidebar: colors.text.sidebar.main,
  textSidebarSubItem: colors.text.sidebar.subItem,

  border: colors.border.main,
  borderSidebar: colors.border.sidebar,
  borderSubtle: isDarkMode ? colors.border.dark : colors.border.light,
  fileBorder: colors.primary.dark,
  fileIconColor: colors.primary.fileIcon,

  hoverBackground: isDarkMode ? colors.state.hover.dark : colors.state.hover.light,
  activeBackground: isDarkMode ? colors.state.active.dark : colors.state.active.light,
  focusRing: isDarkMode ? colors.state.focus.dark : colors.state.focus.light,
});

export const themeClasses = {
  // Background classes
  background: (isDarkMode: boolean) => (isDarkMode ? 'bg-[#00223C]' : 'bg-[#ECF7FF]'),

  backgroundInput: (isDarkMode: boolean) => (isDarkMode ? 'bg-[#002D4F]' : 'bg-[#F5F5F5]'),

  backgroundSidebar: (isDarkMode: boolean) => (isDarkMode ? 'bg-[rgba(0,57,99,1)]' : 'bg-[#003963]'),

  // Text classes
  text: (isDarkMode: boolean) => (isDarkMode ? 'text-[#ECF7FF]' : 'text-[#003963]'),

  textMuted: (isDarkMode: boolean) => (isDarkMode ? 'text-[#9CA3AF]' : 'text-[#6B7280]'),

  // Placeholder text
  placeholder: (isDarkMode: boolean) => (isDarkMode ? 'placeholder-[#ECF7FF]' : 'placeholder-[#003963]'),

  // Border classes
  border: (isDarkMode: boolean) => (isDarkMode ? 'border-[#374151]' : 'border-[#E5E7EB]'),
  borderPrimary: () => 'border border-[#0066B1]',
  borderSidebar: () => 'border-l-[rgba(255,255,255,0.25)]',

  // Hover states
  hoverBackground: (isDarkMode: boolean) => (isDarkMode ? 'hover:bg-[#002D4F]' : 'hover:bg-[#F5F5F5]'),

  // Focus states
  focusRing: (isDarkMode: boolean) =>
    isDarkMode
      ? 'focus:ring-2 focus:ring-[rgba(236,247,255,0.2)] focus:outline-none'
      : 'focus:ring-2 focus:ring-[rgba(0,102,177,0.2)] focus:outline-none',

  // Component classes
  button: (isDarkMode: boolean, variant: 'primary' | 'secondary' | 'ghost' = 'primary') => {
    const baseClasses = `px-4 py-2 rounded-md font-medium transition-all focus:outline-none ${
      isDarkMode ? 'focus:ring-2 focus:ring-[rgba(236,247,255,0.2)]' : 'focus:ring-2 focus:ring-[rgba(0,102,177,0.2)]'
    }`;

    if (variant === 'primary') {
      return `${baseClasses} bg-[#0066B1] text-white hover:bg-[#0075ca] active:bg-[#005694]`;
    }

    if (variant === 'secondary') {
      return `${baseClasses} border border-[#0066B1] ${
        isDarkMode ? 'text-[#ECF7FF]' : 'text-[#0066B1]'
      } hover:bg-opacity-10 hover:bg-[#0066B1]`;
    }

    return `${baseClasses} ${isDarkMode ? 'text-[#ECF7FF] hover:bg-[#002D4F]' : 'text-[#003963] hover:bg-[#F5F5F5]'}`;
  },

  // Form element classes
  input: (isDarkMode: boolean) =>
    `${isDarkMode ? 'bg-[#002D4F]' : 'bg-[#F5F5F5]'} ${
      isDarkMode ? 'text-[#ECF7FF]' : 'text-[#003963]'
    } ${isDarkMode ? 'placeholder-[#ECF7FF]' : 'placeholder-[#003963]'} border ${
      isDarkMode ? 'border-[#374151]' : 'border-[#E5E7EB]'
    } rounded-md px-3 py-2 ${
      isDarkMode
        ? 'focus:ring-2 focus:ring-[rgba(236,247,255,0.2)] focus:outline-none'
        : 'focus:ring-2 focus:ring-[rgba(0,102,177,0.2)] focus:outline-none'
    }`,

  // Card classes
  card: (isDarkMode: boolean) =>
    `${isDarkMode ? 'bg-[#002D4F]' : 'bg-[#F5F5F5]'} rounded-lg p-4 border ${
      isDarkMode ? 'border-[#374151]' : 'border-[#E5E7EB]'
    } shadow-sm`,

  // Navigation classes
  sidebar: (isDarkMode: boolean) =>
    `${isDarkMode ? 'bg-[rgba(0,57,99,1)]' : 'bg-[#003963]'} text-white h-full overflow-y-auto`,

  // Panel classes
  rightPanel: (isDarkMode: boolean) =>
    `${isDarkMode ? 'bg-[#00223C]' : 'bg-[#ECF7FF]'} border-l ${
      isDarkMode ? 'border-[#003963]' : 'border-[#0066B1]'
    } overflow-y-auto transition-colors duration-300`,

  // Common utility classes
  iconButton: (isDarkMode: boolean) =>
    `${isDarkMode ? 'text-[#ECF7FF]' : 'text-[#003963]'} hover:opacity-80 transition-all`,

  circleIconButton: (isDarkMode: boolean) =>
    `p-2 rounded-full border border-[#0066B1] ${
      isDarkMode ? 'text-[#ECF7FF]' : 'text-[#003963]'
    } hover:opacity-80 transition-all`,

  // Sidebar specific classes
  sidebarText: () => 'text-[#ECF7FF]',
  sidebarSubItemText: () => 'text-[#C5E6FF]',
  sidebarBorder: () => 'border-l-[rgba(255,255,255,0.25)]',
  sidebarIconHover: () =>
    'text-[#6B7280] hover:text-[rgba(255,255,255,0.5)] focus:text-[rgba(255,255,255,0.5)] transition-colors',
};
