import { getGenericCookie, setGenericCookie } from '@/utils/cookieUtils';

interface CallbackParams {
  callback_url: string;
  client_id: string;
  client_secret: string;
  code_verifier: string;
  token_url: string;
}

export const refreshAuthFlow = async () => {
  const callbackParamsLink = '/sidekick/callback_params';
  const fetchOptions = {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
  };

  const response = await fetch(callbackParamsLink, fetchOptions);
  const data = await response.json();
  await refreshAuth(data);
};

const refreshAuth = async (data: CallbackParams) => {
  //outputs a new auth token that needs to go back to the backend to verify & store it in session/cookie
  // Step 1, refresh token sets new token cookie //unverified
  // Step 2 send token -> backend for verification //being verified
  // // Step 2 response ->
  let client_id = data.client_id;
  let client_secret = data.client_secret;
  let token_url = data.token_url;
  let refresh_token = getGenericCookie('refresh_token');

  const cics = btoa(`${client_id}:${client_secret}`);

  const headers = {
    'X-OAUTH-IDENTITY-DOMAIN-NAME': 'HighmarkHealthGenAI',
    'Content-Type': 'application/x-www-form-urlencoded',
    Authorization: `Basic ${cics}`,
  };

  let new_data = { grant_type: 'REFRESH_TOKEN', refresh_token: refresh_token || '' };

  let urlencoded = new URLSearchParams(new_data);

  let requestOptions = {
    method: 'POST',
    headers: headers,
    body: urlencoded,
  };

  const response = await fetch(`${token_url}`, requestOptions);
  const result = await response.text();
  await send_token(result, true);
};

const send_token = async (token: string, refresh: boolean = false) => {
  // In here set token to be cookie AND send it to backend.

  const auth_token = JSON.parse(token);
  let timeout = 1;
  if (auth_token['expires_in'] == 3600) {
    timeout = 1; //1 hour
  } else {
    timeout = 0.25; //15 minutes
  }

  //This is getting set as undefined from refresh_auth.
  setGenericCookie('token', auth_token['access_token'], timeout); //Mostly for tokens generated via refresh token.

  let token_json = { auth: auth_token, refresh: refresh };

  // step 2 // Could be in backend with socketio forcing a redirect here.
  // verify token process, will redirect back to /login if bad token, redirects to onboard_user -> chat if good.
  const url = `/sidekick/token`;
  const fetchOptions = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(token_json),
  };
  const response = await fetch(url, fetchOptions);
  if (response.redirected) {
    window.location.href = response.url;
  }
};

export const checkAuth = () => {
  //If true auth tokens still exist, if false
  const refresh_token = getGenericCookie('refresh_token');
  const token = getGenericCookie('token');
  if (refresh_token !== null && token !== null) {
    //Refresh token and Auth token exists
    return 0;
  } else if (token !== null) {
    // Check for access token first
    //Only auth token exists, website will work as normal.
    return 0;
  } else if (refresh_token !== null) {
    //Refresh token exists, auth token doesn't, refresh auth process will happen.
    return 1; //Refresh auth
  } else {
    // No tokens exist
    //No token exists, redirect user to normal auth flow.
    return 2; //Force a page refresh
  }
  return false; //This case should never exist
};

const getCookieExpiryDate = (hours: number) => {
  const now = new Date();
  now.setTime(now.getTime() + hours * 60 * 60 * 1000);
  return now;
};
