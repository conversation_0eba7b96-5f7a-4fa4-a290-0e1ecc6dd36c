export type Nullable<T> = T | null;
export type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export interface SidekickBaseModel {
  createdUtc: Date;
  updatedUtc: Date;
}

export interface TaggableModel {
  tags?: string[];
}

export interface AuthorizableModel {
  authorizedEntities?: string[];
}

export interface SidekickDropDownProps {
  value: string;
  placeholder: string;
  select: { name: string; value: string }[];
  iconSVG?: React.ReactNode;
  disabled?: boolean;
  onChange: (value: string) => void;
}

export interface SidekickValueSliderProps {
  title: string;
  min: number;
  max: number;
  value: number;
  step: number;
  disabled?: boolean;
  onChange: (value: number) => void;
}
