export interface FileUploadValidationResult {
  isValid: boolean;
  error?: string;
}

export interface FileInfo {
  name: string;
  size: number;
  type: string;
  gcs_uri: string;
  isExpired?: boolean;
}

export type UploadStatus =
  | 'pending'
  | 'uploading'
  | 'success'
  | 'error'
  | 'cancelling'
  | 'cancelled'
  | 'deleting';

export interface ValidatedFile {
  name: string;
  size: number;
  type: string;
  lastModified: number;
  webkitRelativePath?: string;
  originalFile: File;
  id: string; // Unique ID for list management
  uploadProgress?: number;
  uploadError?: string;
  validationError?: string;
  uploadStatus?: UploadStatus;
  gcs_uri?: string;
  isCancellationRequested?: boolean;
  cancelTimestamp?: number;
}
