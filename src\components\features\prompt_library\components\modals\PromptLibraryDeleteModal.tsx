// import { useState } from "react";
import { Dialog } from '@base-ui-components/react/dialog';

import useThemeStyles from '@/hooks/useThemeStyles';

import './PromptLibraryDeleteModal.scss';

interface PromptLibraryDeleteModalProps {
  isOpen: boolean;
  onOpenChange: (value: boolean) => void;
  onDeletePromptTemplate: () => void;
}

const PromptLibraryDeleteModal: React.FC<PromptLibraryDeleteModalProps> = ({
  isOpen,
  onOpenChange,
  onDeletePromptTemplate,
}) => {

  const { classes } = useThemeStyles();

  return (
    <Dialog.Root open={isOpen} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Backdrop className={`promptLibraryDeleteModal__backdrop ${classes.text}`} />
        <Dialog.Popup className="promptLibraryDeleteModal__popup">
          <div className="promptLibraryDeleteModal__popup__info-container">
            <div className="promptLibraryDeleteModal__popup__icon-container">
              <div className="promptLibraryDeleteModal__popup__icon-background-container">
                <span className="promptLibraryDeleteModal__popup__info-icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="64"
                    height="64"
                    viewBox="0 0 64 64"
                    fill="none"
                  >
                    <path
                      d="M37.6533 27.92L31.9999 33.5733L26.3199 27.92L22.5599 31.68L28.2399 37.3333L22.5866 42.9867L26.3466 46.7467L31.9999 41.0933L37.6533 46.7467L41.4133 42.9867L35.7599 37.3333L41.4133 31.68L37.6533 27.92ZM41.3333 10.6667L38.6666 8H25.3333L22.6666 10.6667H13.3333V16H50.6666V10.6667H41.3333ZM15.9999 50.6667C15.9999 53.6 18.3999 56 21.3333 56H42.6666C45.5999 56 47.9999 53.6 47.9999 50.6667V18.6667H15.9999V50.6667ZM21.3333 24H42.6666V50.6667H21.3333V24Z"
                      fill="#FFE6EC"
                    />
                  </svg>
                </span>
              </div>
            </div>
            <div className="promptLibraryDeleteModal__popup__content-container">
              <div className="promptLibraryDeleteModal__popup__content-message-container">
                <p className="promptLibraryDeleteModal__popup__content-message-title">
                  Delete this prompt permanently?
                </p>
                <p className="promptLibraryDeleteModal__popup__content-message-description">
                  This prompt cannot be recovered once deleted.
                </p>
              </div>
            </div>
          </div>
          <div className="promptLibraryDeleteModal__popup__action-container">
            <div className="promptLibraryDeleteModal__popup__action-items-container">
              <button
                className="promptLibraryDeleteModal__popup__action-cancel-button"
                onClick={() => onOpenChange(false)}
              >
                <div className="promptLibraryDeleteModal__popup__action-cancel-button__contents-container">
                  <span className="promptLibraryDeleteModal__popup__action-cancel-button__contents-container__cancel-icon">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                    >
                      <path
                        d="M12 2C6.47 2 2 6.47 2 12C2 17.53 6.47 22 12 22C17.53 22 22 17.53 22 12C22 6.47 17.53 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20ZM15.59 7L12 10.59L8.41 7L7 8.41L10.59 12L7 15.59L8.41 17L12 13.41L15.59 17L17 15.59L13.41 12L17 8.41L15.59 7Z"
                        fill="white"
                      />
                    </svg>
                  </span>
                  <p className="promptLibraryDeleteModal__popup__action-cancel-button__contents-container__cancel-text">
                    No, cancel
                  </p>
                </div>
              </button>
              <button
                className="promptLibraryDeleteModal__popup__action-proceed-button"
                onClick={() => onDeletePromptTemplate()}
              >
                <div className="promptLibraryDeleteModal__popup__action-proceed-button__contents-container">
                  <span className="promptLibraryDeleteModal__popup__action-proceed-button__contents-container__proceed-icon">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                    >
                      <path
                        d="M14.12 10.47L12 12.59L9.87 10.47L8.46 11.88L10.59 14L8.47 16.12L9.88 17.53L12 15.41L14.12 17.53L15.53 16.12L13.41 14L15.53 11.88L14.12 10.47ZM15.5 4L14.5 3H9.5L8.5 4H5V6H19V4H15.5ZM6 19C6 20.1 6.9 21 8 21H16C17.1 21 18 20.1 18 19V7H6V19ZM8 9H16V19H8V9Z"
                        fill="#F7987D"
                      />
                    </svg>
                  </span>
                  <p className="promptLibraryDeleteModal__popup__action-proceed-button__contents-container__proceed-text">
                    Yes, delete this prompt
                  </p>
                </div>
              </button>
            </div>
          </div>
        </Dialog.Popup>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export default PromptLibraryDeleteModal;
