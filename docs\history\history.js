function historyMessage(user, cls, message, time, sidekick, titleMessage = "") {
  let messageText;
  if (titleMessage === "") {
    messageText = formMessageText(
      user,
      cls,
      message,
      sidekick,
      time,
      (id = "")
    );
  } else {
    messageText = formMessageText(
      user,
      cls,
      message,
      sidekick,
      time,
      titleMessage,
      (id = "")
    );
  }
  writeMessageToScreen(messageText, "", "", false);
}

fetch(url_history_id)
  .then((response) => {
    if (response.ok) {
      return response.json();
    }
  })
  .then((data) => {
    let fileHistory = data["file_history"]
    let conversationHistory = data["conversation_history"]

    const historyEvents = []
    for (const prompt of conversationHistory) {
      const {
        prompt: promptText,
        temperature,
        prompt_time_utc: promptTimeUTC,
        response: responseText,
        response_time_utc: responseTimeUTC
      } = prompt
      
      const promptTimeDisplay = convertISODate(promptTimeUTC)
      const promptTextDisplay = promptText
        .replace(/</g, "<")
        .replace(/>/g, ">")
        .replace(/\n/g, "<br>")
      
      historyEvents.push({
        type: "prompt",
        eventTime: new Date(promptTimeUTC),
        data: {
          promptText: promptTextDisplay,
          promptTime: promptTimeDisplay
        }
      })

      if (responseText) {
        const responseTimeDisplay = convertISODate(responseTimeUTC)
        const responseTextDisplay = converter.makeHtml(responseText)
        const titleMessage = ((temperature = 0.2) => {
          switch (temperature) {
            case 0.2:
              return "Precise"
            case 1:
              return "Balanced"
            case 1.8:
              return "Creative"
            default:
              return `${temperature}`
          }
        })(temperature)

        historyEvents.push({
          type: "response",
          eventTime: new Date(responseTimeUTC),
          data: {
            responseText: responseTextDisplay,
            responseTime: responseTimeDisplay,
            titleMessage: `Temperature used in generation: ${titleMessage}`
          }
        })
      }
    }

    const fileUploadTimeMap = new Map() // map of Date to array of files uploaded 
    for (const uploadedFile of fileHistory) {
      const uploadTime = uploadedFile["upload_time_utc"]
      const files = fileUploadTimeMap.get(uploadTime)
      if (files) {
        files.push(uploadedFile)
      } else {
        fileUploadTimeMap.set(uploadTime, [uploadedFile])
      }
    }

    for (const [uploadTimeString, files] of fileUploadTimeMap) {
      historyEvents.push({
        type: "fileUpload",
        eventTime: new Date(uploadTimeString),
        data: {
          files: files,
          uploadTime: convertISODate(uploadTimeString)
        }
      })
    }


    historyEvents.sort((eventA, eventB) => eventA["eventTime"] - eventB["eventTime"])
    for (const historyEvent of historyEvents) {
      const eventData = historyEvent["data"]
      switch (historyEvent["type"]) {
        case "prompt":
          historyMessage("person", "prompts", eventData["promptText"], eventData["promptTime"], "You")
          break
        case "response":
          historyMessage(
            "robot",
            "responses",
            eventData["responseText"],
            eventData["responseTime"],
            "Sidekick",
            eventData["titleMessage"]
          )
          let copyButton = generateCopyButton();
          copyIter += 1; // Increment copyIter constant
          let feedback_list = document.getElementsByClassName("messages_wrapper");
          let feedback_div = document.createElement("div");
          feedback_div.className = "feedbackDIV feedback";
          feedback_div.appendChild(copyButton);

          feedback_list[feedback_list.length - 1].appendChild(feedback_div);
          break
        case "fileUpload":
          let uploadedFiles = eventData["files"]
          let filesAsHTMLList = uploadedFiles.map(file => {
            let fileName = file["gcs_path"].substring(file["gcs_path"].lastIndexOf("/") + 1)
            return `<li>${fileName}</li>`
          }).join("")
          let fileUploadText = `File(s) uploaded:<br>${filesAsHTMLList}`
          historyMessage(
            "person",
            "prompts",
            fileUploadText,
            eventData["uploadTime"],
            "You"
          )
          break
      }
    }
  });
