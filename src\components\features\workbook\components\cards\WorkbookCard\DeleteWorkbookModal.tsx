import React, { useEffect } from 'react';
import { createPortal } from 'react-dom';
import { MdOutlineDeleteForever, MdCancel } from 'react-icons/md';
import { AiOutlineLoading3Quarters } from 'react-icons/ai';

import './DeleteWorkbookModal.scss';

interface DeleteWorkbookModalProps {
  isOpen: boolean;
  onOpenChange: (value: boolean) => void;
  onDeleteWorkbook: () => void;
  workbookName?: string;
  isDeleting?: boolean;
}

const DeleteWorkbookModal: React.FC<DeleteWorkbookModalProps> = ({
  isOpen,
  onOpenChange,
  onDeleteWorkbook,
  isDeleting = false,
}) => {
  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  const modalContent = (
    <div className="deleteWorkbookModal__backdrop" onClick={() => onOpenChange(false)}>
      <div className="deleteWorkbookModal__popup" onClick={e => e.stopPropagation()}>
        {/* Header with icon and message */}
        <div className="deleteWorkbookModal__header">
          <div className="deleteWorkbookModal__icon">
            <MdOutlineDeleteForever size={64} color="#fff" />
          </div>
          <div className="deleteWorkbookModal__content">
            <h3 className="deleteWorkbookModal__title">Delete this workbook permanently?</h3>
            <p className="deleteWorkbookModal__description">This workbook cannot be recovered once deleted.</p>
          </div>
        </div>

        {/* Action buttons */}
        <div className="deleteWorkbookModal__actions">
          <button className="deleteWorkbookModal__cancel-btn" onClick={() => onOpenChange(false)}>
            <MdCancel size={24} />
            <span>No, cancel</span>
          </button>

          <button
            className={`deleteWorkbookModal__delete-btn ${isDeleting ? 'deleteWorkbookModal__delete-btn--deleting' : ''}`}
            onClick={() => onDeleteWorkbook()}
            disabled={isDeleting}
          >
            {isDeleting ? (
              <>
                <AiOutlineLoading3Quarters size={24} className="deleteWorkbookModal__spinner" />
                <span>Deleting...</span>
              </>
            ) : (
              <>
                <MdOutlineDeleteForever size={24} />
                <span>Yes, delete this workbook</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );

  return createPortal(modalContent, document.body);
};

export default DeleteWorkbookModal;
