import React, { useEffect } from 'react';
import { createPortal } from 'react-dom';
import { MdOutlineDeleteForever, MdCancel } from 'react-icons/md';
import { AiOutlineLoading3Quarters } from 'react-icons/ai';

import './DeleteWorkbookModal.scss';

interface DeleteWorkbookModalProps {
  isOpen: boolean;
  onOpenChange: (value: boolean) => void;
  onDeleteWorkbook: () => void;
  workbookName?: string;
  isDeleting?: boolean;
}

const DeleteWorkbookModal: React.FC<DeleteWorkbookModalProps> = ({
  isOpen,
  onOpenChange,
  onDeleteWorkbook,
  isDeleting = false,
}) => {
  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  const modalContent = (
    <div className="deleteWorkbookModal__backdrop" onClick={() => onOpenChange(false)}>
      <div className="deleteWorkbookModal__popup" onClick={e => e.stopPropagation()}>
        <div className="deleteWorkbookModal__popup__info-container">
          <div className="deleteWorkbookModal__popup__icon-container">
            <div className="deleteWorkbookModal__popup__icon-background-container">
              <span className="deleteWorkbookModal__popup__info-icon">
                <MdOutlineDeleteForever size={64} color="#fff" />
              </span>
            </div>
          </div>
          <div className="deleteWorkbookModal__popup__content-container">
            <div className="deleteWorkbookModal__popup__content-message-container">
              <p className="deleteWorkbookModal__popup__content-message-title">Delete this workbook permanently?</p>
              <p className="deleteWorkbookModal__popup__content-message-description">
                This workbook cannot be recovered once deleted.
              </p>
            </div>
          </div>
        </div>
        <div className="deleteWorkbookModal__popup__action-container">
          <div className="deleteWorkbookModal__popup__action-items-container">
            <button className="deleteWorkbookModal__popup__action-cancel-button" onClick={() => onOpenChange(false)}>
              <div className="deleteWorkbookModal__popup__action-cancel-button__contents-container">
                <span className="deleteWorkbookModal__popup__action-cancel-button__contents-container__cancel-icon">
                  <MdCancel size={24} color="white" />
                </span>
                <p className="deleteWorkbookModal__popup__action-cancel-button__contents-container__cancel-text">
                  No, cancel
                </p>
              </div>
            </button>
            <button
              className={`deleteWorkbookModal__popup__action-proceed-button ${isDeleting ? 'deleteWorkbookModal__popup__action-proceed-button--deleting' : ''}`}
              onClick={() => onDeleteWorkbook()}
              disabled={isDeleting}
            >
              <div className="deleteWorkbookModal__popup__action-proceed-button__contents-container">
                <span className="deleteWorkbookModal__popup__action-proceed-button__contents-container__proceed-icon">
                  {isDeleting ? (
                    <AiOutlineLoading3Quarters
                      size={24}
                      color="#F7987D"
                      className="deleteWorkbookModal__popup__loading-spinner"
                    />
                  ) : (
                    <MdOutlineDeleteForever size={24} color="#F7987D" />
                  )}
                </span>
                <p className="deleteWorkbookModal__popup__action-proceed-button__contents-container__proceed-text">
                  {isDeleting ? 'Deleting...' : 'Yes, delete this workbook'}
                </p>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  return createPortal(modalContent, document.body);
};

export default DeleteWorkbookModal;
