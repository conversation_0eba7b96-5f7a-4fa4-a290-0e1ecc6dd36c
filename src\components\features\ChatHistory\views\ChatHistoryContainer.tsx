import React, { useEffect, useState } from 'react';
import ChatHistoryItem from '../components/ChatHistoryItem';
import NoChatHistory from '../components/NoChatHistory';
import { ChatHistorySummary } from '../chatHistoryTypes';
import { useThemeStyles } from '@/hooks/useThemeStyles';
import { fetchChatHistory } from '@/api/chatHistoryApi';

const ChatHistoryContainer: React.FC = () => {
  const { classes } = useThemeStyles();
  const [historyItems, setHistoryItems] = useState<ChatHistorySummary[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadHistory = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const data = await fetchChatHistory();
        setHistoryItems(data);
      } catch (err) {
        const message = err instanceof Error ? err.message : 'An unknown error occurred.';
        setError(message);
        setHistoryItems([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadHistory();
  }, []);

  // Conditional Rendering Logic
  const renderContent = () => {
    if (isLoading) {
      return <p className={`chat-history-container__loading-text ${classes.text}`}>Loading chat history...</p>;
    }
    if (error) {
      return (
        <div
          className={`chat-history-container__error-message text-center p-8 rounded-lg text-red-500 dark:text-red-400`}
        >
          <h2 className={`chat-history-container__error-heading text-xl font-semibold mb-2 ${classes.text}`}>
            Error Loading History
          </h2>
          <p className="chat-history-container__error-details">{error}</p>
        </div>
      );
    }
    if (historyItems.length === 0) {
      return <NoChatHistory />;
    }
    return historyItems.map(item => <ChatHistoryItem key={item.id} item={item} />);
  };

  return (
    <div
      className={`chat-history-container flex flex-col flex-grow pt-[24px] pb-[40px] gap-[40px] 
                    w-full h-full max-w-[760px] mx-auto ${classes.background} 
                    ${isLoading || error || historyItems.length === 0 ? 'items-center justify-center' : 'items-center'}`}
    >
      {renderContent()}
    </div>
  );
};

export default ChatHistoryContainer;
