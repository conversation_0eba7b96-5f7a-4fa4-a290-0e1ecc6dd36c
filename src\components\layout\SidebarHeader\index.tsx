import React from 'react';
import { BiArrowToLeft } from 'react-icons/bi';

import { SidekickLogoIcon } from '@/components/common/icons';

import { SidebarHeaderProps } from '@/types/layout';
import { useThemeStyles } from '@/hooks/useThemeStyles';

import './styles.scss';

const SidebarHeader: React.FC<SidebarHeaderProps> = ({ onToggle }) => {
  const { classes } = useThemeStyles();

  return (
    <div className="sidebar-header-main">
      <div className="sidebar-header-title">
        <div className="sidebar-header-icon">
          <SidekickLogoIcon />
        </div>
        <span className="sidebar-header-text">Sidekick</span>
      </div>
      <button className="sidebar-collapse-toggle" aria-label="Close sidebar" onClick={onToggle} tabIndex={0}>
        <BiArrowToLeft className={`h-5 w-5 ${classes.sidebarIconHover}`} />
      </button>
    </div>
  );
};

export default SidebarHeader;
