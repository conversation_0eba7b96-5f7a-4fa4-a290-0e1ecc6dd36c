import { PartialBy, SidekickBaseModel, TaggableModel, AuthorizableModel } from './common';

export enum ModelName {
  FLASH_DEPRECATED = 'gemini-1.5-flash-002',
  FLASH = 'gemini-2.0-flash-001',
  MEDLM = 'medlm-large-1.5',
}

export enum SessionType {
  CHAT = 1,
  CODE = 2,
  MDLM = 3,
  POLICY = 4,
}

export interface PromptTemplate extends SidekickBaseModel, TaggableModel, AuthorizableModel {
  id: string;
  name: string;
  author: string;
  createdBy?: string;
  prompt: string;
  sessionType: SessionType;
  model: ModelName;
  temperature: number;
  feedbackPositive?: number;
  feedbackNegative?: number;
  clonedFrom?: string;
  systemInstructions?: string;
}

export interface AddPromptTemplateTileProps {
  onClick: () => void;
  isGlobal: boolean;
}

export type CreatePromptTemplate = Omit<
  PartialBy<PromptTemplate, 'id'>,
  'author' | 'model' | 'createdBy' | 'createdUtc' | 'updatedUtc'
>;
export type CreatePromptTemplateResponse = {
  promptTemplate: PromptTemplate;
};

export type UpdatePromptTemplate = Omit<
  PartialBy<
    PromptTemplate,
    'name' | 'prompt' | 'sessionType' | 'temperature' | 'systemInstructions'
  >,
  'author' | 'createdBy' | 'model' | 'createdUtc' | 'updatedUtc'
> & {
  addedTags?: string[];
  removedTags?: string[];
  addedAuthorizedEntities?: string[];
  removedAuthorizedEntities?: string[];
};

export type DeletePromptTemplateResponse = {
  id: string;
  deleted: boolean;
};

