import React, { useState, useCallback, Suspense } from 'react';
import { IoAdd, IoRemove, IoRefresh, IoClose } from 'react-icons/io5';
import { useThemeStyles } from '@hooks/useThemeStyles';
import { useZoomPanPinchImport } from './hooks/useMermaidImport';

interface MermaidModalProps {
  isOpen: boolean;
  svgContent: string;
  onClose: () => void;
}

// Fallback zoom controls without external library
const FallbackZoomControls = ({
  onZoomIn,
  onZoomOut,
  onReset,
}: {
  onZoomIn: () => void;
  onZoomOut: () => void;
  onReset: () => void;
}) => (
  <div className="mermaid-zoom-controls">
    <button onClick={onZoomIn} className="mermaid-zoom-btn zoom-in" title="Zoom In">
      <IoAdd className="zoom-icon" />
    </button>
    <button onClick={onZoomOut} className="mermaid-zoom-btn zoom-out" title="Zoom Out">
      <IoRemove className="zoom-icon" />
    </button>
    <button onClick={onReset} className="mermaid-zoom-btn zoom-reset" title="Reset Zoom">
      <IoRefresh className="zoom-icon" />
    </button>
  </div>
);

// Simple zoom container without external library
const FallbackZoomContainer = ({ 
  children, 
  onZoomIn, 
  onZoomOut, 
  onReset 
}: { 
  children: React.ReactNode;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onReset: () => void;
}) => {
  const [scale, setScale] = useState(1.2);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  const handleZoomIn = useCallback(() => {
    setScale(prev => Math.min(prev * 1.2, 4));
    onZoomIn();
  }, [onZoomIn]);

  const handleZoomOut = useCallback(() => {
    setScale(prev => Math.max(prev / 1.2, 0.5));
    onZoomOut();
  }, [onZoomOut]);

  const handleReset = useCallback(() => {
    setScale(1.2);
    setPosition({ x: 0, y: 0 });
    onReset();
  }, [onReset]);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    setIsDragging(true);
    setDragStart({ x: e.clientX - position.x, y: e.clientY - position.y });
  }, [position]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (isDragging) {
      setPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y,
      });
    }
  }, [isDragging, dragStart]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  return (
    <div className="mermaid-transform-wrapper">
      <div
        className="mermaid-transform-content"
        style={{
          transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`,
          cursor: isDragging ? 'grabbing' : 'grab',
          transition: isDragging ? 'none' : 'transform 0.1s ease-out',
        }}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        {children}
      </div>
      <FallbackZoomControls
        onZoomIn={handleZoomIn}
        onZoomOut={handleZoomOut}
        onReset={handleReset}
      />
    </div>
  );
};

// Advanced zoom container with react-zoom-pan-pinch
const AdvancedZoomContainer = ({ 
  children, 
  zoomLibrary 
}: { 
  children: React.ReactNode;
  zoomLibrary: any;
}) => {
  const { TransformWrapper, TransformComponent } = zoomLibrary;

  const ZoomControls = ({
    zoomIn,
    zoomOut,
    resetTransform,
  }: {
    zoomIn: () => void;
    zoomOut: () => void;
    resetTransform: () => void;
  }) => (
    <div className="mermaid-zoom-controls">
      <button onClick={() => zoomIn()} className="mermaid-zoom-btn zoom-in" title="Zoom In">
        <IoAdd className="zoom-icon" />
      </button>
      <button onClick={() => zoomOut()} className="mermaid-zoom-btn zoom-out" title="Zoom Out">
        <IoRemove className="zoom-icon" />
      </button>
      <button onClick={() => resetTransform()} className="mermaid-zoom-btn zoom-reset" title="Reset Zoom">
        <IoRefresh className="zoom-icon" />
      </button>
    </div>
  );

  return (
    <TransformWrapper
      initialScale={1.2}
      minScale={0.5}
      maxScale={4}
      wheel={{ step: 0.1 }}
      doubleClick={{ disabled: false }}
      panning={{ disabled: false }}
      centerOnInit={true}
      limitToBounds={false}
      centerZoomedOut={true}
      alignmentAnimation={{ sizeX: 0, sizeY: 0 }}
    >
      {({ zoomIn, zoomOut, resetTransform }: any) => (
        <>
          <TransformComponent
            wrapperClass="mermaid-transform-wrapper"
            contentClass="mermaid-transform-content"
          >
            {children}
          </TransformComponent>
          <ZoomControls zoomIn={zoomIn} zoomOut={zoomOut} resetTransform={resetTransform} />
        </>
      )}
    </TransformWrapper>
  );
};

const MermaidModal: React.FC<MermaidModalProps> = ({ isOpen, svgContent, onClose }) => {
  const { classes } = useThemeStyles();
  const { loading: zoomLoading, data: zoomLibrary } = useZoomPanPinchImport();

  const handleOverlayClick = useCallback((e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  }, [onClose]);

  if (!isOpen) return null;

  return (
    <div className="mermaid-modal-overlay" onClick={handleOverlayClick}>
      <div className={`mermaid-modal-container ${classes.backgroundInput}`} onClick={e => e.stopPropagation()}>
        <button
          onClick={onClose}
          className={`mermaid-modal-close ${classes.text} ${classes.hoverBackground}`}
          aria-label="Close modal"
          tabIndex={0}
        >
          <IoClose className="close-icon" />
        </button>

        <div className="mermaid-modal-content">
          <h3 className="mermaid-modal-title">Mermaid Diagram</h3>

          <div className="mermaid-modal-svg">
            {zoomLoading ? (
              <div className="mermaid-loading-indicator">Loading zoom controls...</div>
            ) : null}
            
            <Suspense fallback={<div className="mermaid-loading-indicator">Loading zoom controls...</div>}>
              {zoomLibrary ? (
                <AdvancedZoomContainer zoomLibrary={zoomLibrary}>
                  <div dangerouslySetInnerHTML={{ __html: svgContent }} />
                </AdvancedZoomContainer>
              ) : (
                <FallbackZoomContainer
                  onZoomIn={() => {}}
                  onZoomOut={() => {}}
                  onReset={() => {}}
                >
                  <div dangerouslySetInnerHTML={{ __html: svgContent }} />
                </FallbackZoomContainer>
              )}
            </Suspense>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MermaidModal;
