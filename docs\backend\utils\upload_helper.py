import os
from datetime import datetime, timezone

from werkzeug.utils import secure_filename

from website.google_cloud_init import GCS
from website.logger_framework import make_logger

workflow = "util"

logger_info, logger_error = make_logger(workflow, __file__)


def handle_file_upload_to_gcs(
    local_file_path: str,
    file_name: str,
    email: str,
    delete: bool = False,
):
    try:
        gcs_util = GCS()
        # Generate a unique filename
        timestamp = datetime.now(timezone.utc).strftime("%Y-%m-%d %H-%M-%S")
        email_domain = email.split("@")[1]
        # filename = secure_filename(os.path.basename(local_file_path))
        filename = secure_filename(f"{timestamp}__{file_name}")
        gcs_path = f"{email_domain}/{email}/{filename}"

        # Upload the file to GCS
        gcs_uri = gcs_util.upload_file(local_file_path, gcs_path, email)
        logger_info.info(
            f"{workflow} : handle_file_upload_to_gcs : Successfully uploaded file for {email}"
        )
        return gcs_uri
    except Exception as e:
        logger_error.error(
            f"{workflow} : handle_file_upload_to_gcs : {str(e)}")
        raise
    finally:
        if delete:
            # Clean up the temporary file to free up space
            try:
                if os.path.exists(local_file_path):
                    os.remove(local_file_path)
                    logger_info.info(
                        f"{workflow} : handle_file_upload_to_gcs : Temp file {local_file_path} removed."
                    )
            except OSError as cleanup_error:
                logger_error.error(
                    f"{workflow} : handle_file_upload_to_gcs : Failed to remove temp file {local_file_path}: {str(cleanup_error)}"
                )
