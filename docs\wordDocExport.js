function createWordButton() {
  let wordButton = document.createElement("button");
  wordButton.type = "button";
  wordButton.className = "word-button";
  wordButton.id = "wordDocExport";
  wordButton.addEventListener("click", generateWordDoc);
  wordButton.innerHTML = "Export to Word";
  return wordButton;
}
async function generateWordDoc(event) {
  const clickedButton = event.target;
  const spinner = '<div class="spinner"></div>';
  const normText = "Export to Word";
  console.log(clickedButton);
  clickedButton.innerHTML = spinner;
  let value =
    clickedButton.parentElement.parentElement.childNodes[0].childNodes[0]
      .childNodes[1].textContent;
  let option = clickedButton.value;
  clickedButton.selectedIndex = 0;
  // clickedButton.style.display = "none";
  clickedButton.disabled = true;
  clickedButton.parentElement.disabled = true;
  // spinner.style.display = "block";
  console.log(value);
  await fetchWordDocument(value);
  clickedButton.innerHTML = normText;
  // spinner.style.display = "none";
  // clickedButton.style.display = "block";
  clickedButton.parentElement.disabled = false;
  clickedButton.disabled = false;
}

async function fetchWordDocument(fetchInput) {
  try {
    const response = await fetch("/sidekick/restful/word_export", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ input: fetchInput }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.success) {
      // Open the download URL in a new tab
      window.open(data.download_url, "_blank", "noopener noreferrer");
    } else {
      console.error("Error fetching Word Doc:", data.error || data.message);
    }
  } catch (error) {
    console.error("Error sending data:", error);
  }
}
