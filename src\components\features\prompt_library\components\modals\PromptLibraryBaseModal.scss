.promptLibraryTemplateCreateEditModalContainer {
    width: 800px;
    height: 526px;
    display: flex;
    flex-direction: column;
    justify-content: start;
    align-items: center;
    border-radius: 32px;
    background: #003963;
    box-sizing: border-box;
    .promptLibraryTemplateCreateEditModalContainer__body-container {
        width: 800px;
        height: 470px;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: var(--spacing-spacing-l-1, 32px) var(--spacing-spacing-l-3, 48px) var(--spacing-spacing-m-2, 16px) var(--spacing-spacing-l-3, 48px);
        gap: var(--spacing-spacing-l-1, 32px);
        .promptLibraryTemplateCreateEditModalContainer__body-container__title-container {
            width: 704px;
            height: 62px;
            display: flex;
            flex-direction: row;
            gap: 10px;
            align-items: center;
            color: #FFFFFF;
            position: relative;
            .promptLibraryTemplateCreateEditModalContainer__body-container__title-container__title-text {
                font-family: var(--font-sofia-pro);
                font-size: 48px;
                font-style: normal;
                font-weight: 500;
                line-height: 62.4px;
                letter-spacing: 0.384px;
                width: 444px;
                height: 62px;
                overflow: hidden;
            }
            .promptLibraryTemplateCreateEditModalContainer__body-container__title-container__action-close-button {
                position: absolute;
                cursor: pointer;
                width: 36px;
                height: 36px;
                aspect-ratio: 1/1;
                top: -7px;
                right: -24px;
                .promptLibraryTemplateCreateEditModalContainer__body-contgainer__title-container__action-close-button__icon {
                    height: 36px;
                    width: 36px;
                }
            }    
        }
    }
    .promptLibraryTemplateCreateEditModalContainer__info-container {
        width: 800px;
        height: 56px;
        border-radius: 0px 0px 32px 32px;
        display: flex;
        align-items: center;
        align-self: stretch;
        background: #003963;
        padding: var(--spacing-spacing-m-2, 16px) var(--spacing-spacing-l-3, 48px);
        border-top: 1px solid #0066B1;
        .promptLibraryTemplateCreateEditModalContainer__info-container__content-container {
            width: 704px;
            height: 24px;
            display: flex;
            align-items: center;
            gap: var(--spacing-spacing-sm-3, 8px);
            flex: 1 0 0;
            color: #FFFFFF;
            .promptLibraryTemplateCreateEditModalContainer__info-container__content-container__icon {
                width: 24px;
                height: 24px;
            }
            .promptLibraryTemplateCreateEditModalContainer__info-container__content-container__text {
                text-align: center;
                font-family: var(--font-roboto);
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 20.8px;
                letter-spacing: 0.128px;
            }
        }
    }
}
