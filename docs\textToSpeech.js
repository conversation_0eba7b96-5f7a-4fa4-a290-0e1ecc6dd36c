document.addEventListener("DOMContentLoaded", function() {
  const synthesizeButton = document.getElementById('synthesize-btn');
  const downloadButton = document.getElementById('download-btn');
  const resetButton = document.getElementById('reset-btn');
  const speedSlider = document.getElementById('speed');
  const pitchSlider = document.getElementById('pitch');
  const speedValue = document.getElementById('speed-value');
  const pitchValue = document.getElementById('pitch-value');
  // const volumeSlider = document.getElementById('volume');

  if (!checkIfAllowed(false)) {
    downloadButton.style.display = "none";
  }

  [speedSlider, pitchSlider].forEach((slider) => {
    slider.addEventListener("input", function () {
      // showTooltip(this);
      updateSliderBackground(this);
      updateValueDisplay(this);
    });
    // slider.addEventListener("mouseleave", hideTooltip);
    updateSliderBackground(slider);
    updateValueDisplay(slider)
  });

  loadLanguagesData();

  synthesizeButton.addEventListener('click', () => {
    synthesizeSpeech();
  });

  downloadButton.addEventListener('click', () => {
    downloadSpeech();
  });

  resetButton.addEventListener('click', ()=>{
    resetSpeedAndPitch();
  })
});


const noPitchVoices = [
  "de-DE-Journey-D --Male",
  "de-DE-Journey-F --Female",
  "en-GB-Journey-D --Male",
  "en-GB-Journey-F --Female",
  "en-IN-Journey-D --Male",
  "en-IN-Journey-F --Female",
  "en-US-Journey-D --Male",
  "en-US-Journey-F --Female",
  "en-US-Journey-O --Female",
  "es-US-Journey-D --Male",
  "es-US-Journey-F --Female",
  "fr-CA-Journey-D --Male",
  "fr-CA-Journey-F --Female",
  "fr-FR-Journey-D --Male",
  "fr-FR-Journey-F --Female",
  "it-IT-Journey-D --Male",
  "it-IT-Journey-F --Female"
];


let pitchAlertShown = false;

function updatePitchControlAvailability() {
  const voiceSelect = document.getElementById("voice");
  const pitchSlider = document.getElementById("pitch");
  if (noPitchVoices.includes(voiceSelect.value)) {
    pitchSlider.classList.add("disabled-slider");
  } else {
    pitchSlider.classList.remove("disabled-slider");
  }
}

document.getElementById("voice").addEventListener("change", function(){
  resetSpeedAndPitch();
  pitchAlertShown = false; 
  updatePitchControlAvailability();
});


document.getElementById("pitch").addEventListener("input", function(e) {
  const voiceSelect = document.getElementById("voice");
  if (noPitchVoices.includes(voiceSelect.value)) {
    if (!pitchAlertShown) {
      alert("This voice temporarily does not support pitch adjustment.");
      pitchAlertShown = true;
    }
    this.value = 0;
    updateValueDisplay(this);
    updateSliderBackground(this);
  }
});

function resetSpeedAndPitch(){
  const speedSlider = document.getElementById('speed');
  const pitchSlider = document.getElementById('pitch');

  speedSlider.value = 1;
  pitchSlider.value = 0;

  updateValueDisplay(speedSlider);
  updateValueDisplay(pitchSlider);
  updateSliderBackground(speedSlider);
  updateSliderBackground(pitchSlider);
}

function updateValueDisplay(slider){
  const value = parseFloat(slider.value).toFixed(2);
  if(slider.id =='speed'){
    document.getElementById('speed-value').textContent = value;
  }else if (slider.id === 'pitch'){
    document.getElementById('pitch-value').textContent = value;
  }
}
function getTTSParams() {
  const textContent = document.getElementById("ttsTextContent").value.trim();
  const languageCode = document.getElementById("language").value;
  const voiceNameRaw = document.getElementById("voice").value;
  const voiceName = voiceNameRaw.split(" --")[0];
  const audioEncoding = document.getElementById("audio-encoding").value;
  // const sampleRateHertz = parseInt(document.getElementById("sample-rate").value) || 24000;
  const speakingRate = parseFloat(document.getElementById("speed").value) || 1.0;
  const pitch = parseFloat(document.getElementById("pitch").value) || 0.0;
  // const volumeGainDb = parseFloat(document.getElementById("volume").value) || 0.0;

  return { textContent, languageCode, voiceName, audioEncoding, speakingRate, pitch};
}

function validateText(textContent, maxBytes = 5000) {
  if (!textContent) {
    alert("Please enter some text before generating or downloading speech.");
    return false;
  }
  const encoder = new TextEncoder();
  const textBytes = encoder.encode(textContent);
  if (textBytes.length > maxBytes) {
    alert("The text exceeds 5000 bytes limitation and cannot be converted to speech.");
    return false;
  }
  return true;
}

async function synthesizeSpeech() {
  const synthesizeButton = document.getElementById('synthesize-btn');
  const { textContent, languageCode, voiceName, audioEncoding, sampleRateHertz, speakingRate, pitch, volumeGainDb } = getTTSParams();

  // text validation
  if (!validateText(textContent)) return;

  const currentParams = {
    text: textContent,
    language_code: languageCode,
    voice_name: voiceName,
    audio_encoding: audioEncoding,
    // sample_rate_hertz: sampleRateHertz,
    speaking_rate: speakingRate,
    pitch: pitch
    // volume_gain_db: volumeGainDb
  };

  function areParamsEqual(params1, params2) {
    return JSON.stringify(params1) === JSON.stringify(params2);
  }

  if (synthesizeButton.audio && areParamsEqual(synthesizeButton.lastParams, currentParams)) {
    if (!synthesizeButton.audio.paused) {
      synthesizeButton.audio.pause();
      synthesizeButton.audio.currentTime = 0;
      synthesizeButton.classList.remove("playing");
      updateSynthesizeIcon(synthesizeButton, "static");
    } else {
      synthesizeButton.audio.play();
      synthesizeButton.classList.add("playing");
      updateSynthesizeIcon(synthesizeButton, "playing");
    }
    return;
  } else {
    if (synthesizeButton.audio) {
      synthesizeButton.audio.pause();
      synthesizeButton.audio.currentTime = 0;
      synthesizeButton.classList.remove("playing");
      updateSynthesizeIcon(synthesizeButton, "static");
    }
  }

  try {
    synthesizeButton.classList.add("loading");
    updateSynthesizeIcon(synthesizeButton, "loading");
    synthesizeButton.style.pointerEvents = "none";

    const controller = new AbortController();
    const signal = controller.signal;

    // set 300 second time out
    const timeoutId = setTimeout(() => {
      controller.abort();
    }, 300000); 

    const response = await fetch('/sidekick/restful/synthesizeSpeech-speech', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(currentParams),
      signal: signal
    });

    // clear timeout 
    clearTimeout(timeoutId);

    synthesizeButton.classList.remove("loading");
    synthesizeButton.style.pointerEvents = "auto";

    if (response.ok) {
      const audioBlob = await response.blob();
      const audioUrl = URL.createObjectURL(audioBlob);

      const audio = new Audio(audioUrl);
      synthesizeButton.audio = audio;
      synthesizeButton.lastParams = currentParams;

      audio.onplay = function () {
        synthesizeButton.classList.add("playing");
        updateSynthesizeIcon(synthesizeButton, "playing");
      };
      audio.onpause = function () {
        synthesizeButton.classList.remove("playing");
        updateSynthesizeIcon(synthesizeButton, "static");
      };
      audio.onended = function () {
        synthesizeButton.classList.remove("playing");
        audio.currentTime = 0;
        updateSynthesizeIcon(synthesizeButton, "static");
      };

      audio.play();
    } else {
      // check if backend return timeout error
      if (response.status === 504) {
        alert("The speech synthesis request timed out after 300 seconds. Please try again or adjust your parameters.");
      } else {
        console.error("Unable to generate speech.");
      }
      updateSynthesizeIcon(synthesizeButton, "static");
    }
  } catch (error) {
    console.error("Error during speech synthesis:", error);
    if (error.name === 'AbortError') {
      // time out happened
      alert("The speech synthesis request took too long (over 300 seconds). Please try again later or adjust your input.");
    }
    synthesizeButton.classList.remove("loading");
    synthesizeButton.style.pointerEvents = "auto";
    updateSynthesizeIcon(synthesizeButton, "static");
  }
}

async function downloadSpeech() {
  const downloadButton = document.getElementById('download-btn');
  const { textContent, languageCode, voiceName, audioEncoding, sampleRateHertz, speakingRate, pitch, volumeGainDb } = getTTSParams();

  if (!validateText(textContent)) return;

  try {
    downloadButton.classList.add("loading");
    updateDownloadIcon(downloadButton, "loading");
    downloadButton.style.pointerEvents = "none";

    const response = await fetch('/sidekick/restful/download-speech', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        text: textContent,
        language_code: languageCode,
        voice_name: voiceName,
        audio_encoding: audioEncoding,
        // sample_rate_hertz: sampleRateHertz,
        speaking_rate: speakingRate,
        pitch: pitch
        // volume_gain_db: volumeGainDb
      })
    });

    downloadButton.classList.remove("loading");
    downloadButton.style.pointerEvents = "auto";
    updateDownloadIcon(downloadButton, "static");

    if (response.ok) {
      const data = await response.json();
      if (data.success && data.download_url) {
        const downloadUrl = data.download_url;
        const a = document.createElement('a');
        a.href = downloadUrl;
        a.download = '';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      } else {
        console.error("Failed to get download URL.");
        alert("Failed to get download URL.");
      }
    } else {
      console.error("Failed to generate speech from input text.");
      alert("Failed to generate speech from input text.");
    }
  } catch (error) {
    console.error("Failed to download speech:", error);
    downloadButton.classList.remove("loading");
    downloadButton.style.pointerEvents = "auto";
    updateDownloadIcon(downloadButton, "static");
  }
}

function updateSynthesizeIcon(element, state) {
  const staticSVG = `
    <svg version="1.1" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 32 32">
      <path d="M10.24 11.616l-4.224 4.192 4.224 4.192c1.088-1.056 1.76-2.56 1.76-4.192s-0.672-3.136-1.76-4.192z"></path>
      <path d="M15.199 6.721l-1.791 1.76c1.856 1.888 3.008 4.48 3.008 7.328s-1.152 5.44-3.008 7.328l1.791 1.76c2.336-2.304 3.809-5.536 3.809-9.088s-1.473-6.784-3.809-9.088z"></path>
      <path d="M20.129 1.793l-1.762 1.76c3.104 3.168 5.025 7.488 5.025 12.256s-1.921 9.088-5.025 12.256l1.762 1.76c3.648-3.616 5.887-8.544 5.887-14.016s-2.239-10.432-5.887-14.016z"></path>
    </svg>`;

  const animatedSVG = `
    <svg version="1.1" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 32 32">
      <path d="M10.24 11.616l-4.224 4.192 4.224 4.192c1.088-1.056 1.76-2.56 1.76-4.192s-0.672-3.136-1.76-4.192z"></path>
      <path d="M15.199 6.721l-1.791 1.76c1.856 1.888 3.008 4.48 3.008 7.328s-1.152 5.44-3.008 7.328l1.791 1.76c2.336-2.304 3.809-5.536 3.809-9.088s-1.473-6.784-3.809-9.088z">
        <animate attributeName="opacity" values="0;0;1;1" begin="0" dur="1" keyTimes="0;.333;.334;1" repeatCount="indefinite"/>
      </path>
      <path d="M20.129 1.793l-1.762 1.76c3.104 3.168 5.025 7.488 5.025 12.256s-1.921 9.088-5.025 12.256l1.762 1.76c3.648-3.616 5.887-8.544 5.887-14.016s-2.239-10.432-5.887-14.016z">
        <animate attributeName="opacity" values="0;0;1;1" begin="0" dur="1" keyTimes="0;.666;.667;1" repeatCount="indefinite"/>
      </path>
    </svg>`;

  const loadingSpinnerSVG = `
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 100 100">
      <circle cx="50" cy="50" r="35" stroke-width="10" stroke="#000" fill="none"
        stroke-dasharray="164.93361431346415 56.97787143782138">
        <animateTransform attributeName="transform" type="rotate" repeatCount="indefinite" dur="1s"
          from="0 50 50" to="360 50 50"></animateTransform>
      </circle>
    </svg>`;

  const iconSpan = element.querySelector('.synthesize-icon');
  if (state === "static") {
    iconSpan.innerHTML = staticSVG;
  } else if (state === "playing") {
    iconSpan.innerHTML = animatedSVG;
  } else if (state === "loading") {
    iconSpan.innerHTML = loadingSpinnerSVG;
  }
}

function updateDownloadIcon(element, state) {
  const staticSVG = `
  <svg fill="#000000" height="16px" width="16px" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" 
       viewBox="0 0 489.701 489.701" xml:space="preserve">
    <g>
      <g>
        <g>
          <path d="M244.9,0c-9.5,0-17.1,7.7-17.1,17.2v312.3l-77.6-77.6c-6.7-6.7-17.6-6.7-24.3,0c-6.7,6.7-6.7,17.6,0,24.3l106.9,106.9
              c3.2,3.2,7.6,5,12.1,5c4.6,0,8.9-1.8,12.1-5l106.9-107c6.7-6.7,6.7-17.6,0-24.3s-17.6-6.7-24.3,0L262,329.4V17.2
              C262.1,7.7,254.4,0,244.9,0z"/>
          <path d="M455.8,472.6c0-9.5-7.7-17.2-17.2-17.2H51.1c-9.5,0-17.2,7.7-17.2,17.2s7.7,17.1,17.2,17.1h387.6
              C448.201,489.8,455.8,482.1,455.8,472.6z"/>
        </g>
      </g>
    </g>
  </svg>`;

  const loadingSpinnerSVG = `
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 100 100">
      <circle cx="50" cy="50" r="35" stroke-width="10" stroke="#000" fill="none"
        stroke-dasharray="164.93361431346415 56.97787143782138">
        <animateTransform attributeName="transform" type="rotate" repeatCount="indefinite" dur="1s"
          from="0 50 50" to="360 50 50"></animateTransform>
      </circle>
    </svg>`;

  const iconSpan = element.querySelector('.download-icon');
  if (state === "static") {
    iconSpan.innerHTML = staticSVG;
  } else if (state === "loading") {
    iconSpan.innerHTML = loadingSpinnerSVG;
  }
}

function updateSliderBackground(slider) {
  const value = ((slider.value - slider.min) / (slider.max - slider.min)) * 100;
  slider.style.setProperty("--value", `${value}%`);
}

// function showTooltip(slider) {
//   const tooltip = document.getElementById("tooltip");
//   tooltip.textContent = parseFloat(slider.value).toFixed(2);
//   const rect = slider.getBoundingClientRect();
//   tooltip.style.display = "block";
//   tooltip.style.left =
//     rect.left +
//     window.pageXOffset +
//     slider.offsetWidth / 2 -
//     tooltip.offsetWidth / 2 +
//     "px";
//   tooltip.style.top = rect.top + window.pageYOffset - 30 + "px";
// }

// function hideTooltip() {
//   const tooltip = document.getElementById("tooltip");
//   tooltip.style.display = "none";
// }

function loadLanguagesData() {
  fetch("/static/assets/languages.json")
    .then((response) => response.json())
    .then((data) => populateLanguages(data.languages))
    .catch((error) => console.error("Error loading language data:", error));
}

function populateLanguages(languages) {
  const languageSelect = document.getElementById("language");
  languageSelect.innerHTML = "";

  languages.forEach((lang) => {
    const option = document.createElement("option");
    option.value = lang.code;
    option.textContent = lang.name;
    languageSelect.appendChild(option);
  });

  languageSelect.addEventListener("change", function () {
    const selectedLanguage = languages.find((lang) => lang.code === languageSelect.value);
    if (selectedLanguage) {
      updateVoices(selectedLanguage.voices);
    }
  });

  const defaultLangCode= "en-US";
  const selectedLanguage = languages.find((lang) => lang.code === defaultLangCode);
  if (selectedLanguage) {
    languageSelect.value = defaultLangCode;
    updateVoices(selectedLanguage.voices);
  }else if(languages.length > 0) {
    updateVoices(languages[0].voices);
  }
}

function updateVoices(voices) {
  const voiceSelect = document.getElementById("voice");
  voiceSelect.innerHTML = "";

  voices.forEach((voice) => {
    
    // const cleanedVoice = voice.split(" --")
    const option = document.createElement("option");
    option.value = voice;
    option.textContent = voice;
    voiceSelect.appendChild(option);
  });

  voiceSelect.addEventListener("change",() =>{
    resetSpeedAndPitch();
  })
}
