import type {
  RetrievalFile,
  RetrievalFileCallbackResponse,
  RetrievalFileSignedUrlUpload,
  RetrievalFileSignedUrlUploadResponse,
  WorkbookDetailsViewProps,
} from '../../workbookTypes';

import React, { useEffect, useState } from 'react';
import { useLocation, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';

import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  selectGlobalWorkbookById,
  selectUserWorkbookById,
  fetchMyWorkbookById,
  fetchGlobalWorkbookById,
  createMyWorkbookSession,
  addMockLoadingFile,
  removeMockLoadingFile,
  callbackUploadFile,
} from '@/store/slices/workbookSlice';

import { postWorkbookFiles, putFileDirectly } from '@/api/workbookApi';

import WorkbookUtilityBar from '../../components/WorkbookUtilityBar';
import WorkbookChatView from '../WorkbookChatView/WorkbookChatView';
import { WORKBOOK_CONSTANTS } from '@/config/constants';
import './styles.scss';
import { NotFoundPage } from '@/components/common';

const WorkbookDetailsView: React.FC<WorkbookDetailsViewProps> = (
  props: WorkbookDetailsViewProps
) => {
  const location = useLocation();
  const dispatch = useAppDispatch();
  const [filesToUpload, setFilesToUpload] = useState<File[]>(location?.state?.filesToUpload || []);
  const [isLoading, setIsLoading] = useState(true);
  const [loadError, setLoadError] = useState<Error | null>(null);

  const { workbookId } = useParams();
  const { isGlobal } = props;

  const onFilesToUploadChanged = (validatedFiles: File[]) => {
    const filteredFiles = [];
    for (const file of validatedFiles) {
      switch (file.type) {
        case 'text/plain':
          if (file.size > WORKBOOK_CONSTANTS.fileSizeLimits['text/plain']) {
            toast.error(`${file.name} exceeds the 10 MB size limit for text files`);
          } else {
            filteredFiles.push(file);
          }
          break;
        case 'application/pdf':
          if (file.size > WORKBOOK_CONSTANTS.fileSizeLimits['application/pdf']) {
            toast.error(`${file.name} exceeds the 200 MB size limit for PDFs`);
          } else {
            filteredFiles.push(file);
          }
          break;
        default:
          toast.error('Unsupported file type');
      }
    }
    setFilesToUpload(filteredFiles);
  };

  useEffect(() => {
    const uploadFiles = async (workbookId: string, files: File[]) => {
      const filesByName: Record<string, File> = files.reduce(
        (acc, curr) => {
          acc[curr.name] = curr;
          return acc;
        },
        {} as Record<string, File>
      );

      const signRequest: RetrievalFileSignedUrlUpload = files.map(file => {
        return {
          name: file.name,
          mimeType: file.type,
          fileSizeBytes: file.size,
        };
      });

      const { filesSignedUrls } = await postWorkbookFiles(workbookId, signRequest, isGlobal);
      const uploadTasks: Promise<readonly [RetrievalFileSignedUrlUploadResponse, boolean]>[] =
        filesSignedUrls.map(signedInfo => {
          return putFileDirectly(filesByName[signedInfo.name], signedInfo);
        });
      const uploadResults = await Promise.all(uploadTasks);

      const failedFiles: RetrievalFileSignedUrlUploadResponse[] = uploadResults
        .filter(result => !result[1])
        .map(result => result[0]);
      const uploadedFiles: RetrievalFileSignedUrlUploadResponse[] = uploadResults
        .filter(result => result[1])
        .map(result => result[0]);
      console.log(failedFiles); // Post error notification for each file

      const callbackTasks: Promise<RetrievalFileCallbackResponse>[] = uploadedFiles.map(file => {
        return dispatch(
          callbackUploadFile({
            workbookId: workbookId,
            fileInfo: file,
            isGlobal: isGlobal,
          })
        ).unwrap();
      });
      for (const file of uploadedFiles) {
        dispatch(removeMockLoadingFile({ workbookId: workbook.id, fileName: file.name, isGlobal }));
      }
      await Promise.all(callbackTasks);
      setFilesToUpload([]);
    };

    const files: File[] = (filesToUpload as File[]) ?? [];
    if (files && files.length > 0) {
      for (const file of files) {
        const mockRetrievalFile: RetrievalFile = {
          id: file.name,
          name: file.name,
          mimeType: file.type,
          gcsPath: '',
          fileSizeBytes: file.size,
          tokenCount: 0,
          dataStoreId: workbook?.dataStoreId ?? '',
          chunkSize: workbook?.chunkSize ?? '',
          isChunked: false,
          createdUtc: new Date().toDateString(),
          updatedUtc: new Date().toDateString(),
        };
        dispatch(addMockLoadingFile({ workbook, file: mockRetrievalFile, isGlobal }));
      }
      try {
        uploadFiles(workbookId!, files);
      } catch {
      } finally {
        setFilesToUpload([]);
        window.history.replaceState({}, '');
      }
    }
  }, [filesToUpload, workbookId]);

  const selectById = isGlobal ? selectGlobalWorkbookById : selectUserWorkbookById;
  const workbook = useAppSelector(state => selectById(state, workbookId!));
  useEffect(() => {
    const fetchWorkbook = async () => {
      setIsLoading(true);
      setLoadError(null);
      try {
        const fetchById = isGlobal ? fetchGlobalWorkbookById : fetchMyWorkbookById;
        const workbook = await dispatch(fetchById(workbookId!)).unwrap();
        if (workbook && (workbook?.sessions ?? []).length === 0) {
          await dispatch(createMyWorkbookSession({ workbookId: workbook.id, isGlobal }));
        }
        setIsLoading(false);
      } catch (error) {
        console.error('Failed to fetch workbook:', error);
        setLoadError(error as Error);
      } finally {
        setIsLoading(false);
      }
    };

    if (workbookId) {
      fetchWorkbook();
    }
  }, [workbookId, isGlobal, dispatch]);

  // Show 404 page if workbook not found after loading attempt
  if (!isLoading && (loadError || !workbook)) {
    return (
      <div className="workbook-details-view__not-found flex-1 min-h-0 overflow-y-auto pt-6 px-[64px]">
        <NotFoundPage
          title="404"
          message="This workbook does not exist or you do not have permission to access it."
        />
      </div>
    );
  }

  return (
    <>
      <div className="workbook-details-view__content-wrapper flex flex-row flex-1 min-h-0">
        <div className="workbook-details-view__left-section flex flex-col flex-1 min-h-0">
          <div className="workbook-details-view__chat-outer flex-1 min-h-0 flex flex-col items-center pt-6 px-[64px] overflow-hidden">
            <div className="workbook-details-view__chat-scroll w-full max-w-4xl overflow-y-auto flex-1 flex flex-col items-stretch">
              {workbook && <WorkbookChatView isGlobal={isGlobal} workbook={workbook} />}
            </div>
          </div>
        </div>
        <div className="workbook-details-view__utility-bar w-[300px] flex-shrink-0 border-l border-[#0066B1] overflow-y-auto">
          <WorkbookUtilityBar
            workbook={workbook}
            onFileUpload={onFilesToUploadChanged}
            isGlobal={isGlobal}
          />
        </div>
      </div>
    </>
  );
};

export default WorkbookDetailsView;
