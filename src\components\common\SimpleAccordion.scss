.simple-accordion__main {
  cursor: pointer;
  border: 1px solid #0066B1;
  border-radius: 10px;
  border-bottom-left-radius: 0px;
  border-bottom-right-radius: 0px;

  &.simple-accordion__main--disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
  &.simple-accordion__main--expanded {
    .simple-accordion__expand-icon {
      transform: rotate(-180deg) !important;
    }
    .simple-accordion__header {
      border-bottom: 1px solid #0066B1;
      transition: border-bottom 0.3s ease-in-out;
    }
    .simple-accordion__content {
      margin: 16px;
    }
  }
  .simple-accordion__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border-bottom: 0px solid #0066B1;
    transition: border-bottom 0.3s ease-in-out;
    .simple-accordion__title {
      color: 'rgb(236, 247, 255)';
      font-family: var(--font-sofia-pro);
      font-size: 14px;
      font-weight: 799;
      letter-spacing: .224px;
      user-select: none;
      cursor: default;
    }
    .simple-accordion__expand-icon {
      transform: rotate(0deg);
      transition: transform 0.3s ease-in-out;
    }
  }
  .simple-accordion__content {
    margin: 0px;
    overflow: hidden;
    transition: max-height 0.3s ease-in-out, margin 0.3s ease-in-out;
  }
}
