import React from 'react';

import resourceData from '../resources.json';
import './ResourcesView.scss';

interface HelpfulLink {
  title: string;
  url: string;
}

interface ModelDetailsSection {
  heading: string;
  content: string[];
}

interface ResourcesData {
  title: string;
  introduction: string[];
  helpfulLinks: HelpfulLink[];
  modelDetailsTitle: string;
  modelDetailsSection: ModelDetailsSection[];
}

const ResourcesView: React.FC = () => {
  const data: ResourcesData = resourceData;

  return (
    <div className="resourcesview-layout">
      <div className="resources-container">
        {/* Main Title */}
        <h1>{data.title}</h1>

        {/* Introduction */}
        {data.introduction.map(introduction => (
          <p key={introduction} className="resources-text-content">{introduction}</p>
        ))}

        <div className="resource-link-list-container">
          <ul className="resource-link-list">
            {data.helpfulLinks.map(helpfulLink => (
              <li key={helpfulLink.title} className="resource-link-item">
                <a
                  href={helpfulLink.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="resource-link-item-text"
                >
                  {helpfulLink.title}
                </a>
              </li>
            ))}
          </ul>
        </div>

        {/* Model Details Title */}
        <h1>{data.modelDetailsTitle}</h1>

        <div>
          {data.modelDetailsSection.map((modelInformation, modelInfoIndex) => (
            <React.Fragment key={modelInformation.heading}>
              <h1 className="model-information-title">{modelInformation.heading}</h1>
              <div className="model-information-list-container">
                <ul className="model-information-list">
                  {modelInformation.content.map((content, contentIndex) => 
                    <li className="model-information-item" key={`${modelInfoIndex}-${contentIndex}`}>
                        {content}
                        {content.includes("Temperature button values") && (
                          <div className="model-information-list-container">
                            <ul className="model-information-list">
                              <li className="model-information-item">Less Creative: 0.2</li>
                              <li className="model-information-item">Classic: 1.0</li>
                              <li className="model-information-item">More Creative: 1.8</li>
                            </ul>
                          </div>
                        )
                        }
                    </li>)}
                </ul>
              </div>
            </React.Fragment>
            )
          )}
        </div>
      </div>
    </div>
  )
}

export default ResourcesView;
