$base-font-size: 18px; 
$base-spacing-unit: 0.5rem;
$code-border-radius: 6px;

.markdown-body {
  /* --- THEME COLORS  --- */
  --text-color: #e0e0e0;
  --link-color: #8ab4f8;
  --border-color: #4a4a4a;
  --code-header-bg: #393939;
  --code-content-bg: #242629;
  --code-header-text: #ccc;
  --code-copy-btn-text: #ccc;
  --code-copy-btn-hover-text: #ffffff;
  --inline-code-bg: rgba(110, 118, 129, 0.4);
  --inline-code-text: inherit;
  --table-border: rgba(59, 130, 246, 0.4);
  --table-header-bg: rgba(30, 64, 175, 0.5);
  --table-header-text: #dbeafe;
  --table-row-hover-bg: rgba(30, 64, 175, 0.3);

  &.user-message {
    --text-color: #ffffff;
    --table-border: #2c4a6f;
    --table-header-bg: #1e3a5f;
    --table-row-hover-bg: rgba(0, 45, 79, 0.3);
  }

  /* --- BASE CONTAINER & TYPOGRAPHY --- */
  color: var(--text-color);
  line-height: 1.6;
  font-size: $base-font-size;

  /* Ensure superscripts don't get cut off at the top */
  > *:first-child {
    margin-top: 0.25rem;
  }
  
  /* Reset margin for elements that don't need it */
  > h1:first-child,
  > h2:first-child, 
  > h3:first-child {
    margin-top: ($base-spacing-unit * 4);
  }

  /* === General Link Styling === */
  a {
    color: var(--link-color);
    text-decoration: none;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
    }
  }

  /* === Paragraphs === */
  p {
    margin: 0 0 ($base-spacing-unit * 2) 0; 
  }

  /* === Lists === */
  ul,
  ol {
    margin: ($base-spacing-unit * 2) 0; 
    padding-left: ($base-spacing-unit * 4.5); 
  }

  li {
    margin: 0;
    & + li {
      margin-top: ($base-spacing-unit * 1.2);
    }
    
    > p {
      display: block;
      margin-bottom: 0;
    }
  }

  /* List markers */
  ul { list-style: disc; }
  ul ul { list-style: circle; }
  ul ul ul { list-style: square; }
  ol { list-style: decimal; }
  ol ol { list-style: lower-alpha; }
  ol ol ol { list-style: lower-roman; }
  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3; 
    margin: ($base-spacing-unit * 4) 0 ($base-spacing-unit * 2) 0; 
  }
  h1 { font-size: 2rem; }
  h2 { font-size: 1.55rem; }
  h3 { font-size: 1.22rem; }

  /* === Inline Elements === */
  .inline-code {
    padding: 0.15em 0.4em;
    font-size: 85%;
    background-color: var(--inline-code-bg);
    color: var(--inline-code-text);
    border-radius: 4px;
    font-family: monospace;
    vertical-align: baseline;
  }

  /* --- BLOCK ELEMENTS STYLING --- */
  .table-wrapper {
    margin: ($base-spacing-unit * 4) 0;
    border: 1px solid var(--table-border);
    border-radius: $code-border-radius;
    overflow: hidden;
    overflow-x: auto;
  }

  /* === Code Blocks === */
  .code-block-wrapper {
    margin: ($base-spacing-unit * 4) 0; 
    border-radius: $code-border-radius;
    overflow: hidden;
    border: 1px solid var(--border-color);
  }

  .code-block-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 2.5rem;
    padding: 0 ($base-spacing-unit * 2.5); 
    font-size: 0.875rem;
    font-family: sans-serif;
    background-color: var(--code-header-bg);
    color: var(--code-header-text);

    .code-language {
      font-weight: 600;
      text-transform: uppercase;
    }
    .copy-button {
      background: none;
      border: none;
      color: var(--code-copy-btn-text);
      cursor: pointer;
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      transition: color 0.2s, background-color 0.2s;

      &:hover {
        color: var(--code-copy-btn-hover-text);
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
  }

  .code-block-content {
    margin: 0;
    overflow-x: auto;
    background-color: var(--code-content-bg);
  }

  /* === Tables === */
  table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;

    th,
    td {
      padding: ($base-spacing-unit * 2) ($base-spacing-unit * 2.5); 
      text-align: left;
      vertical-align: top;
      border-right: 1px solid var(--table-border);
      &:last-child { border-right: none; }
    }

    thead {
      background-color: var(--table-header-bg);
      color: var(--table-header-text);
      th { font-weight: 600; }
    }

    tbody tr {
      border-bottom: 1px solid var(--table-border);
      transition: background-color 0.2s;
      &:last-child { border-bottom: none; }
      &:hover { background-color: var(--table-row-hover-bg); }
    }
  }

  /* === Inline (span) container === */
  &.inline {
    display: inline;
    
    > * {
      &:only-child {
        /* Keep small inline elements inline */
        &:not(p):not(ul):not(ol):not(h1):not(h2):not(h3):not(.code-block-wrapper):not(table) {
          display: inline;
          margin: 0;
        }
      }
    }
  }

  /* === policy web table === */
  &.policy-render {
    .policy-table-wrapper {
      margin: 1rem 0;
      border-radius: 0.5rem;
      overflow: hidden;
      border: 1px solid #4a5568; 
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      overflow-x: auto;
    }

    table {
      margin: 0;
      border: none;
      min-width: 100%;
      border-collapse: collapse;
      font-size: 0.875rem;
    }

    thead {
      background-color: #003963;
      color: white;
      text-align: center;
      th {
        background-color: #003963;
        padding: 1rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        border-bottom: 1px solid #2d3748; 
        // avoid table title not align issue
        white-space: nowrap;
        word-break: keep-all;
      }
    }

    tbody {
      background-color: #00223c;
      color: white;
      tr {
        border-color: #2d3748; 
        border-bottom: 1px solid #4a5568; 
        transition: background-color 0.2s ease-in-out;
        &:hover {
          background-color: rgba(0, 64, 128, 0.9);
        }
      }
    }

    td {
      padding: 0.75rem 1rem;
      border-color: #4a5568;
      border-right: 1px solid #4a5568;
      &:last-child {
        border-right: none;
      }
    }

    a {
      color: #60a5fa; 
      text-decoration: underline;

      &:hover {
        color: #93c5fd; 
        text-decoration: underline;
      }
    }
  }

  /* === Citation Styling === */
  sup {
    &.citation-superscript {
      color: #1d4ed8; 
      font-weight: bold;
      cursor: pointer;
    }
  }

  &.user-message sup.citation-superscript,
  [data-theme="dark"] & sup.citation-superscript {
    color: #60a5fa; 
  }

  /* === Citation Title Renderer === */
  &.citation-title-renderer {
    font-size: 14px !important;
    font-family: 'Roboto', sans-serif !important;
    color: #000000 !important;
    margin: 0 !important;
    padding: 0 !important;
    display: inline !important;
    
    * {
      font-size: 14px !important;
      font-family: 'Roboto', sans-serif !important;
      color: #000000 !important;
      margin: 0 !important;
    }
  }

}
