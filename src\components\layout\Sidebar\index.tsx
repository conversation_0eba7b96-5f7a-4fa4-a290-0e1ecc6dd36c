import type { SidebarLinkProps } from '../SidebarLink';

import React, { useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { MdOutlineTranslate, MdHearing } from 'react-icons/md';
import { FaHistory } from 'react-icons/fa';
import { LuNotebookText } from 'react-icons/lu';
import { PiBookOpenTextFill } from "react-icons/pi";
import { GoQuestion } from 'react-icons/go';
import { PiChatsBold } from 'react-icons/pi';
import { useAppDispatch } from '@/store/hooks';
import { startNewChat } from '@/store/slices/chatSlice';

import { SidebarContext } from '@contexts/SidebarContext';
import { useThemeStyles } from '@/hooks/useThemeStyles';

import SidebarHeader from '../SidebarHeader';
import SidebarLink from '../SidebarLink';

// Restore SCSS import
import './styles.scss';

const Sidebar: React.FC = () => {
  const { classes } = useThemeStyles();
  const { toggleSidebar } = useContext(SidebarContext);
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const handleStartNewChatClick = () => {
    dispatch(startNewChat());

    navigate('/chat', {
      replace: true,
      state: null,
    });
  };

  // Navigation Items Configuration
  const linkItems: SidebarLinkProps[] = [
    {
      to: 'chat',
      label: 'Start Chatting',
      IconComponent: <PiChatsBold className="h-5 w-5" />,
      onClick: handleStartNewChatClick,
    },
    {
      to: 'history',
      label: 'Chat History',
      IconComponent: <FaHistory className="h-5 w-5" />,
    },
    {
      to: '/pl',
      label: 'Prompt Library',
      IconComponent: <PiBookOpenTextFill className="h-5 w-5" />,
      subLinks: [
        {
          to: 'prompt_library/public',
          label: 'Public Prompts',
        },
        {
          to: 'prompt_library/my',
          label: 'My Prompts',
        },
      ]
    },
    {
      label: 'Workbooks',
      to: '/w',
      IconComponent: <LuNotebookText className="h-5 w-5" />,
      subLinks: [
        {
          to: 'workbooks/public',
          label: 'Public Workbooks',
        },
        {
          to: 'workbooks/my',
          label: 'My Workbooks',
        },
      ],
    },
    {
      to: 'translate',
      label: 'Translate',
      IconComponent: <MdOutlineTranslate className="h-5 w-5" />,
    },
    {
      to: 'texttospeech',
      label: 'Text-to-Speech',
      IconComponent: <MdHearing className="h-5 w-5" />,
    },
    {
      to: 'resources',
      label: 'Resources',
      IconComponent: <GoQuestion className="h-5 w-5" />,
    },
  ];

  return (
    // Restore original className structure relying on SCSS
    <div className={`${classes.backgroundSidebar} sidebar-main`}>
      <div className="sidebar-content">
        <SidebarHeader onToggle={toggleSidebar} />
        <div className="sidebar-link-items">
          {linkItems.map(item => (
            <SidebarLink
              key={item.to || item.label}
              to={item.to}
              label={item.label}
              IconComponent={item.IconComponent}
              subLinks={item?.subLinks}
              onClick={item.onClick}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
