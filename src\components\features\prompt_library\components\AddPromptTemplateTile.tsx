import React from 'react';
import { IoMdAdd } from 'react-icons/io';
import { useThemeStyles } from '@hooks/useThemeStyles';
import { AddPromptTemplateTileProps } from '@/types/promptTemplateTypes';

const AddPromptTemplateTile: React.FC<AddPromptTemplateTileProps> = ({ onClick, isGlobal }) => {
  const { classes, colors } = useThemeStyles();

  const styles = {
    tile: `
      flex w-[240px] h-[280px] 
      rounded-[12px] 
      border border-[${colors.border}]
      ${classes.background}
      cursor-pointer
      hover:opacity-80
      transition-opacity
      focus:outline-none
      focus:ring-2
      focus:ring-blue-500
    `,
    contentContainer: 'flex-1 flex flex-col items-center justify-center gap-4',
    icon: `w-12 h-12 ${classes.text}`,
    text: `font-roboto text-[16px] font-medium ${classes.text} text-center px-4`,
  };

  return (
    <div
      className={styles.tile}
      onClick={onClick}
      role="button"
      aria-label="Add new prompt template"
    >
      <div className={styles.contentContainer}>
        <IoMdAdd className={styles.icon} aria-hidden="true" />
        <span className={styles.text}>Save a new {isGlobal ? 'Public' : ''} Prompt Template</span>
      </div>
    </div>
  );
};

export default AddPromptTemplateTile;
