import json
from abc import ABC
from datetime import datetime, timezone
from typing import <PERSON><PERSON><PERSON>, Generator, List, Literal, Optional

from firebase_admin.firestore import firestore
from pydantic import (
    BaseModel,
    Field,
    PrivateAttr,
    computed_field,
    field_validator,
    model_validator,
)

from lib.firestore_client import FirestoreClient, FirestoreLimits
from models.retrieval_workbook.RetrievalCitation import (
    RetrievalCitation,
    RetrievalCitationSource,
)
from models.SidekickBaseModel import SidekickBaseModel
from models.utils.partial_model import partial_model


class RetrievalWorkbookMessage(BaseModel):
    """For Pubsub use only"""

    message_id: str
    workbook_session_id: str
    workbook_id: str
    workbook_user: str
    message: str
    message_role: Literal["user", "model"] = Field(
        ..., description="Must be 'user' or 'model'"
    )
    created_utc: datetime
    model_feedback: Optional[bool] = Field(default=None)
    has_citations: Optional[bool] = Field(default=None)
    is_public: Optional[bool] = Field(default=False)

    @computed_field
    @property
    def id(self) -> str:
        domain = self.workbook_user.split("@")[1]
        return (
            f"{domain}-{self.workbook_id}-{self.workbook_session_id}-{self.message_id}"
        )

    @field_validator("workbook_user", mode="after")
    @classmethod
    def validate_workbook_user(cls, value: str):
        if "@" not in value:
            raise ValueError("workbook_user must be an email")
        return value.strip().lower()

    def to_pubsub_message(self) -> bytes:
        message_dict = self.model_dump()

        message_dict["created_utc"] = self.created_utc.isoformat()

        message_dict["_CHANGE_TYPE"] = "UPSERT"
        if self.has_citations is not None:
            message_dict["has_citations"] = {"boolean": self.has_citations}

        if self.model_feedback is not None:
            message_dict["model_feedback"] = {"boolean": self.model_feedback}

        if self.is_public is not None:
            message_dict["is_public"] = {"boolean": self.is_public}
            
        return json.dumps(message_dict).encode("utf-8")


class RetrievalSessionMessage(SidekickBaseModel, ABC):

    COLLECTION_NAME: ClassVar[str] = "RetrievalMessages"

    id: str
    created_utc: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    conversation_role: Literal["user", "model"] = Field(
        ..., description="Must be 'user' or 'model'"
    )

    def to_dict(self, date_format_iso=True, include_document_id=False, to_camel=False):
        mode = "json" if date_format_iso else "python"
        exclude = {}
        if not include_document_id:
            exclude["id"] = True

        return self.model_dump(mode=mode, exclude=exclude, by_alias=to_camel)


class RetrievalSessionQuery(RetrievalSessionMessage):
    query: str
    conversation_role: Literal["user"] = "user"


_createRetrievalSessionQuery = partial_model(RetrievalSessionQuery, "id")
CreateRetrievalSessionQuery = _createRetrievalSessionQuery


class RetrievalSessionAnswer(RetrievalSessionMessage):
    answer: str
    conversation_role: Literal["model"] = "model"
    state: Literal["STATE_UNSPECIFIED", "IN_PROGRESS", "FAILED", "SUCCEEDED"]
    citations: List[RetrievalCitation] = Field(default_factory=list)
    citation_sources: List[RetrievalCitationSource] = Field(default_factory=list)
    feedback: Optional[bool] = Field(default=None)


_createRetrievalSessionAnswer = partial_model(RetrievalSessionAnswer, "id", "feedback")
CreateRetrievalSessionAnswer = _createRetrievalSessionAnswer


class RetrievalSession(SidekickBaseModel):

    COLLECTION_NAME: ClassVar[str] = "RetrievalSessions"

    id: str
    user_id: str  # email, maybe List[str]? Multiple users same Session? Multiple users same Workbook?
    messages: List[RetrievalSessionQuery | RetrievalSessionAnswer] = Field(
        default_factory=list
    )
    created_utc: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    def to_dict(
        self,
        date_format_iso=True,
        include_document_id=False,
        to_camel=False,
        to_exclude={},
    ):
        mode = "json" if date_format_iso else "python"
        exclude = {**to_exclude}
        if not include_document_id:
            exclude["id"] = True

        return self.model_dump(mode=mode, exclude=exclude, by_alias=to_camel)


CreateRetrievalSession = partial_model(RetrievalSession, "id", "messages")

# if __name__ == "__main__":
#     RWM = RetrievalWorkbookMessage(
#         message_id="",
#         workbook_session_id="",
#         workbook_id="",
#         workbook_user="",
#         message="",
#         message_role="user",
#         created_utc=datetime.now(),
#     )
#     print(RWM)
