import React, { useState } from 'react';
import { CitationRendererProps, RetrievalCitationSource } from '@/components/features/workbook/workbookTypes';
import MarkdownRenderer from '../../../../common/markdown/MarkdownRenderer';
import { FiChevronDown, FiChevronUp, FiFile } from 'react-icons/fi';
import { processCitationsIntoMarkdown } from './utils/citationUtils';

interface EnhancedCitationRendererProps extends CitationRendererProps {
  renderWithMarkdown?: boolean;
  messageId: string;
}

// Helper function to truncate text with ellipsis
const truncateText = (text: string, maxLength: number = 100): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

const CitationRenderer: React.FC<EnhancedCitationRendererProps> = ({
  message,
  citations = [],
  citationSources = [],
  isUser,
  textContentStyle,
  renderWithMarkdown = false,
  messageId,
}) => {
  // State: controls expansion/collapse of the bottom citation list
  const [showCitationList, setShowCitationList] = useState(false);
  // Use a proper type for the expandedCitations state
  const [expandedCitations, setExpandedCitations] = useState<{ [key: string]: boolean }>({});

  // If no citations, return the content directly
  if (!citations || citations.length === 0) {
    if (renderWithMarkdown) {
      return <MarkdownRenderer content={message} className={textContentStyle} isUser={isUser} />;
    } else {
      return <div className={textContentStyle}>{message}</div>;
    }
  }

  // Process citations into markdown with single-pass rendering
  const { processedContent, citationMap } = processCitationsIntoMarkdown(message, citations);

  // Function to handle citation click
  const handleCitationClick = (citationNumber: number) => {
    const targetId = `citation-detail-${messageId}-${citationNumber - 1}`;
    setShowCitationList(true);

    setTimeout(() => {
      document.getElementById(targetId)?.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }, 50);
  };

  // Toggle expansion of a citation source
  const toggleCitationExpansion = (idx: number) => {
    setExpandedCitations(prev => ({
      ...prev,
      [idx.toString()]: !prev[idx.toString()],
    }));
  };

  return (
    <div className={textContentStyle}>
      {/* Main content with citations using single MarkdownRenderer */}
      <div style={{ marginBottom: '0.5rem' }}>
        {renderWithMarkdown ? (
          <MarkdownRenderer
            content={processedContent}
            className=""
            isUser={isUser}
            onCitationClick={handleCitationClick}
          />
        ) : (
          <div>{processedContent}</div>
        )}
      </div>

      {/* Bottom citation list with collapse/expand button */}
      <div style={{ marginTop: '0.75rem', paddingTop: '0.25rem' }}>
        <button
          onClick={() => setShowCitationList(prev => !prev)}
          className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 flex items-center gap-1 focus:outline-none transition-colors"
        >
          {showCitationList ? <FiChevronUp className="text-blue-600" /> : <FiChevronDown className="text-blue-600" />}
          {showCitationList ? 'Hide Citations' : 'Show Citations'}
        </button>

        {showCitationList && (
          <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
            {Array.from(citationMap.values()).map(({ originalIndex, citationNumber, citation }) => {
              // Get the cited text for display
              const citedText = message.substring(citation.start_index, citation.end_index);
              const isExpanded = expandedCitations[originalIndex.toString()] || false;

              // Handle long cited text with expand/collapse
              const displayCitedText = isExpanded ? citedText : truncateText(citedText, 150);

              const citationDetailId = `citation-detail-${messageId}-${originalIndex}`;

              return (
                <div
                  id={citationDetailId}
                  key={originalIndex}
                  className="mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
                >
                  <div className="flex items-start mb-2">
                    <span className="inline-flex items-center justify-center w-5 h-5 text-xs font-bold rounded-full mr-2 bg-blue-100 text-blue-700 dark:bg-blue-800 dark:text-blue-100 flex-shrink-0">
                      {citationNumber}
                    </span>
                    <strong className="text-sm text-gray-800 dark:text-gray-200 italic break-words">
                      <MarkdownRenderer
                        content={displayCitedText}
                        isInline={true}
                        isUser={isUser}
                        className="citation-title-renderer"
                      />
                      {citedText.length > 150 && (
                        <button
                          className="text-blue-600 dark:text-blue-400 ml-1 text-xs underline focus:outline-none"
                          onClick={() => toggleCitationExpansion(originalIndex)}
                        >
                          {isExpanded ? 'Show less' : 'Show more'}
                        </button>
                      )}
                    </strong>
                  </div>

                  {/* List all sources for this citation */}
                  <div className="pl-2">
                    {citation.citation_sources.map((source_index: number, sourceIdx: number) => {
                      const citation_source: RetrievalCitationSource = citationSources[source_index];
                      const sourceKey = `${originalIndex}-${sourceIdx}`;
                      const isSourceExpanded = expandedCitations[sourceKey] || false;
                      const displayContent = isSourceExpanded
                        ? citation_source.chunk_content
                        : truncateText(citation_source.chunk_content, 100);

                      return (
                        <div key={sourceIdx} className="mb-2">
                          <div className="flex items-center">
                            <FiFile className="mr-1 text-blue-600 dark:text-blue-400" size={12} />
                            <div className="italic text-sm text-gray-700 dark:text-gray-300">
                              {citation_source.name}
                            </div>
                          </div>
                          <div className="text-xs text-gray-600 dark:text-gray-400 mt-1 pl-4 border-l border-gray-200 dark:border-gray-700">
                            {displayContent}
                            {citation_source.chunk_content.length > 100 && (
                              <button
                                className="text-blue-600 dark:text-blue-400 ml-1 text-xs underline focus:outline-none"
                                onClick={() =>
                                  setExpandedCitations(prev => ({
                                    ...prev,
                                    [sourceKey]: !isSourceExpanded,
                                  }))
                                }
                              >
                                {isSourceExpanded ? 'Show less' : 'Show more'}
                              </button>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default CitationRenderer;
