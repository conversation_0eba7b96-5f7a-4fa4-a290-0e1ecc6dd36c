import { useState } from 'react';
import { UseMessageActionsResult } from '@/components/features/workbook/workbookTypes';
// import { sendFeedbackByMessageId } from '@/api/workbookApi'
export const useMessageActions = (message: string): UseMessageActionsResult => {
  const [copied, setCopied] = useState(false);
  const [liked, setLiked] = useState(false);
  const [disliked, setDisliked] = useState(false);

  // Handle copy action
  const handleCopy = (e: React.MouseEvent) => {
    e.stopPropagation();
    navigator.clipboard.writeText(message);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  // Handle thumbs up action
  const handleThumbsUp = (e: React.MouseEvent) => {
    e.stopPropagation();
    setLiked(true);
    if (disliked) setDisliked(false);
    // console.log('Message liked');
    // Need
    //  Workbook ID from URL
    //  Session ID
    //  Message ID
    // Reset after visual feedback
    // sendUserFeedbackByMessageId("test", "test", "test", 1)
    setTimeout(() => setLiked(false), 1000);
  };

  // Handle thumbs down action
  const handleThumbsDown = (e: React.MouseEvent) => {
    e.stopPropagation();
    setDisliked(true);
    if (liked) setLiked(false);
    // console.log('Message disliked');

    // sendUserFeedbackByMessageId("test", "test", "test", 0)
    // Reset after visual feedback
    setTimeout(() => setDisliked(false), 1000);
  };

  return {
    state: {
      copied,
      liked,
      disliked,
    },
    handleCopy,
    handleThumbsUp,
    handleThumbsDown,
  };
};

