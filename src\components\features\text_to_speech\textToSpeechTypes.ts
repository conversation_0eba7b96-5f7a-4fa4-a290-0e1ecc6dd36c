import { ReactNode } from 'react';

export interface AdvancedSettingsProps {
  children: ReactNode;
}

export interface TTSLanguagesJSON {
  languages: {
    name: string;
    code: string;
    voices: string[];
  }[];
}

export type TTSParams = {
  message: string;
  language: string;
  voice: string;
  encoding: string;
  speed: number;
  pitch: number;
};

export interface TTSInputProps {
  message: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
}

export interface TTSDownloadResponse {
  download_url: string;
  message: string;
  success: boolean;
}

