import { Accordion } from '@base-ui-components/react';
import './AdvancedSettings.scss';

import { AdvancedSettingsProps } from '../textToSpeechTypes';

import GearIcon from '@common/icons/GearIcon';


const AdvancedSettings = ({ children }: AdvancedSettingsProps) => {
  return (
    <Accordion.Root className="advanced-settings__root">
      <Accordion.Item className="advanced-settings__item">
        <Accordion.Header>
          <Accordion.Trigger className="advanced-settings__trigger">
            <span className="advanced-settings__icon"><GearIcon /></span>
            <span className="advanced-settings__trigger-text">Advanced Options</span>
          </Accordion.Trigger>
        </Accordion.Header>
        <Accordion.Panel className="advanced-settings__panel">
          <div className="advanced-settings__panel-content">
            {children}
          </div>
        </Accordion.Panel>
      </Accordion.Item>
    </Accordion.Root>
  );
};

export default AdvancedSettings;
