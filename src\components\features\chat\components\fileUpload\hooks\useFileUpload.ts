import { useState, useCallback, useEffect, useReducer, useMemo } from 'react';
import { useAppSelector } from '@/store/hooks';
import { selectFirestoreSessionId, selectSelectedChatType } from '@/store/slices/chatSlice';
import { selectCurrentUser } from '@/store/slices/authSlice';
import { ValidatedFile, FileInfo, UploadStatus } from '@/types/fileUpload';
import { showToast } from '@/components/common/ToastNotification/ToastNotification';
import { useGCSUploadManager } from './useGCSUploadManager';
import {
  createValidatedFileFromFile,
  checkModelRestriction,
  getFilesReadyForUpload,
  hasUploadingFiles,
} from '../utils/fileUploadHelpers';
import { useDropzoneFiles } from './useDropzoneFiles';
import { withUCDAttestation } from '@/utils/ucdFileUploadWrapper';
import { filesReducer, FilesAction } from '../state/fileUploadReducer';
import { handleBatchUpload } from '../utils/batchUploadHandler';

export interface UseFileUploadResult {
  selectedFiles: ValidatedFile[];
  addFiles: (newFiles: File[]) => Promise<boolean>;
  removeFile: (fileId: string) => void;
  clearAllFiles: () => void;
  clearUploadedFiles: () => void;
  getSuccessfullyUploadedFilesInfo: () => FileInfo[];
  isUploading: boolean;
  getRootProps: <T extends Record<string, any>>(props?: T) => T;
  getInputProps: <T extends Record<string, any>>(props?: T) => T;
  isDragActive: boolean;
  openFileDialog: () => void;
}

/**
 * Orchestrates the file upload feature by integrating state, UI, and asynchronous operations.
 *
 * Responsibilities:
 * - Manages the list of selected files using `useReducer` (`filesReducer`).
 * - Initializes child hooks (`useGCSUploadManager`, `useDropzoneFiles`) for specific tasks.
 * - Triggers the upload process in an effect when new files are added.
 * - Delegates the batch upload logic to `handleBatchUpload`.
 * - Manages the lifecycle of a file: selection -> validation -> upload -> success/error/cancellation -> deletion.
 *
 * Note: File removal now skips backend deletion to prevent session creation in welcome view.
 * Files uploaded without being sent in a prompt will be cleaned up by GCS 24-hour policy.
 */
export const useFileUpload = (): UseFileUploadResult => {
  const currentFirestoreSessionId = useAppSelector(selectFirestoreSessionId);
  const selectedChatType = useAppSelector(selectSelectedChatType);
  const currentUser = useAppSelector(selectCurrentUser);
  const [state, dispatch] = useReducer(filesReducer, { files: new Map() });
  const selectedFiles = useMemo(() => Array.from(state.files.values()), [state.files]);

  // A state-based "lock" to prevent multiple `handleBatchUpload` calls from running simultaneously.
  const [isBatchUploadInProgress, setIsBatchUploadInProgress] = useState(false);
  const [reportedIsUploadingGlobal, setReportedIsUploadingGlobal] = useState(false);

  const gcsUploadManager = useGCSUploadManager();
  const {
    isUploading: isFileCurrentlyUploadingToGCS,
    cancelUpload,
    cancelAllUploads,
  } = gcsUploadManager;

  useEffect(() => {
    const filesToProcess = getFilesReadyForUpload(
      selectedFiles,
      gcsUploadManager.isCancellationRequested
    );

    // only proceed if there are files to upload, no other batch is currently in progress,
    // NOTE: removed the sessionID requirement to allow uploads
    if (!filesToProcess.length || isBatchUploadInProgress) {
      return;
    }

    const process = async () => {
      setIsBatchUploadInProgress(true); // Acquire the lock
      await handleBatchUpload({
        filesToProcess,
        gcsUploadManager,
        dispatch: dispatch as React.Dispatch<FilesAction>,
        session: {
          id: currentFirestoreSessionId,
          type: selectedChatType,
        },
      });
      setIsBatchUploadInProgress(false); // Release the lock
    };

    process();
  }, [
    selectedFiles,
    isBatchUploadInProgress,
    gcsUploadManager,
    currentFirestoreSessionId,
    selectedChatType,
  ]);

  useEffect(() => {
    setReportedIsUploadingGlobal(hasUploadingFiles(selectedFiles, isFileCurrentlyUploadingToGCS));
  }, [selectedFiles, isFileCurrentlyUploadingToGCS]);

  useEffect(() => {
    // Automatically remove files marked as 'cancelled' from the UI after the cancellation is fully processed.
    if (selectedFiles.some(f => f.uploadStatus === 'cancelled')) {
      dispatch({ type: 'REMOVE_CANCELLED' });
    }
  }, [selectedFiles]);

  /**
   * Adds new files to the state after running them through the UCD attestation flow.
   */
  const addFiles = useCallback(
    async (newFiles: File[]): Promise<boolean> => {
      if (!checkModelRestriction(selectedChatType)) {
        showToast.info(
          'File Upload Restricted',
          'File uploads are only allowed in the General model.'
        );
        return false;
      }

      const attestationOk = await withUCDAttestation(
        newFiles,
        'chat',
        currentUser,
        (files: File[]) => {
          const processed = files.map(createValidatedFileFromFile);
          dispatch({ type: 'ADD_FILES', payload: processed });
        },
        newFiles
      );

      if (!attestationOk) {
        console.log('[useFileUpload] UCD attestation was cancelled or failed.');
        return false;
      }
      return true;
    },
    [selectedChatType, currentUser]
  );

  /**
   * A callback specifically for `useDropzoneFiles` to process files after they've been
   * validated by the dropzone (for size, type, etc.). It separates valid files to be
   * sent through the `addFiles` flow from invalid ones that are added directly to the state for display.
   */
  const handleFilesFromDropzone = useCallback(
    async (validated: ValidatedFile[]) => {
      const acceptedFiles = validated
        .filter(vf => !vf.validationError && vf.originalFile)
        .map(vf => vf.originalFile!);

      if (acceptedFiles.length) {
        await addFiles(acceptedFiles);
      }

      const invalidFiles = validated.filter(vf => vf.validationError);
      if (invalidFiles.length) {
        dispatch({ type: 'ADD_FILES', payload: invalidFiles });
      }
    },
    [addFiles]
  );

  const { getRootProps, getInputProps, isDragActive, openFileDialog } = useDropzoneFiles({
    selectedChatType,
    onFilesProcessed: handleFilesFromDropzone,
  });

  /*
   * Handles the removal of a single file, managing different logic based on the file's current state.
   */
  const removeFile = useCallback(
    (fileId: string) => {
      const fileToRemove = state.files.get(fileId);
      if (!fileToRemove) return;

      // Case 1: The file was successfully uploaded.
      // Changed: Skip backend deletion, just remove from local state.
      // Let GCS 24-hour deletion policy handle unused files.
      if (fileToRemove.uploadStatus === 'success' && fileToRemove.gcs_uri) {
        dispatch({ type: 'REMOVE_FILE', payload: { fileId } });
        return;
      }

      // Case 2: The file is pending or actively uploading. The upload operation should be cancelled.
      // We signal the GCS manager to abort and update the UI to a 'cancelling' or 'cancelled' state.
      if (
        ['uploading', 'pending'].includes(fileToRemove.uploadStatus ?? '') ||
        isFileCurrentlyUploadingToGCS(fileId)
      ) {
        cancelUpload(fileId);
        const newStatus =
          fileToRemove.uploadStatus === 'uploading' || isFileCurrentlyUploadingToGCS(fileId)
            ? 'cancelling'
            : 'cancelled';

        const updates: Partial<ValidatedFile> = {
          uploadStatus: newStatus as UploadStatus,
          isCancellationRequested: true,
        };

        if (newStatus === 'cancelled') {
          updates.uploadError = 'Upload cancelled by user.';
        }

        dispatch({ type: 'UPDATE_FILE', payload: { fileId, updates } });
        return;
      }

      // Case 3: Any other state (e.g., validation error, already cancelled). Just remove from local state.
      dispatch({ type: 'REMOVE_FILE', payload: { fileId } });
    },
    [state.files, cancelUpload, isFileCurrentlyUploadingToGCS]
  );

  const getSuccessfullyUploadedFilesInfo = useCallback((): FileInfo[] => {
    return selectedFiles
      .filter(f => f.uploadStatus === 'success' && f.gcs_uri)
      .map(f => ({
        name: f.name,
        type: f.type,
        size: f.size,
        gcs_uri: f.gcs_uri!,
      }));
  }, [selectedFiles]);

  const clearAllFiles = useCallback(() => {
    cancelAllUploads();
    dispatch({ type: 'CLEAR_ALL' });
  }, [cancelAllUploads]);

  const clearUploadedFiles = useCallback(() => {
    dispatch({ type: 'CLEAR_UPLOADED' });
  }, []);

  return {
    selectedFiles,
    addFiles,
    removeFile,
    clearAllFiles,
    clearUploadedFiles,
    getSuccessfullyUploadedFilesInfo,
    isUploading: reportedIsUploadingGlobal,
    getRootProps,
    getInputProps,
    isDragActive,
    openFileDialog,
  };
};

