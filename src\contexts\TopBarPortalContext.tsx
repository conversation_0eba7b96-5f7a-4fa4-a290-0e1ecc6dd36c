import React, { createContext, useState, useContext } from 'react';

export type TopBarPortalContextType = {
    topBarPortalRef: HTMLElement | null
    setTopBarPortalRef: React.Dispatch<React.SetStateAction<HTMLElement | null>>
}

const TopBarPortalContext = createContext<TopBarPortalContextType>({
    topBarPortalRef: null,
    setTopBarPortalRef: () => {}
});

export const TopBarPortalProvider = ({ children }: { children: React.ReactNode}) => {
  const [topBarPortalRef, setTopBarPortalRef] = useState<HTMLElement | null>(null);
  const value: TopBarPortalContextType = {
    topBarPortalRef,
    setTopBarPortalRef
  }
  
  return (
    <TopBarPortalContext.Provider value={value}>
      {children}
    </TopBarPortalContext.Provider>
  );
}

export const useTopBarPortal = (): TopBarPortalContextType => {
    return useContext(TopBarPortalContext);
}
